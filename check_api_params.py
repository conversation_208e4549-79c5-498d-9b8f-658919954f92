#!/usr/bin/env python3
"""
豆包图像生成 - API参数兼容性检查

这个工具帮助检查哪些参数被API支持。
"""

import os
from volcenginesdkarkruntime import Ark


def test_parameter_support():
    """测试不同参数的支持情况"""
    print("🔍 检查API参数支持情况")
    print("=" * 50)
    
    api_key = os.environ.get("ARK_API_KEY")
    if not api_key:
        print("❌ 请先设置 ARK_API_KEY 环境变量")
        return
    
    client = Ark(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=api_key,
    )
    
    # 基础参数测试
    base_params = {
        "model": "doubao-seedream-3-0-t2i-250415",  # 需要替换为有效的接入点ID
        "prompt": "测试图像",
        "response_format": "url",
    }
    
    # 要测试的参数
    test_params = [
        ("size", "1024x1024"),
        ("seed", 42),
        ("guidance_scale", 7.5),
        ("watermark", True),
        ("n", 1),  # 可能不支持
        ("quality", "standard"),  # 可能不支持
        ("style", "vivid"),  # 可能不支持
    ]
    
    supported_params = []
    unsupported_params = []
    
    for param_name, param_value in test_params:
        print(f"\n🧪 测试参数: {param_name} = {param_value}")
        
        test_request = base_params.copy()
        test_request[param_name] = param_value
        
        try:
            # 尝试调用API（不会真正生成图像，只检查参数）
            response = client.images.generate(**test_request)
            print(f"✅ 参数 '{param_name}' 被支持")
            supported_params.append(param_name)
        except Exception as e:
            error_msg = str(e)
            if "unexpected keyword argument" in error_msg:
                print(f"❌ 参数 '{param_name}' 不被支持")
                unsupported_params.append(param_name)
            elif "invalid model specified" in error_msg:
                print(f"⚠️  参数 '{param_name}' 可能被支持（模型无效）")
                supported_params.append(param_name)
            else:
                print(f"❓ 参数 '{param_name}' 状态未知: {error_msg[:100]}...")
    
    # 输出总结
    print("\n" + "=" * 50)
    print("📊 参数支持情况总结")
    print("=" * 50)
    
    if supported_params:
        print("✅ 支持的参数:")
        for param in supported_params:
            print(f"   - {param}")
    
    if unsupported_params:
        print("\n❌ 不支持的参数:")
        for param in unsupported_params:
            print(f"   - {param}")
    
    # 生成推荐配置
    print("\n💡 推荐的参数配置:")
    recommended_config = {
        "size": "1024x1024",
        "response_format": "url",
        "watermark": True,
    }
    
    # 只包含支持的高级参数
    if "seed" in supported_params:
        recommended_config["seed"] = 42
    if "guidance_scale" in supported_params:
        recommended_config["guidance_scale"] = 7.5
    
    print("```python")
    print("params = {")
    for key, value in recommended_config.items():
        if isinstance(value, str):
            print(f'    "{key}": "{value}",')
        else:
            print(f'    "{key}": {value},')
    print("}")
    print("```")


def generate_curl_example():
    """生成对应的curl命令示例"""
    print("\n🌐 对应的curl命令示例:")
    print("=" * 50)
    
    curl_command = '''curl -X POST https://ark.cn-beijing.volces.com/api/v3/images/generations \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer $ARK_API_KEY" \\
  -d '{
    "model": "your-endpoint-id",
    "prompt": "一只可爱的猫咪",
    "response_format": "url",
    "size": "1024x1024",
    "seed": 42,
    "guidance_scale": 7.5,
    "watermark": true
  }' '''
    
    print(curl_command)
    print("\n💡 使用前请将 'your-endpoint-id' 替换为您的实际推理接入点ID")


def main():
    print("🔧 豆包图像生成 - API参数兼容性检查工具")
    print("=" * 60)
    
    # 检查参数支持
    test_parameter_support()
    
    # 生成curl示例
    generate_curl_example()
    
    print("\n📚 更多信息:")
    print("   - 官方API文档: https://www.volcengine.com/docs/82379/1541523")
    print("   - 修改 config.py 来调整默认参数")


if __name__ == "__main__":
    main()
