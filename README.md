# 豆包图像生成项目

使用豆包AI生成图像的Python项目，基于uv包管理器。

## 快速开始

### 1. 安装依赖
```bash
uv sync
```

### 2. 配置API密钥
在`.env`文件中设置：
```
ARK_API_KEY=your_api_key_here
```

### 3. 运行程序
```bash
uv run python main.py
```

## 参数配置

编辑 `config.py` 文件自定义参数：

```python
IMAGE_PARAMS = {
    "size": "1024x1024",        # 图像尺寸
    "watermark": False,         # 是否添加水印
    "guidance_scale": 7.5,      # 引导强度 (1.0-20.0)
    "seed": None,               # 随机种子
}
```

### 可用参数

| 参数 | 说明 | 可选值 |
|------|------|--------|
| `size` | 图像尺寸 | `512x512`, `768x768`, `1024x1024`, `1152x864`, `864x1152` |
| `guidance_scale` | 引导强度 | 1.0-20.0 (推荐7.5) |
| `seed` | 随机种子 | 数字(固定) 或 None(随机) |
| `watermark` | 水印 | true/false |

## 常用命令

```bash
# 添加依赖
uv add package_name

# 运行脚本
uv run python script.py

# 更新依赖
uv lock --upgrade
```

## 故障排除

- **API密钥错误**: 检查`.env`文件中的`ARK_API_KEY`
- **网络问题**: 确保可以访问火山引擎API
- **依赖问题**: 运行`uv sync --reinstall`重新安装

## 项目特点

- ✅ 使用uv进行现代化依赖管理
- ✅ 支持丰富的图像生成参数配置
- ✅ 简洁的代码结构，易于理解和修改
- ✅ 完整的错误处理和用户提示
