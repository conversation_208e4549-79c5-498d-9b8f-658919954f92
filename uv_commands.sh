#!/bin/bash

# 豆包图像生成项目 - uv常用命令示例

echo "=== 豆包图像生成项目 - uv管理命令 ==="
echo

echo "1. 查看uv版本:"
uv --version
echo

echo "2. 查看项目信息:"
uv info
echo

echo "3. 查看已安装的包:"
uv pip list
echo

echo "4. 查看依赖树:"
uv pip show volcengine-python-sdk
echo

echo "5. 运行主程序:"
echo "uv run python main.py"
echo

echo "6. 其他常用命令:"
echo "   - 添加依赖: uv add package_name"
echo "   - 移除依赖: uv remove package_name"
echo "   - 更新锁定文件: uv lock --upgrade"
echo "   - 同步依赖: uv sync"
echo "   - 重新安装: uv sync --reinstall"
echo "   - 清理缓存: uv cache clean"
echo

echo "7. 虚拟环境相关:"
echo "   - 激活虚拟环境: source .venv/bin/activate"
echo "   - 退出虚拟环境: deactivate"
echo

echo "项目已成功配置为使用uv进行依赖管理！"
