import os
# 通过 uv add 'volcengine-python-sdk[ark]' 安装方舟SDK
from volcenginesdkarkruntime import Ark


def main():
    # 请确保您已将 API Key 存储在环境变量 ARK_API_KEY 中
    # 初始化Ark客户端，从环境变量中读取您的API Key
    client = Ark(
        # 此为默认路径，您可根据业务所在地域进行配置
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        # 从环境变量中获取您的 API Key。此为默认方式，您可根据需要进行修改
        api_key=os.environ.get("ARK_API_KEY"),
    )

    try:
        print("正在生成图像，请稍候...")
        imagesResponse = client.images.generate(
            model="doubao-seedream-3-0-t2i-250415",  # 使用官方文档中的正确模型ID
            prompt="鱼眼镜头，一只猫咪的头部，画面呈现出猫咪的五官因为拍摄方式扭曲的效果。"
        )
        print("✅ 图像生成成功！")
        print("🖼️  生成的图像URL:", imagesResponse.data[0].url)
    except Exception as e:
        print(f"❌ 生成图像时出错: {e}")
        print("💡 请检查:")
        print("   1. API密钥是否正确")
        print("   2. 是否已在控制台开通该模型服务")
        print("   3. 网络连接是否正常")


if __name__ == "__main__":
    main()
