#!/usr/bin/env python3
"""
豆包图像生成 - 高级参数示例

这个文件展示了如何使用各种参数来控制图像生成的效果。
"""

import os
from volcenginesdkarkruntime import Ark


def example_basic_generation():
    """基础图像生成示例"""
    print("🎨 基础图像生成示例")
    print("=" * 40)
    
    client = Ark(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=os.environ.get("ARK_API_KEY"),
    )
    
    try:
        response = client.images.generate(
            model="your-endpoint-id",  # 替换为您的推理接入点ID
            prompt="一只可爱的橘猫坐在阳光下",
            size="1024x1024",
            response_format="url",
            watermark=True
        )
        
        print(f"✅ 生成成功: {response.data[0].url}")
    except Exception as e:
        print(f"❌ 生成失败: {e}")


def example_with_seed():
    """使用固定种子确保可重现的结果"""
    print("\n🌱 固定种子示例")
    print("=" * 40)
    
    client = Ark(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=os.environ.get("ARK_API_KEY"),
    )
    
    prompt = "未来城市的夜景，霓虹灯闪烁"
    seed = 12345  # 固定种子
    
    try:
        response = client.images.generate(
            model="your-endpoint-id",  # 替换为您的推理接入点ID
            prompt=prompt,
            size="1024x1024",
            seed=seed,
            response_format="url",
            watermark=True
        )
        
        print(f"📝 提示词: {prompt}")
        print(f"🌱 种子: {seed}")
        print(f"✅ 生成成功: {response.data[0].url}")
        print("💡 使用相同的种子和提示词会生成相同的图像")
    except Exception as e:
        print(f"❌ 生成失败: {e}")


def example_guidance_scale():
    """不同引导强度的对比"""
    print("\n🎯 引导强度对比示例")
    print("=" * 40)
    
    client = Ark(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=os.environ.get("ARK_API_KEY"),
    )
    
    prompt = "一朵盛开的红玫瑰，细节丰富"
    guidance_scales = [2.5, 7.5, 15.0]
    
    for scale in guidance_scales:
        try:
            response = client.images.generate(
                model="your-endpoint-id",  # 替换为您的推理接入点ID
                prompt=prompt,
                size="1024x1024",
                guidance_scale=scale,
                seed=42,  # 使用相同种子便于对比
                response_format="url",
                watermark=True
            )
            
            print(f"🎯 引导强度 {scale}: {response.data[0].url}")
        except Exception as e:
            print(f"❌ 引导强度 {scale} 失败: {e}")
    
    print("💡 引导强度说明:")
    print("   - 低值 (1.0-5.0): 更有创意，可能偏离提示词")
    print("   - 中值 (5.0-10.0): 平衡创意和准确性")
    print("   - 高值 (10.0-20.0): 严格遵循提示词，可能过度拟合")


def example_different_sizes():
    """不同尺寸的图像生成"""
    print("\n📐 不同尺寸示例")
    print("=" * 40)
    
    client = Ark(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=os.environ.get("ARK_API_KEY"),
    )
    
    prompt = "山水画风格的风景"
    sizes = ["512x512", "768x768", "1024x1024", "1152x864", "864x1152"]
    
    for size in sizes:
        try:
            response = client.images.generate(
                model="your-endpoint-id",  # 替换为您的推理接入点ID
                prompt=prompt,
                size=size,
                seed=123,  # 使用相同种子便于对比
                response_format="url",
                watermark=True
            )
            
            print(f"📐 尺寸 {size}: {response.data[0].url}")
        except Exception as e:
            print(f"❌ 尺寸 {size} 失败: {e}")
    
    print("💡 尺寸选择建议:")
    print("   - 512x512: 快速预览，节省成本")
    print("   - 1024x1024: 标准正方形，适合头像、图标")
    print("   - 1152x864: 横向构图，适合风景、横幅")
    print("   - 864x1152: 纵向构图，适合人像、海报")


def example_multiple_images():
    """一次生成多张图像"""
    print("\n🖼️  批量生成示例")
    print("=" * 40)
    
    client = Ark(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=os.environ.get("ARK_API_KEY"),
    )
    
    try:
        response = client.images.generate(
            model="your-endpoint-id",  # 替换为您的推理接入点ID
            prompt="抽象艺术，色彩丰富",
            size="1024x1024",
            n=4,  # 生成4张图像
            response_format="url",
            watermark=True
        )
        
        print(f"📝 提示词: 抽象艺术，色彩丰富")
        print(f"🔢 生成数量: {len(response.data)}")
        
        for i, image in enumerate(response.data, 1):
            print(f"🖼️  图像 {i}: {image.url}")
            
    except Exception as e:
        print(f"❌ 批量生成失败: {e}")
    
    print("💡 批量生成说明:")
    print("   - 可以一次生成1-4张图像")
    print("   - 每张图像都会有不同的随机变化")
    print("   - 费用按生成的图像数量计算")


def example_creative_prompts():
    """创意提示词示例"""
    print("\n✨ 创意提示词示例")
    print("=" * 40)
    
    creative_prompts = [
        "赛博朋克风格的猫咪，霓虹灯背景，高科技感",
        "水彩画风格，春天的樱花飘落，温柔的粉色调",
        "油画质感，梵高风格的星空下的小镇",
        "极简主义设计，几何图形组成的抽象鸟类",
        "蒸汽朋克机械装置，齿轮和蒸汽，复古未来感"
    ]
    
    print("💡 以下是一些创意提示词示例:")
    for i, prompt in enumerate(creative_prompts, 1):
        print(f"   {i}. {prompt}")
    
    print("\n🎨 提示词技巧:")
    print("   - 描述风格: 水彩画、油画、赛博朋克、极简主义等")
    print("   - 指定色调: 温暖色调、冷色调、单色、高对比度等")
    print("   - 添加情感: 宁静的、激动人心的、神秘的、温馨的等")
    print("   - 技术细节: 高分辨率、细节丰富、柔和光线、戏剧性光影等")


def main():
    """运行所有示例"""
    print("🚀 豆包图像生成 - 高级参数示例")
    print("=" * 50)
    
    # 检查API密钥
    if not os.environ.get("ARK_API_KEY"):
        print("❌ 请先设置 ARK_API_KEY 环境变量")
        return
    
    print("⚠️  注意: 请先将示例中的 'your-endpoint-id' 替换为您的实际推理接入点ID")
    print()
    
    # 运行示例（注释掉避免实际调用）
    # example_basic_generation()
    # example_with_seed()
    # example_guidance_scale()
    # example_different_sizes()
    # example_multiple_images()
    example_creative_prompts()
    
    print("\n📚 更多信息:")
    print("   - 修改 main.py 中的 get_user_preferences() 函数来自定义参数")
    print("   - 参考官方文档: https://www.volcengine.com/docs/82379/1541523")


if __name__ == "__main__":
    main()
