# coding: utf-8

"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyVpnGatewayAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asn': 'int',
        'bandwidth': 'int',
        'description': 'str',
        'ipsec_enabled': 'bool',
        'ssl_enabled': 'bool',
        'ssl_max_connections': 'int',
        'vpn_gateway_id': 'str',
        'vpn_gateway_name': 'str'
    }

    attribute_map = {
        'asn': 'Asn',
        'bandwidth': 'Bandwidth',
        'description': 'Description',
        'ipsec_enabled': 'IpsecEnabled',
        'ssl_enabled': 'SslEnabled',
        'ssl_max_connections': 'SslMaxConnections',
        'vpn_gateway_id': 'VpnGatewayId',
        'vpn_gateway_name': 'VpnGatewayName'
    }

    def __init__(self, asn=None, bandwidth=None, description=None, ipsec_enabled=None, ssl_enabled=None, ssl_max_connections=None, vpn_gateway_id=None, vpn_gateway_name=None, _configuration=None):  # noqa: E501
        """ModifyVpnGatewayAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asn = None
        self._bandwidth = None
        self._description = None
        self._ipsec_enabled = None
        self._ssl_enabled = None
        self._ssl_max_connections = None
        self._vpn_gateway_id = None
        self._vpn_gateway_name = None
        self.discriminator = None

        if asn is not None:
            self.asn = asn
        if bandwidth is not None:
            self.bandwidth = bandwidth
        if description is not None:
            self.description = description
        if ipsec_enabled is not None:
            self.ipsec_enabled = ipsec_enabled
        if ssl_enabled is not None:
            self.ssl_enabled = ssl_enabled
        if ssl_max_connections is not None:
            self.ssl_max_connections = ssl_max_connections
        self.vpn_gateway_id = vpn_gateway_id
        if vpn_gateway_name is not None:
            self.vpn_gateway_name = vpn_gateway_name

    @property
    def asn(self):
        """Gets the asn of this ModifyVpnGatewayAttributesRequest.  # noqa: E501


        :return: The asn of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :rtype: int
        """
        return self._asn

    @asn.setter
    def asn(self, asn):
        """Sets the asn of this ModifyVpnGatewayAttributesRequest.


        :param asn: The asn of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :type: int
        """

        self._asn = asn

    @property
    def bandwidth(self):
        """Gets the bandwidth of this ModifyVpnGatewayAttributesRequest.  # noqa: E501


        :return: The bandwidth of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this ModifyVpnGatewayAttributesRequest.


        :param bandwidth: The bandwidth of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def description(self):
        """Gets the description of this ModifyVpnGatewayAttributesRequest.  # noqa: E501


        :return: The description of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyVpnGatewayAttributesRequest.


        :param description: The description of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def ipsec_enabled(self):
        """Gets the ipsec_enabled of this ModifyVpnGatewayAttributesRequest.  # noqa: E501


        :return: The ipsec_enabled of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._ipsec_enabled

    @ipsec_enabled.setter
    def ipsec_enabled(self, ipsec_enabled):
        """Sets the ipsec_enabled of this ModifyVpnGatewayAttributesRequest.


        :param ipsec_enabled: The ipsec_enabled of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :type: bool
        """

        self._ipsec_enabled = ipsec_enabled

    @property
    def ssl_enabled(self):
        """Gets the ssl_enabled of this ModifyVpnGatewayAttributesRequest.  # noqa: E501


        :return: The ssl_enabled of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._ssl_enabled

    @ssl_enabled.setter
    def ssl_enabled(self, ssl_enabled):
        """Sets the ssl_enabled of this ModifyVpnGatewayAttributesRequest.


        :param ssl_enabled: The ssl_enabled of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :type: bool
        """

        self._ssl_enabled = ssl_enabled

    @property
    def ssl_max_connections(self):
        """Gets the ssl_max_connections of this ModifyVpnGatewayAttributesRequest.  # noqa: E501


        :return: The ssl_max_connections of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :rtype: int
        """
        return self._ssl_max_connections

    @ssl_max_connections.setter
    def ssl_max_connections(self, ssl_max_connections):
        """Sets the ssl_max_connections of this ModifyVpnGatewayAttributesRequest.


        :param ssl_max_connections: The ssl_max_connections of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :type: int
        """

        self._ssl_max_connections = ssl_max_connections

    @property
    def vpn_gateway_id(self):
        """Gets the vpn_gateway_id of this ModifyVpnGatewayAttributesRequest.  # noqa: E501


        :return: The vpn_gateway_id of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpn_gateway_id

    @vpn_gateway_id.setter
    def vpn_gateway_id(self, vpn_gateway_id):
        """Sets the vpn_gateway_id of this ModifyVpnGatewayAttributesRequest.


        :param vpn_gateway_id: The vpn_gateway_id of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpn_gateway_id is None:
            raise ValueError("Invalid value for `vpn_gateway_id`, must not be `None`")  # noqa: E501

        self._vpn_gateway_id = vpn_gateway_id

    @property
    def vpn_gateway_name(self):
        """Gets the vpn_gateway_name of this ModifyVpnGatewayAttributesRequest.  # noqa: E501


        :return: The vpn_gateway_name of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpn_gateway_name

    @vpn_gateway_name.setter
    def vpn_gateway_name(self, vpn_gateway_name):
        """Sets the vpn_gateway_name of this ModifyVpnGatewayAttributesRequest.


        :param vpn_gateway_name: The vpn_gateway_name of this ModifyVpnGatewayAttributesRequest.  # noqa: E501
        :type: str
        """

        self._vpn_gateway_name = vpn_gateway_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyVpnGatewayAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyVpnGatewayAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyVpnGatewayAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
