# coding: utf-8

"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateVpnGatewayRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asn': 'int',
        'bandwidth': 'int',
        'billing_type': 'int',
        'client_token': 'str',
        'description': 'str',
        'dual_tunnel_enabled': 'bool',
        'ipsec_enabled': 'bool',
        'period': 'int',
        'period_unit': 'str',
        'project_name': 'str',
        'secondary_subnet_id': 'str',
        'ssl_enabled': 'bool',
        'ssl_max_connections': 'int',
        'subnet_id': 'str',
        'tags': 'list[TagForCreateVpnGatewayInput]',
        'vpc_id': 'str',
        'vpn_gateway_name': 'str'
    }

    attribute_map = {
        'asn': 'Asn',
        'bandwidth': 'Bandwidth',
        'billing_type': 'BillingType',
        'client_token': 'ClientToken',
        'description': 'Description',
        'dual_tunnel_enabled': 'DualTunnelEnabled',
        'ipsec_enabled': 'IpsecEnabled',
        'period': 'Period',
        'period_unit': 'PeriodUnit',
        'project_name': 'ProjectName',
        'secondary_subnet_id': 'SecondarySubnetId',
        'ssl_enabled': 'SslEnabled',
        'ssl_max_connections': 'SslMaxConnections',
        'subnet_id': 'SubnetId',
        'tags': 'Tags',
        'vpc_id': 'VpcId',
        'vpn_gateway_name': 'VpnGatewayName'
    }

    def __init__(self, asn=None, bandwidth=None, billing_type=None, client_token=None, description=None, dual_tunnel_enabled=None, ipsec_enabled=None, period=None, period_unit=None, project_name=None, secondary_subnet_id=None, ssl_enabled=None, ssl_max_connections=None, subnet_id=None, tags=None, vpc_id=None, vpn_gateway_name=None, _configuration=None):  # noqa: E501
        """CreateVpnGatewayRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asn = None
        self._bandwidth = None
        self._billing_type = None
        self._client_token = None
        self._description = None
        self._dual_tunnel_enabled = None
        self._ipsec_enabled = None
        self._period = None
        self._period_unit = None
        self._project_name = None
        self._secondary_subnet_id = None
        self._ssl_enabled = None
        self._ssl_max_connections = None
        self._subnet_id = None
        self._tags = None
        self._vpc_id = None
        self._vpn_gateway_name = None
        self.discriminator = None

        if asn is not None:
            self.asn = asn
        self.bandwidth = bandwidth
        if billing_type is not None:
            self.billing_type = billing_type
        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        if dual_tunnel_enabled is not None:
            self.dual_tunnel_enabled = dual_tunnel_enabled
        if ipsec_enabled is not None:
            self.ipsec_enabled = ipsec_enabled
        if period is not None:
            self.period = period
        if period_unit is not None:
            self.period_unit = period_unit
        if project_name is not None:
            self.project_name = project_name
        if secondary_subnet_id is not None:
            self.secondary_subnet_id = secondary_subnet_id
        if ssl_enabled is not None:
            self.ssl_enabled = ssl_enabled
        if ssl_max_connections is not None:
            self.ssl_max_connections = ssl_max_connections
        self.subnet_id = subnet_id
        if tags is not None:
            self.tags = tags
        self.vpc_id = vpc_id
        if vpn_gateway_name is not None:
            self.vpn_gateway_name = vpn_gateway_name

    @property
    def asn(self):
        """Gets the asn of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The asn of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: int
        """
        return self._asn

    @asn.setter
    def asn(self, asn):
        """Sets the asn of this CreateVpnGatewayRequest.


        :param asn: The asn of this CreateVpnGatewayRequest.  # noqa: E501
        :type: int
        """

        self._asn = asn

    @property
    def bandwidth(self):
        """Gets the bandwidth of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The bandwidth of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this CreateVpnGatewayRequest.


        :param bandwidth: The bandwidth of this CreateVpnGatewayRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and bandwidth is None:
            raise ValueError("Invalid value for `bandwidth`, must not be `None`")  # noqa: E501

        self._bandwidth = bandwidth

    @property
    def billing_type(self):
        """Gets the billing_type of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The billing_type of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: int
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this CreateVpnGatewayRequest.


        :param billing_type: The billing_type of this CreateVpnGatewayRequest.  # noqa: E501
        :type: int
        """

        self._billing_type = billing_type

    @property
    def client_token(self):
        """Gets the client_token of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The client_token of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateVpnGatewayRequest.


        :param client_token: The client_token of this CreateVpnGatewayRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The description of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateVpnGatewayRequest.


        :param description: The description of this CreateVpnGatewayRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dual_tunnel_enabled(self):
        """Gets the dual_tunnel_enabled of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The dual_tunnel_enabled of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: bool
        """
        return self._dual_tunnel_enabled

    @dual_tunnel_enabled.setter
    def dual_tunnel_enabled(self, dual_tunnel_enabled):
        """Sets the dual_tunnel_enabled of this CreateVpnGatewayRequest.


        :param dual_tunnel_enabled: The dual_tunnel_enabled of this CreateVpnGatewayRequest.  # noqa: E501
        :type: bool
        """

        self._dual_tunnel_enabled = dual_tunnel_enabled

    @property
    def ipsec_enabled(self):
        """Gets the ipsec_enabled of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The ipsec_enabled of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: bool
        """
        return self._ipsec_enabled

    @ipsec_enabled.setter
    def ipsec_enabled(self, ipsec_enabled):
        """Sets the ipsec_enabled of this CreateVpnGatewayRequest.


        :param ipsec_enabled: The ipsec_enabled of this CreateVpnGatewayRequest.  # noqa: E501
        :type: bool
        """

        self._ipsec_enabled = ipsec_enabled

    @property
    def period(self):
        """Gets the period of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The period of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: int
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this CreateVpnGatewayRequest.


        :param period: The period of this CreateVpnGatewayRequest.  # noqa: E501
        :type: int
        """

        self._period = period

    @property
    def period_unit(self):
        """Gets the period_unit of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The period_unit of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._period_unit

    @period_unit.setter
    def period_unit(self, period_unit):
        """Sets the period_unit of this CreateVpnGatewayRequest.


        :param period_unit: The period_unit of this CreateVpnGatewayRequest.  # noqa: E501
        :type: str
        """

        self._period_unit = period_unit

    @property
    def project_name(self):
        """Gets the project_name of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The project_name of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateVpnGatewayRequest.


        :param project_name: The project_name of this CreateVpnGatewayRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def secondary_subnet_id(self):
        """Gets the secondary_subnet_id of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The secondary_subnet_id of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._secondary_subnet_id

    @secondary_subnet_id.setter
    def secondary_subnet_id(self, secondary_subnet_id):
        """Sets the secondary_subnet_id of this CreateVpnGatewayRequest.


        :param secondary_subnet_id: The secondary_subnet_id of this CreateVpnGatewayRequest.  # noqa: E501
        :type: str
        """

        self._secondary_subnet_id = secondary_subnet_id

    @property
    def ssl_enabled(self):
        """Gets the ssl_enabled of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The ssl_enabled of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: bool
        """
        return self._ssl_enabled

    @ssl_enabled.setter
    def ssl_enabled(self, ssl_enabled):
        """Sets the ssl_enabled of this CreateVpnGatewayRequest.


        :param ssl_enabled: The ssl_enabled of this CreateVpnGatewayRequest.  # noqa: E501
        :type: bool
        """

        self._ssl_enabled = ssl_enabled

    @property
    def ssl_max_connections(self):
        """Gets the ssl_max_connections of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The ssl_max_connections of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: int
        """
        return self._ssl_max_connections

    @ssl_max_connections.setter
    def ssl_max_connections(self, ssl_max_connections):
        """Sets the ssl_max_connections of this CreateVpnGatewayRequest.


        :param ssl_max_connections: The ssl_max_connections of this CreateVpnGatewayRequest.  # noqa: E501
        :type: int
        """

        self._ssl_max_connections = ssl_max_connections

    @property
    def subnet_id(self):
        """Gets the subnet_id of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The subnet_id of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this CreateVpnGatewayRequest.


        :param subnet_id: The subnet_id of this CreateVpnGatewayRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and subnet_id is None:
            raise ValueError("Invalid value for `subnet_id`, must not be `None`")  # noqa: E501

        self._subnet_id = subnet_id

    @property
    def tags(self):
        """Gets the tags of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The tags of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: list[TagForCreateVpnGatewayInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateVpnGatewayRequest.


        :param tags: The tags of this CreateVpnGatewayRequest.  # noqa: E501
        :type: list[TagForCreateVpnGatewayInput]
        """

        self._tags = tags

    @property
    def vpc_id(self):
        """Gets the vpc_id of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The vpc_id of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this CreateVpnGatewayRequest.


        :param vpc_id: The vpc_id of this CreateVpnGatewayRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_id is None:
            raise ValueError("Invalid value for `vpc_id`, must not be `None`")  # noqa: E501

        self._vpc_id = vpc_id

    @property
    def vpn_gateway_name(self):
        """Gets the vpn_gateway_name of this CreateVpnGatewayRequest.  # noqa: E501


        :return: The vpn_gateway_name of this CreateVpnGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpn_gateway_name

    @vpn_gateway_name.setter
    def vpn_gateway_name(self, vpn_gateway_name):
        """Sets the vpn_gateway_name of this CreateVpnGatewayRequest.


        :param vpn_gateway_name: The vpn_gateway_name of this CreateVpnGatewayRequest.  # noqa: E501
        :type: str
        """

        self._vpn_gateway_name = vpn_gateway_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateVpnGatewayRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateVpnGatewayRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateVpnGatewayRequest):
            return True

        return self.to_dict() != other.to_dict()
