# coding: utf-8

"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifySslVpnServerRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auth': 'str',
        'cipher': 'str',
        'client_ip_pool': 'str',
        'compress': 'bool',
        'description': 'str',
        'local_subnets': 'list[str]',
        'port': 'int',
        'protocol': 'str',
        'ssl_vpn_server_id': 'str',
        'ssl_vpn_server_name': 'str'
    }

    attribute_map = {
        'auth': 'Auth',
        'cipher': 'Cipher',
        'client_ip_pool': 'ClientIpPool',
        'compress': 'Compress',
        'description': 'Description',
        'local_subnets': 'LocalSubnets',
        'port': 'Port',
        'protocol': 'Protocol',
        'ssl_vpn_server_id': 'SslVpnServerId',
        'ssl_vpn_server_name': 'SslVpnServerName'
    }

    def __init__(self, auth=None, cipher=None, client_ip_pool=None, compress=None, description=None, local_subnets=None, port=None, protocol=None, ssl_vpn_server_id=None, ssl_vpn_server_name=None, _configuration=None):  # noqa: E501
        """ModifySslVpnServerRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auth = None
        self._cipher = None
        self._client_ip_pool = None
        self._compress = None
        self._description = None
        self._local_subnets = None
        self._port = None
        self._protocol = None
        self._ssl_vpn_server_id = None
        self._ssl_vpn_server_name = None
        self.discriminator = None

        if auth is not None:
            self.auth = auth
        if cipher is not None:
            self.cipher = cipher
        if client_ip_pool is not None:
            self.client_ip_pool = client_ip_pool
        if compress is not None:
            self.compress = compress
        if description is not None:
            self.description = description
        if local_subnets is not None:
            self.local_subnets = local_subnets
        if port is not None:
            self.port = port
        if protocol is not None:
            self.protocol = protocol
        self.ssl_vpn_server_id = ssl_vpn_server_id
        if ssl_vpn_server_name is not None:
            self.ssl_vpn_server_name = ssl_vpn_server_name

    @property
    def auth(self):
        """Gets the auth of this ModifySslVpnServerRequest.  # noqa: E501


        :return: The auth of this ModifySslVpnServerRequest.  # noqa: E501
        :rtype: str
        """
        return self._auth

    @auth.setter
    def auth(self, auth):
        """Sets the auth of this ModifySslVpnServerRequest.


        :param auth: The auth of this ModifySslVpnServerRequest.  # noqa: E501
        :type: str
        """

        self._auth = auth

    @property
    def cipher(self):
        """Gets the cipher of this ModifySslVpnServerRequest.  # noqa: E501


        :return: The cipher of this ModifySslVpnServerRequest.  # noqa: E501
        :rtype: str
        """
        return self._cipher

    @cipher.setter
    def cipher(self, cipher):
        """Sets the cipher of this ModifySslVpnServerRequest.


        :param cipher: The cipher of this ModifySslVpnServerRequest.  # noqa: E501
        :type: str
        """

        self._cipher = cipher

    @property
    def client_ip_pool(self):
        """Gets the client_ip_pool of this ModifySslVpnServerRequest.  # noqa: E501


        :return: The client_ip_pool of this ModifySslVpnServerRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_ip_pool

    @client_ip_pool.setter
    def client_ip_pool(self, client_ip_pool):
        """Sets the client_ip_pool of this ModifySslVpnServerRequest.


        :param client_ip_pool: The client_ip_pool of this ModifySslVpnServerRequest.  # noqa: E501
        :type: str
        """

        self._client_ip_pool = client_ip_pool

    @property
    def compress(self):
        """Gets the compress of this ModifySslVpnServerRequest.  # noqa: E501


        :return: The compress of this ModifySslVpnServerRequest.  # noqa: E501
        :rtype: bool
        """
        return self._compress

    @compress.setter
    def compress(self, compress):
        """Sets the compress of this ModifySslVpnServerRequest.


        :param compress: The compress of this ModifySslVpnServerRequest.  # noqa: E501
        :type: bool
        """

        self._compress = compress

    @property
    def description(self):
        """Gets the description of this ModifySslVpnServerRequest.  # noqa: E501


        :return: The description of this ModifySslVpnServerRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifySslVpnServerRequest.


        :param description: The description of this ModifySslVpnServerRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def local_subnets(self):
        """Gets the local_subnets of this ModifySslVpnServerRequest.  # noqa: E501


        :return: The local_subnets of this ModifySslVpnServerRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._local_subnets

    @local_subnets.setter
    def local_subnets(self, local_subnets):
        """Sets the local_subnets of this ModifySslVpnServerRequest.


        :param local_subnets: The local_subnets of this ModifySslVpnServerRequest.  # noqa: E501
        :type: list[str]
        """

        self._local_subnets = local_subnets

    @property
    def port(self):
        """Gets the port of this ModifySslVpnServerRequest.  # noqa: E501


        :return: The port of this ModifySslVpnServerRequest.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this ModifySslVpnServerRequest.


        :param port: The port of this ModifySslVpnServerRequest.  # noqa: E501
        :type: int
        """

        self._port = port

    @property
    def protocol(self):
        """Gets the protocol of this ModifySslVpnServerRequest.  # noqa: E501


        :return: The protocol of this ModifySslVpnServerRequest.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this ModifySslVpnServerRequest.


        :param protocol: The protocol of this ModifySslVpnServerRequest.  # noqa: E501
        :type: str
        """

        self._protocol = protocol

    @property
    def ssl_vpn_server_id(self):
        """Gets the ssl_vpn_server_id of this ModifySslVpnServerRequest.  # noqa: E501


        :return: The ssl_vpn_server_id of this ModifySslVpnServerRequest.  # noqa: E501
        :rtype: str
        """
        return self._ssl_vpn_server_id

    @ssl_vpn_server_id.setter
    def ssl_vpn_server_id(self, ssl_vpn_server_id):
        """Sets the ssl_vpn_server_id of this ModifySslVpnServerRequest.


        :param ssl_vpn_server_id: The ssl_vpn_server_id of this ModifySslVpnServerRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ssl_vpn_server_id is None:
            raise ValueError("Invalid value for `ssl_vpn_server_id`, must not be `None`")  # noqa: E501

        self._ssl_vpn_server_id = ssl_vpn_server_id

    @property
    def ssl_vpn_server_name(self):
        """Gets the ssl_vpn_server_name of this ModifySslVpnServerRequest.  # noqa: E501


        :return: The ssl_vpn_server_name of this ModifySslVpnServerRequest.  # noqa: E501
        :rtype: str
        """
        return self._ssl_vpn_server_name

    @ssl_vpn_server_name.setter
    def ssl_vpn_server_name(self, ssl_vpn_server_name):
        """Sets the ssl_vpn_server_name of this ModifySslVpnServerRequest.


        :param ssl_vpn_server_name: The ssl_vpn_server_name of this ModifySslVpnServerRequest.  # noqa: E501
        :type: str
        """

        self._ssl_vpn_server_name = ssl_vpn_server_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifySslVpnServerRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifySslVpnServerRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifySslVpnServerRequest):
            return True

        return self.to_dict() != other.to_dict()
