# coding: utf-8

"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CustomerGatewayForDescribeCustomerGatewaysOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'asn': 'int',
        'connection_count': 'int',
        'creation_time': 'str',
        'customer_gateway_id': 'str',
        'customer_gateway_name': 'str',
        'description': 'str',
        'ip_address': 'str',
        'project_name': 'str',
        'status': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'asn': 'Asn',
        'connection_count': 'ConnectionCount',
        'creation_time': 'CreationTime',
        'customer_gateway_id': 'CustomerGatewayId',
        'customer_gateway_name': 'CustomerGatewayName',
        'description': 'Description',
        'ip_address': 'IpAddress',
        'project_name': 'ProjectName',
        'status': 'Status',
        'update_time': 'UpdateTime'
    }

    def __init__(self, account_id=None, asn=None, connection_count=None, creation_time=None, customer_gateway_id=None, customer_gateway_name=None, description=None, ip_address=None, project_name=None, status=None, update_time=None, _configuration=None):  # noqa: E501
        """CustomerGatewayForDescribeCustomerGatewaysOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._asn = None
        self._connection_count = None
        self._creation_time = None
        self._customer_gateway_id = None
        self._customer_gateway_name = None
        self._description = None
        self._ip_address = None
        self._project_name = None
        self._status = None
        self._update_time = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if asn is not None:
            self.asn = asn
        if connection_count is not None:
            self.connection_count = connection_count
        if creation_time is not None:
            self.creation_time = creation_time
        if customer_gateway_id is not None:
            self.customer_gateway_id = customer_gateway_id
        if customer_gateway_name is not None:
            self.customer_gateway_name = customer_gateway_name
        if description is not None:
            self.description = description
        if ip_address is not None:
            self.ip_address = ip_address
        if project_name is not None:
            self.project_name = project_name
        if status is not None:
            self.status = status
        if update_time is not None:
            self.update_time = update_time

    @property
    def account_id(self):
        """Gets the account_id of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501


        :return: The account_id of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this CustomerGatewayForDescribeCustomerGatewaysOutput.


        :param account_id: The account_id of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def asn(self):
        """Gets the asn of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501


        :return: The asn of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :rtype: int
        """
        return self._asn

    @asn.setter
    def asn(self, asn):
        """Sets the asn of this CustomerGatewayForDescribeCustomerGatewaysOutput.


        :param asn: The asn of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :type: int
        """

        self._asn = asn

    @property
    def connection_count(self):
        """Gets the connection_count of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501


        :return: The connection_count of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :rtype: int
        """
        return self._connection_count

    @connection_count.setter
    def connection_count(self, connection_count):
        """Sets the connection_count of this CustomerGatewayForDescribeCustomerGatewaysOutput.


        :param connection_count: The connection_count of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :type: int
        """

        self._connection_count = connection_count

    @property
    def creation_time(self):
        """Gets the creation_time of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501


        :return: The creation_time of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this CustomerGatewayForDescribeCustomerGatewaysOutput.


        :param creation_time: The creation_time of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def customer_gateway_id(self):
        """Gets the customer_gateway_id of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501


        :return: The customer_gateway_id of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._customer_gateway_id

    @customer_gateway_id.setter
    def customer_gateway_id(self, customer_gateway_id):
        """Sets the customer_gateway_id of this CustomerGatewayForDescribeCustomerGatewaysOutput.


        :param customer_gateway_id: The customer_gateway_id of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._customer_gateway_id = customer_gateway_id

    @property
    def customer_gateway_name(self):
        """Gets the customer_gateway_name of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501


        :return: The customer_gateway_name of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._customer_gateway_name

    @customer_gateway_name.setter
    def customer_gateway_name(self, customer_gateway_name):
        """Sets the customer_gateway_name of this CustomerGatewayForDescribeCustomerGatewaysOutput.


        :param customer_gateway_name: The customer_gateway_name of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._customer_gateway_name = customer_gateway_name

    @property
    def description(self):
        """Gets the description of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501


        :return: The description of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CustomerGatewayForDescribeCustomerGatewaysOutput.


        :param description: The description of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def ip_address(self):
        """Gets the ip_address of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501


        :return: The ip_address of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip_address

    @ip_address.setter
    def ip_address(self, ip_address):
        """Sets the ip_address of this CustomerGatewayForDescribeCustomerGatewaysOutput.


        :param ip_address: The ip_address of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._ip_address = ip_address

    @property
    def project_name(self):
        """Gets the project_name of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501


        :return: The project_name of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CustomerGatewayForDescribeCustomerGatewaysOutput.


        :param project_name: The project_name of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def status(self):
        """Gets the status of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501


        :return: The status of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this CustomerGatewayForDescribeCustomerGatewaysOutput.


        :param status: The status of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def update_time(self):
        """Gets the update_time of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501


        :return: The update_time of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this CustomerGatewayForDescribeCustomerGatewaysOutput.


        :param update_time: The update_time of this CustomerGatewayForDescribeCustomerGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CustomerGatewayForDescribeCustomerGatewaysOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CustomerGatewayForDescribeCustomerGatewaysOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CustomerGatewayForDescribeCustomerGatewaysOutput):
            return True

        return self.to_dict() != other.to_dict()
