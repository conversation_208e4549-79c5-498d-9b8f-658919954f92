# coding: utf-8

"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeSslVpnServersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'ssl_vpn_server_ids': 'list[str]',
        'ssl_vpn_server_name': 'str',
        'tag_filters': 'list[TagFilterForDescribeSslVpnServersInput]',
        'vpn_gateway_id': 'str'
    }

    attribute_map = {
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'ssl_vpn_server_ids': 'SslVpnServerIds',
        'ssl_vpn_server_name': 'SslVpnServerName',
        'tag_filters': 'TagFilters',
        'vpn_gateway_id': 'VpnGatewayId'
    }

    def __init__(self, page_number=None, page_size=None, project_name=None, ssl_vpn_server_ids=None, ssl_vpn_server_name=None, tag_filters=None, vpn_gateway_id=None, _configuration=None):  # noqa: E501
        """DescribeSslVpnServersRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._ssl_vpn_server_ids = None
        self._ssl_vpn_server_name = None
        self._tag_filters = None
        self._vpn_gateway_id = None
        self.discriminator = None

        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if ssl_vpn_server_ids is not None:
            self.ssl_vpn_server_ids = ssl_vpn_server_ids
        if ssl_vpn_server_name is not None:
            self.ssl_vpn_server_name = ssl_vpn_server_name
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if vpn_gateway_id is not None:
            self.vpn_gateway_id = vpn_gateway_id

    @property
    def page_number(self):
        """Gets the page_number of this DescribeSslVpnServersRequest.  # noqa: E501


        :return: The page_number of this DescribeSslVpnServersRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeSslVpnServersRequest.


        :param page_number: The page_number of this DescribeSslVpnServersRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeSslVpnServersRequest.  # noqa: E501


        :return: The page_size of this DescribeSslVpnServersRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeSslVpnServersRequest.


        :param page_size: The page_size of this DescribeSslVpnServersRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeSslVpnServersRequest.  # noqa: E501


        :return: The project_name of this DescribeSslVpnServersRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeSslVpnServersRequest.


        :param project_name: The project_name of this DescribeSslVpnServersRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def ssl_vpn_server_ids(self):
        """Gets the ssl_vpn_server_ids of this DescribeSslVpnServersRequest.  # noqa: E501


        :return: The ssl_vpn_server_ids of this DescribeSslVpnServersRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ssl_vpn_server_ids

    @ssl_vpn_server_ids.setter
    def ssl_vpn_server_ids(self, ssl_vpn_server_ids):
        """Sets the ssl_vpn_server_ids of this DescribeSslVpnServersRequest.


        :param ssl_vpn_server_ids: The ssl_vpn_server_ids of this DescribeSslVpnServersRequest.  # noqa: E501
        :type: list[str]
        """

        self._ssl_vpn_server_ids = ssl_vpn_server_ids

    @property
    def ssl_vpn_server_name(self):
        """Gets the ssl_vpn_server_name of this DescribeSslVpnServersRequest.  # noqa: E501


        :return: The ssl_vpn_server_name of this DescribeSslVpnServersRequest.  # noqa: E501
        :rtype: str
        """
        return self._ssl_vpn_server_name

    @ssl_vpn_server_name.setter
    def ssl_vpn_server_name(self, ssl_vpn_server_name):
        """Sets the ssl_vpn_server_name of this DescribeSslVpnServersRequest.


        :param ssl_vpn_server_name: The ssl_vpn_server_name of this DescribeSslVpnServersRequest.  # noqa: E501
        :type: str
        """

        self._ssl_vpn_server_name = ssl_vpn_server_name

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeSslVpnServersRequest.  # noqa: E501


        :return: The tag_filters of this DescribeSslVpnServersRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeSslVpnServersInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeSslVpnServersRequest.


        :param tag_filters: The tag_filters of this DescribeSslVpnServersRequest.  # noqa: E501
        :type: list[TagFilterForDescribeSslVpnServersInput]
        """

        self._tag_filters = tag_filters

    @property
    def vpn_gateway_id(self):
        """Gets the vpn_gateway_id of this DescribeSslVpnServersRequest.  # noqa: E501


        :return: The vpn_gateway_id of this DescribeSslVpnServersRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpn_gateway_id

    @vpn_gateway_id.setter
    def vpn_gateway_id(self, vpn_gateway_id):
        """Sets the vpn_gateway_id of this DescribeSslVpnServersRequest.


        :param vpn_gateway_id: The vpn_gateway_id of this DescribeSslVpnServersRequest.  # noqa: E501
        :type: str
        """

        self._vpn_gateway_id = vpn_gateway_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeSslVpnServersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeSslVpnServersRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeSslVpnServersRequest):
            return True

        return self.to_dict() != other.to_dict()
