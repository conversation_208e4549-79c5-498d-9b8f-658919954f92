# coding: utf-8

"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeVpnConnectionsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'attach_status': 'str',
        'attach_type': 'str',
        'customer_gateway_id': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'spec': 'str',
        'status': 'str',
        'tag_filters': 'list[TagFilterForDescribeVpnConnectionsInput]',
        'transit_router_id': 'str',
        'vpn_connection_ids': 'list[str]',
        'vpn_connection_name': 'str',
        'vpn_gateway_id': 'str'
    }

    attribute_map = {
        'attach_status': 'AttachStatus',
        'attach_type': 'AttachType',
        'customer_gateway_id': 'CustomerGatewayId',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'spec': 'Spec',
        'status': 'Status',
        'tag_filters': 'TagFilters',
        'transit_router_id': 'TransitRouterId',
        'vpn_connection_ids': 'VpnConnectionIds',
        'vpn_connection_name': 'VpnConnectionName',
        'vpn_gateway_id': 'VpnGatewayId'
    }

    def __init__(self, attach_status=None, attach_type=None, customer_gateway_id=None, page_number=None, page_size=None, project_name=None, spec=None, status=None, tag_filters=None, transit_router_id=None, vpn_connection_ids=None, vpn_connection_name=None, vpn_gateway_id=None, _configuration=None):  # noqa: E501
        """DescribeVpnConnectionsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._attach_status = None
        self._attach_type = None
        self._customer_gateway_id = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._spec = None
        self._status = None
        self._tag_filters = None
        self._transit_router_id = None
        self._vpn_connection_ids = None
        self._vpn_connection_name = None
        self._vpn_gateway_id = None
        self.discriminator = None

        if attach_status is not None:
            self.attach_status = attach_status
        if attach_type is not None:
            self.attach_type = attach_type
        if customer_gateway_id is not None:
            self.customer_gateway_id = customer_gateway_id
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if spec is not None:
            self.spec = spec
        if status is not None:
            self.status = status
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if transit_router_id is not None:
            self.transit_router_id = transit_router_id
        if vpn_connection_ids is not None:
            self.vpn_connection_ids = vpn_connection_ids
        if vpn_connection_name is not None:
            self.vpn_connection_name = vpn_connection_name
        if vpn_gateway_id is not None:
            self.vpn_gateway_id = vpn_gateway_id

    @property
    def attach_status(self):
        """Gets the attach_status of this DescribeVpnConnectionsRequest.  # noqa: E501


        :return: The attach_status of this DescribeVpnConnectionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._attach_status

    @attach_status.setter
    def attach_status(self, attach_status):
        """Sets the attach_status of this DescribeVpnConnectionsRequest.


        :param attach_status: The attach_status of this DescribeVpnConnectionsRequest.  # noqa: E501
        :type: str
        """

        self._attach_status = attach_status

    @property
    def attach_type(self):
        """Gets the attach_type of this DescribeVpnConnectionsRequest.  # noqa: E501


        :return: The attach_type of this DescribeVpnConnectionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._attach_type

    @attach_type.setter
    def attach_type(self, attach_type):
        """Sets the attach_type of this DescribeVpnConnectionsRequest.


        :param attach_type: The attach_type of this DescribeVpnConnectionsRequest.  # noqa: E501
        :type: str
        """

        self._attach_type = attach_type

    @property
    def customer_gateway_id(self):
        """Gets the customer_gateway_id of this DescribeVpnConnectionsRequest.  # noqa: E501


        :return: The customer_gateway_id of this DescribeVpnConnectionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._customer_gateway_id

    @customer_gateway_id.setter
    def customer_gateway_id(self, customer_gateway_id):
        """Sets the customer_gateway_id of this DescribeVpnConnectionsRequest.


        :param customer_gateway_id: The customer_gateway_id of this DescribeVpnConnectionsRequest.  # noqa: E501
        :type: str
        """

        self._customer_gateway_id = customer_gateway_id

    @property
    def page_number(self):
        """Gets the page_number of this DescribeVpnConnectionsRequest.  # noqa: E501


        :return: The page_number of this DescribeVpnConnectionsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeVpnConnectionsRequest.


        :param page_number: The page_number of this DescribeVpnConnectionsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeVpnConnectionsRequest.  # noqa: E501


        :return: The page_size of this DescribeVpnConnectionsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeVpnConnectionsRequest.


        :param page_size: The page_size of this DescribeVpnConnectionsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeVpnConnectionsRequest.  # noqa: E501


        :return: The project_name of this DescribeVpnConnectionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeVpnConnectionsRequest.


        :param project_name: The project_name of this DescribeVpnConnectionsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def spec(self):
        """Gets the spec of this DescribeVpnConnectionsRequest.  # noqa: E501


        :return: The spec of this DescribeVpnConnectionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._spec

    @spec.setter
    def spec(self, spec):
        """Sets the spec of this DescribeVpnConnectionsRequest.


        :param spec: The spec of this DescribeVpnConnectionsRequest.  # noqa: E501
        :type: str
        """

        self._spec = spec

    @property
    def status(self):
        """Gets the status of this DescribeVpnConnectionsRequest.  # noqa: E501


        :return: The status of this DescribeVpnConnectionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeVpnConnectionsRequest.


        :param status: The status of this DescribeVpnConnectionsRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeVpnConnectionsRequest.  # noqa: E501


        :return: The tag_filters of this DescribeVpnConnectionsRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeVpnConnectionsInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeVpnConnectionsRequest.


        :param tag_filters: The tag_filters of this DescribeVpnConnectionsRequest.  # noqa: E501
        :type: list[TagFilterForDescribeVpnConnectionsInput]
        """

        self._tag_filters = tag_filters

    @property
    def transit_router_id(self):
        """Gets the transit_router_id of this DescribeVpnConnectionsRequest.  # noqa: E501


        :return: The transit_router_id of this DescribeVpnConnectionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_id

    @transit_router_id.setter
    def transit_router_id(self, transit_router_id):
        """Sets the transit_router_id of this DescribeVpnConnectionsRequest.


        :param transit_router_id: The transit_router_id of this DescribeVpnConnectionsRequest.  # noqa: E501
        :type: str
        """

        self._transit_router_id = transit_router_id

    @property
    def vpn_connection_ids(self):
        """Gets the vpn_connection_ids of this DescribeVpnConnectionsRequest.  # noqa: E501


        :return: The vpn_connection_ids of this DescribeVpnConnectionsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vpn_connection_ids

    @vpn_connection_ids.setter
    def vpn_connection_ids(self, vpn_connection_ids):
        """Sets the vpn_connection_ids of this DescribeVpnConnectionsRequest.


        :param vpn_connection_ids: The vpn_connection_ids of this DescribeVpnConnectionsRequest.  # noqa: E501
        :type: list[str]
        """

        self._vpn_connection_ids = vpn_connection_ids

    @property
    def vpn_connection_name(self):
        """Gets the vpn_connection_name of this DescribeVpnConnectionsRequest.  # noqa: E501


        :return: The vpn_connection_name of this DescribeVpnConnectionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpn_connection_name

    @vpn_connection_name.setter
    def vpn_connection_name(self, vpn_connection_name):
        """Sets the vpn_connection_name of this DescribeVpnConnectionsRequest.


        :param vpn_connection_name: The vpn_connection_name of this DescribeVpnConnectionsRequest.  # noqa: E501
        :type: str
        """

        self._vpn_connection_name = vpn_connection_name

    @property
    def vpn_gateway_id(self):
        """Gets the vpn_gateway_id of this DescribeVpnConnectionsRequest.  # noqa: E501


        :return: The vpn_gateway_id of this DescribeVpnConnectionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpn_gateway_id

    @vpn_gateway_id.setter
    def vpn_gateway_id(self, vpn_gateway_id):
        """Sets the vpn_gateway_id of this DescribeVpnConnectionsRequest.


        :param vpn_gateway_id: The vpn_gateway_id of this DescribeVpnConnectionsRequest.  # noqa: E501
        :type: str
        """

        self._vpn_gateway_id = vpn_gateway_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeVpnConnectionsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeVpnConnectionsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeVpnConnectionsRequest):
            return True

        return self.to_dict() != other.to_dict()
