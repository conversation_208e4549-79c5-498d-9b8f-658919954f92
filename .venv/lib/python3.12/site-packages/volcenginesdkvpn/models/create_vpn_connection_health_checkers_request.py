# coding: utf-8

"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateVpnConnectionHealthCheckersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'health_check_configs': 'list[HealthCheckConfigForCreateVpnConnectionHealthCheckersInput]',
        'vpn_connection_id': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'health_check_configs': 'HealthCheckConfigs',
        'vpn_connection_id': 'VpnConnectionId'
    }

    def __init__(self, client_token=None, health_check_configs=None, vpn_connection_id=None, _configuration=None):  # noqa: E501
        """CreateVpnConnectionHealthCheckersRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._health_check_configs = None
        self._vpn_connection_id = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if health_check_configs is not None:
            self.health_check_configs = health_check_configs
        self.vpn_connection_id = vpn_connection_id

    @property
    def client_token(self):
        """Gets the client_token of this CreateVpnConnectionHealthCheckersRequest.  # noqa: E501


        :return: The client_token of this CreateVpnConnectionHealthCheckersRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateVpnConnectionHealthCheckersRequest.


        :param client_token: The client_token of this CreateVpnConnectionHealthCheckersRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def health_check_configs(self):
        """Gets the health_check_configs of this CreateVpnConnectionHealthCheckersRequest.  # noqa: E501


        :return: The health_check_configs of this CreateVpnConnectionHealthCheckersRequest.  # noqa: E501
        :rtype: list[HealthCheckConfigForCreateVpnConnectionHealthCheckersInput]
        """
        return self._health_check_configs

    @health_check_configs.setter
    def health_check_configs(self, health_check_configs):
        """Sets the health_check_configs of this CreateVpnConnectionHealthCheckersRequest.


        :param health_check_configs: The health_check_configs of this CreateVpnConnectionHealthCheckersRequest.  # noqa: E501
        :type: list[HealthCheckConfigForCreateVpnConnectionHealthCheckersInput]
        """

        self._health_check_configs = health_check_configs

    @property
    def vpn_connection_id(self):
        """Gets the vpn_connection_id of this CreateVpnConnectionHealthCheckersRequest.  # noqa: E501


        :return: The vpn_connection_id of this CreateVpnConnectionHealthCheckersRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpn_connection_id

    @vpn_connection_id.setter
    def vpn_connection_id(self, vpn_connection_id):
        """Sets the vpn_connection_id of this CreateVpnConnectionHealthCheckersRequest.


        :param vpn_connection_id: The vpn_connection_id of this CreateVpnConnectionHealthCheckersRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpn_connection_id is None:
            raise ValueError("Invalid value for `vpn_connection_id`, must not be `None`")  # noqa: E501

        self._vpn_connection_id = vpn_connection_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateVpnConnectionHealthCheckersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateVpnConnectionHealthCheckersRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateVpnConnectionHealthCheckersRequest):
            return True

        return self.to_dict() != other.to_dict()
