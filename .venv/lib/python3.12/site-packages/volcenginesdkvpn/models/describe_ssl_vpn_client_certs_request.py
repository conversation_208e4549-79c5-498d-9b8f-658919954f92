# coding: utf-8

"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeSslVpnClientCertsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'page_number': 'int',
        'page_size': 'int',
        'ssl_vpn_client_cert_ids': 'list[str]',
        'ssl_vpn_client_cert_name': 'str',
        'ssl_vpn_server_id': 'str',
        'tag_filters': 'list[TagFilterForDescribeSslVpnClientCertsInput]'
    }

    attribute_map = {
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'ssl_vpn_client_cert_ids': 'SslVpnClientCertIds',
        'ssl_vpn_client_cert_name': 'SslVpnClientCertName',
        'ssl_vpn_server_id': 'SslVpnServerId',
        'tag_filters': 'TagFilters'
    }

    def __init__(self, page_number=None, page_size=None, ssl_vpn_client_cert_ids=None, ssl_vpn_client_cert_name=None, ssl_vpn_server_id=None, tag_filters=None, _configuration=None):  # noqa: E501
        """DescribeSslVpnClientCertsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._page_number = None
        self._page_size = None
        self._ssl_vpn_client_cert_ids = None
        self._ssl_vpn_client_cert_name = None
        self._ssl_vpn_server_id = None
        self._tag_filters = None
        self.discriminator = None

        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if ssl_vpn_client_cert_ids is not None:
            self.ssl_vpn_client_cert_ids = ssl_vpn_client_cert_ids
        if ssl_vpn_client_cert_name is not None:
            self.ssl_vpn_client_cert_name = ssl_vpn_client_cert_name
        if ssl_vpn_server_id is not None:
            self.ssl_vpn_server_id = ssl_vpn_server_id
        if tag_filters is not None:
            self.tag_filters = tag_filters

    @property
    def page_number(self):
        """Gets the page_number of this DescribeSslVpnClientCertsRequest.  # noqa: E501


        :return: The page_number of this DescribeSslVpnClientCertsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeSslVpnClientCertsRequest.


        :param page_number: The page_number of this DescribeSslVpnClientCertsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeSslVpnClientCertsRequest.  # noqa: E501


        :return: The page_size of this DescribeSslVpnClientCertsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeSslVpnClientCertsRequest.


        :param page_size: The page_size of this DescribeSslVpnClientCertsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def ssl_vpn_client_cert_ids(self):
        """Gets the ssl_vpn_client_cert_ids of this DescribeSslVpnClientCertsRequest.  # noqa: E501


        :return: The ssl_vpn_client_cert_ids of this DescribeSslVpnClientCertsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ssl_vpn_client_cert_ids

    @ssl_vpn_client_cert_ids.setter
    def ssl_vpn_client_cert_ids(self, ssl_vpn_client_cert_ids):
        """Sets the ssl_vpn_client_cert_ids of this DescribeSslVpnClientCertsRequest.


        :param ssl_vpn_client_cert_ids: The ssl_vpn_client_cert_ids of this DescribeSslVpnClientCertsRequest.  # noqa: E501
        :type: list[str]
        """

        self._ssl_vpn_client_cert_ids = ssl_vpn_client_cert_ids

    @property
    def ssl_vpn_client_cert_name(self):
        """Gets the ssl_vpn_client_cert_name of this DescribeSslVpnClientCertsRequest.  # noqa: E501


        :return: The ssl_vpn_client_cert_name of this DescribeSslVpnClientCertsRequest.  # noqa: E501
        :rtype: str
        """
        return self._ssl_vpn_client_cert_name

    @ssl_vpn_client_cert_name.setter
    def ssl_vpn_client_cert_name(self, ssl_vpn_client_cert_name):
        """Sets the ssl_vpn_client_cert_name of this DescribeSslVpnClientCertsRequest.


        :param ssl_vpn_client_cert_name: The ssl_vpn_client_cert_name of this DescribeSslVpnClientCertsRequest.  # noqa: E501
        :type: str
        """

        self._ssl_vpn_client_cert_name = ssl_vpn_client_cert_name

    @property
    def ssl_vpn_server_id(self):
        """Gets the ssl_vpn_server_id of this DescribeSslVpnClientCertsRequest.  # noqa: E501


        :return: The ssl_vpn_server_id of this DescribeSslVpnClientCertsRequest.  # noqa: E501
        :rtype: str
        """
        return self._ssl_vpn_server_id

    @ssl_vpn_server_id.setter
    def ssl_vpn_server_id(self, ssl_vpn_server_id):
        """Sets the ssl_vpn_server_id of this DescribeSslVpnClientCertsRequest.


        :param ssl_vpn_server_id: The ssl_vpn_server_id of this DescribeSslVpnClientCertsRequest.  # noqa: E501
        :type: str
        """

        self._ssl_vpn_server_id = ssl_vpn_server_id

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeSslVpnClientCertsRequest.  # noqa: E501


        :return: The tag_filters of this DescribeSslVpnClientCertsRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeSslVpnClientCertsInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeSslVpnClientCertsRequest.


        :param tag_filters: The tag_filters of this DescribeSslVpnClientCertsRequest.  # noqa: E501
        :type: list[TagFilterForDescribeSslVpnClientCertsInput]
        """

        self._tag_filters = tag_filters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeSslVpnClientCertsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeSslVpnClientCertsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeSslVpnClientCertsRequest):
            return True

        return self.to_dict() != other.to_dict()
