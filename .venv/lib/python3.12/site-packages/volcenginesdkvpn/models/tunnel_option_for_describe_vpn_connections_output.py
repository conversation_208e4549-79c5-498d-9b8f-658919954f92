# coding: utf-8

"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TunnelOptionForDescribeVpnConnectionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'connect_status': 'str',
        'customer_gateway_id': 'str',
        'dpd_action': 'str',
        'ike_config': 'IkeConfigForDescribeVpnConnectionsOutput',
        'ipsec_config': 'IpsecConfigForDescribeVpnConnectionsOutput',
        'nat_traversal': 'bool',
        'role': 'str',
        'tunnel_id': 'str'
    }

    attribute_map = {
        'connect_status': 'ConnectStatus',
        'customer_gateway_id': 'CustomerGatewayId',
        'dpd_action': 'DpdAction',
        'ike_config': 'IkeConfig',
        'ipsec_config': 'IpsecConfig',
        'nat_traversal': 'NatTraversal',
        'role': 'Role',
        'tunnel_id': 'TunnelId'
    }

    def __init__(self, connect_status=None, customer_gateway_id=None, dpd_action=None, ike_config=None, ipsec_config=None, nat_traversal=None, role=None, tunnel_id=None, _configuration=None):  # noqa: E501
        """TunnelOptionForDescribeVpnConnectionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._connect_status = None
        self._customer_gateway_id = None
        self._dpd_action = None
        self._ike_config = None
        self._ipsec_config = None
        self._nat_traversal = None
        self._role = None
        self._tunnel_id = None
        self.discriminator = None

        if connect_status is not None:
            self.connect_status = connect_status
        if customer_gateway_id is not None:
            self.customer_gateway_id = customer_gateway_id
        if dpd_action is not None:
            self.dpd_action = dpd_action
        if ike_config is not None:
            self.ike_config = ike_config
        if ipsec_config is not None:
            self.ipsec_config = ipsec_config
        if nat_traversal is not None:
            self.nat_traversal = nat_traversal
        if role is not None:
            self.role = role
        if tunnel_id is not None:
            self.tunnel_id = tunnel_id

    @property
    def connect_status(self):
        """Gets the connect_status of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The connect_status of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._connect_status

    @connect_status.setter
    def connect_status(self, connect_status):
        """Sets the connect_status of this TunnelOptionForDescribeVpnConnectionsOutput.


        :param connect_status: The connect_status of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._connect_status = connect_status

    @property
    def customer_gateway_id(self):
        """Gets the customer_gateway_id of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The customer_gateway_id of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._customer_gateway_id

    @customer_gateway_id.setter
    def customer_gateway_id(self, customer_gateway_id):
        """Sets the customer_gateway_id of this TunnelOptionForDescribeVpnConnectionsOutput.


        :param customer_gateway_id: The customer_gateway_id of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._customer_gateway_id = customer_gateway_id

    @property
    def dpd_action(self):
        """Gets the dpd_action of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The dpd_action of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._dpd_action

    @dpd_action.setter
    def dpd_action(self, dpd_action):
        """Sets the dpd_action of this TunnelOptionForDescribeVpnConnectionsOutput.


        :param dpd_action: The dpd_action of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._dpd_action = dpd_action

    @property
    def ike_config(self):
        """Gets the ike_config of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The ike_config of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: IkeConfigForDescribeVpnConnectionsOutput
        """
        return self._ike_config

    @ike_config.setter
    def ike_config(self, ike_config):
        """Sets the ike_config of this TunnelOptionForDescribeVpnConnectionsOutput.


        :param ike_config: The ike_config of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: IkeConfigForDescribeVpnConnectionsOutput
        """

        self._ike_config = ike_config

    @property
    def ipsec_config(self):
        """Gets the ipsec_config of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The ipsec_config of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: IpsecConfigForDescribeVpnConnectionsOutput
        """
        return self._ipsec_config

    @ipsec_config.setter
    def ipsec_config(self, ipsec_config):
        """Sets the ipsec_config of this TunnelOptionForDescribeVpnConnectionsOutput.


        :param ipsec_config: The ipsec_config of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: IpsecConfigForDescribeVpnConnectionsOutput
        """

        self._ipsec_config = ipsec_config

    @property
    def nat_traversal(self):
        """Gets the nat_traversal of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The nat_traversal of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._nat_traversal

    @nat_traversal.setter
    def nat_traversal(self, nat_traversal):
        """Sets the nat_traversal of this TunnelOptionForDescribeVpnConnectionsOutput.


        :param nat_traversal: The nat_traversal of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: bool
        """

        self._nat_traversal = nat_traversal

    @property
    def role(self):
        """Gets the role of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The role of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._role

    @role.setter
    def role(self, role):
        """Sets the role of this TunnelOptionForDescribeVpnConnectionsOutput.


        :param role: The role of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._role = role

    @property
    def tunnel_id(self):
        """Gets the tunnel_id of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The tunnel_id of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._tunnel_id

    @tunnel_id.setter
    def tunnel_id(self, tunnel_id):
        """Sets the tunnel_id of this TunnelOptionForDescribeVpnConnectionsOutput.


        :param tunnel_id: The tunnel_id of this TunnelOptionForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._tunnel_id = tunnel_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TunnelOptionForDescribeVpnConnectionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TunnelOptionForDescribeVpnConnectionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TunnelOptionForDescribeVpnConnectionsOutput):
            return True

        return self.to_dict() != other.to_dict()
