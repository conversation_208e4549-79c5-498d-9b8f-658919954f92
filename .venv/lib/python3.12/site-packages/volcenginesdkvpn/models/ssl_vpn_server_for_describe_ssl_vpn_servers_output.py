# coding: utf-8

"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SslVpnServerForDescribeSslVpnServersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auth': 'str',
        'cipher': 'str',
        'client_ip_pool': 'str',
        'compress': 'bool',
        'creation_time': 'str',
        'description': 'str',
        'is_blocked': 'bool',
        'local_subnets': 'list[str]',
        'port': 'int',
        'project_name': 'str',
        'protocol': 'str',
        'ssl_vpn_server_id': 'str',
        'ssl_vpn_server_name': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribeSslVpnServersOutput]',
        'update_time': 'str',
        'vpn_gateway_id': 'str'
    }

    attribute_map = {
        'auth': 'Auth',
        'cipher': 'Cipher',
        'client_ip_pool': 'ClientIpPool',
        'compress': 'Compress',
        'creation_time': 'CreationTime',
        'description': 'Description',
        'is_blocked': 'IsBlocked',
        'local_subnets': 'LocalSubnets',
        'port': 'Port',
        'project_name': 'ProjectName',
        'protocol': 'Protocol',
        'ssl_vpn_server_id': 'SslVpnServerId',
        'ssl_vpn_server_name': 'SslVpnServerName',
        'status': 'Status',
        'tags': 'Tags',
        'update_time': 'UpdateTime',
        'vpn_gateway_id': 'VpnGatewayId'
    }

    def __init__(self, auth=None, cipher=None, client_ip_pool=None, compress=None, creation_time=None, description=None, is_blocked=None, local_subnets=None, port=None, project_name=None, protocol=None, ssl_vpn_server_id=None, ssl_vpn_server_name=None, status=None, tags=None, update_time=None, vpn_gateway_id=None, _configuration=None):  # noqa: E501
        """SslVpnServerForDescribeSslVpnServersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auth = None
        self._cipher = None
        self._client_ip_pool = None
        self._compress = None
        self._creation_time = None
        self._description = None
        self._is_blocked = None
        self._local_subnets = None
        self._port = None
        self._project_name = None
        self._protocol = None
        self._ssl_vpn_server_id = None
        self._ssl_vpn_server_name = None
        self._status = None
        self._tags = None
        self._update_time = None
        self._vpn_gateway_id = None
        self.discriminator = None

        if auth is not None:
            self.auth = auth
        if cipher is not None:
            self.cipher = cipher
        if client_ip_pool is not None:
            self.client_ip_pool = client_ip_pool
        if compress is not None:
            self.compress = compress
        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if is_blocked is not None:
            self.is_blocked = is_blocked
        if local_subnets is not None:
            self.local_subnets = local_subnets
        if port is not None:
            self.port = port
        if project_name is not None:
            self.project_name = project_name
        if protocol is not None:
            self.protocol = protocol
        if ssl_vpn_server_id is not None:
            self.ssl_vpn_server_id = ssl_vpn_server_id
        if ssl_vpn_server_name is not None:
            self.ssl_vpn_server_name = ssl_vpn_server_name
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time
        if vpn_gateway_id is not None:
            self.vpn_gateway_id = vpn_gateway_id

    @property
    def auth(self):
        """Gets the auth of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The auth of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._auth

    @auth.setter
    def auth(self, auth):
        """Sets the auth of this SslVpnServerForDescribeSslVpnServersOutput.


        :param auth: The auth of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: str
        """

        self._auth = auth

    @property
    def cipher(self):
        """Gets the cipher of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The cipher of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._cipher

    @cipher.setter
    def cipher(self, cipher):
        """Sets the cipher of this SslVpnServerForDescribeSslVpnServersOutput.


        :param cipher: The cipher of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: str
        """

        self._cipher = cipher

    @property
    def client_ip_pool(self):
        """Gets the client_ip_pool of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The client_ip_pool of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._client_ip_pool

    @client_ip_pool.setter
    def client_ip_pool(self, client_ip_pool):
        """Sets the client_ip_pool of this SslVpnServerForDescribeSslVpnServersOutput.


        :param client_ip_pool: The client_ip_pool of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: str
        """

        self._client_ip_pool = client_ip_pool

    @property
    def compress(self):
        """Gets the compress of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The compress of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._compress

    @compress.setter
    def compress(self, compress):
        """Sets the compress of this SslVpnServerForDescribeSslVpnServersOutput.


        :param compress: The compress of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: bool
        """

        self._compress = compress

    @property
    def creation_time(self):
        """Gets the creation_time of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The creation_time of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this SslVpnServerForDescribeSslVpnServersOutput.


        :param creation_time: The creation_time of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The description of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this SslVpnServerForDescribeSslVpnServersOutput.


        :param description: The description of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def is_blocked(self):
        """Gets the is_blocked of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The is_blocked of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_blocked

    @is_blocked.setter
    def is_blocked(self, is_blocked):
        """Sets the is_blocked of this SslVpnServerForDescribeSslVpnServersOutput.


        :param is_blocked: The is_blocked of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: bool
        """

        self._is_blocked = is_blocked

    @property
    def local_subnets(self):
        """Gets the local_subnets of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The local_subnets of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._local_subnets

    @local_subnets.setter
    def local_subnets(self, local_subnets):
        """Sets the local_subnets of this SslVpnServerForDescribeSslVpnServersOutput.


        :param local_subnets: The local_subnets of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: list[str]
        """

        self._local_subnets = local_subnets

    @property
    def port(self):
        """Gets the port of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The port of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this SslVpnServerForDescribeSslVpnServersOutput.


        :param port: The port of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: int
        """

        self._port = port

    @property
    def project_name(self):
        """Gets the project_name of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The project_name of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this SslVpnServerForDescribeSslVpnServersOutput.


        :param project_name: The project_name of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def protocol(self):
        """Gets the protocol of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The protocol of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this SslVpnServerForDescribeSslVpnServersOutput.


        :param protocol: The protocol of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: str
        """

        self._protocol = protocol

    @property
    def ssl_vpn_server_id(self):
        """Gets the ssl_vpn_server_id of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The ssl_vpn_server_id of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._ssl_vpn_server_id

    @ssl_vpn_server_id.setter
    def ssl_vpn_server_id(self, ssl_vpn_server_id):
        """Sets the ssl_vpn_server_id of this SslVpnServerForDescribeSslVpnServersOutput.


        :param ssl_vpn_server_id: The ssl_vpn_server_id of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: str
        """

        self._ssl_vpn_server_id = ssl_vpn_server_id

    @property
    def ssl_vpn_server_name(self):
        """Gets the ssl_vpn_server_name of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The ssl_vpn_server_name of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._ssl_vpn_server_name

    @ssl_vpn_server_name.setter
    def ssl_vpn_server_name(self, ssl_vpn_server_name):
        """Sets the ssl_vpn_server_name of this SslVpnServerForDescribeSslVpnServersOutput.


        :param ssl_vpn_server_name: The ssl_vpn_server_name of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: str
        """

        self._ssl_vpn_server_name = ssl_vpn_server_name

    @property
    def status(self):
        """Gets the status of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The status of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this SslVpnServerForDescribeSslVpnServersOutput.


        :param status: The status of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The tags of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: list[TagForDescribeSslVpnServersOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this SslVpnServerForDescribeSslVpnServersOutput.


        :param tags: The tags of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: list[TagForDescribeSslVpnServersOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The update_time of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this SslVpnServerForDescribeSslVpnServersOutput.


        :param update_time: The update_time of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vpn_gateway_id(self):
        """Gets the vpn_gateway_id of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501


        :return: The vpn_gateway_id of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpn_gateway_id

    @vpn_gateway_id.setter
    def vpn_gateway_id(self, vpn_gateway_id):
        """Sets the vpn_gateway_id of this SslVpnServerForDescribeSslVpnServersOutput.


        :param vpn_gateway_id: The vpn_gateway_id of this SslVpnServerForDescribeSslVpnServersOutput.  # noqa: E501
        :type: str
        """

        self._vpn_gateway_id = vpn_gateway_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SslVpnServerForDescribeSslVpnServersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SslVpnServerForDescribeSslVpnServersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SslVpnServerForDescribeSslVpnServersOutput):
            return True

        return self.to_dict() != other.to_dict()
