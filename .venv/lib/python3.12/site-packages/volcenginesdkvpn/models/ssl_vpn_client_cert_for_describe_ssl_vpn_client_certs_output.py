# coding: utf-8

"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SslVpnClientCertForDescribeSslVpnClientCertsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'certificate_status': 'str',
        'creation_time': 'str',
        'description': 'str',
        'expired_time': 'str',
        'ssl_vpn_client_cert_id': 'str',
        'ssl_vpn_client_cert_name': 'str',
        'ssl_vpn_server_id': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribeSslVpnClientCertsOutput]',
        'update_time': 'str'
    }

    attribute_map = {
        'certificate_status': 'CertificateStatus',
        'creation_time': 'CreationTime',
        'description': 'Description',
        'expired_time': 'ExpiredTime',
        'ssl_vpn_client_cert_id': 'SslVpnClientCertId',
        'ssl_vpn_client_cert_name': 'SslVpnClientCertName',
        'ssl_vpn_server_id': 'SslVpnServerId',
        'status': 'Status',
        'tags': 'Tags',
        'update_time': 'UpdateTime'
    }

    def __init__(self, certificate_status=None, creation_time=None, description=None, expired_time=None, ssl_vpn_client_cert_id=None, ssl_vpn_client_cert_name=None, ssl_vpn_server_id=None, status=None, tags=None, update_time=None, _configuration=None):  # noqa: E501
        """SslVpnClientCertForDescribeSslVpnClientCertsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._certificate_status = None
        self._creation_time = None
        self._description = None
        self._expired_time = None
        self._ssl_vpn_client_cert_id = None
        self._ssl_vpn_client_cert_name = None
        self._ssl_vpn_server_id = None
        self._status = None
        self._tags = None
        self._update_time = None
        self.discriminator = None

        if certificate_status is not None:
            self.certificate_status = certificate_status
        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if expired_time is not None:
            self.expired_time = expired_time
        if ssl_vpn_client_cert_id is not None:
            self.ssl_vpn_client_cert_id = ssl_vpn_client_cert_id
        if ssl_vpn_client_cert_name is not None:
            self.ssl_vpn_client_cert_name = ssl_vpn_client_cert_name
        if ssl_vpn_server_id is not None:
            self.ssl_vpn_server_id = ssl_vpn_server_id
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time

    @property
    def certificate_status(self):
        """Gets the certificate_status of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501


        :return: The certificate_status of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :rtype: str
        """
        return self._certificate_status

    @certificate_status.setter
    def certificate_status(self, certificate_status):
        """Sets the certificate_status of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.


        :param certificate_status: The certificate_status of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :type: str
        """

        self._certificate_status = certificate_status

    @property
    def creation_time(self):
        """Gets the creation_time of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501


        :return: The creation_time of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.


        :param creation_time: The creation_time of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501


        :return: The description of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.


        :param description: The description of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def expired_time(self):
        """Gets the expired_time of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501


        :return: The expired_time of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :rtype: str
        """
        return self._expired_time

    @expired_time.setter
    def expired_time(self, expired_time):
        """Sets the expired_time of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.


        :param expired_time: The expired_time of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :type: str
        """

        self._expired_time = expired_time

    @property
    def ssl_vpn_client_cert_id(self):
        """Gets the ssl_vpn_client_cert_id of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501


        :return: The ssl_vpn_client_cert_id of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :rtype: str
        """
        return self._ssl_vpn_client_cert_id

    @ssl_vpn_client_cert_id.setter
    def ssl_vpn_client_cert_id(self, ssl_vpn_client_cert_id):
        """Sets the ssl_vpn_client_cert_id of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.


        :param ssl_vpn_client_cert_id: The ssl_vpn_client_cert_id of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :type: str
        """

        self._ssl_vpn_client_cert_id = ssl_vpn_client_cert_id

    @property
    def ssl_vpn_client_cert_name(self):
        """Gets the ssl_vpn_client_cert_name of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501


        :return: The ssl_vpn_client_cert_name of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :rtype: str
        """
        return self._ssl_vpn_client_cert_name

    @ssl_vpn_client_cert_name.setter
    def ssl_vpn_client_cert_name(self, ssl_vpn_client_cert_name):
        """Sets the ssl_vpn_client_cert_name of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.


        :param ssl_vpn_client_cert_name: The ssl_vpn_client_cert_name of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :type: str
        """

        self._ssl_vpn_client_cert_name = ssl_vpn_client_cert_name

    @property
    def ssl_vpn_server_id(self):
        """Gets the ssl_vpn_server_id of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501


        :return: The ssl_vpn_server_id of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :rtype: str
        """
        return self._ssl_vpn_server_id

    @ssl_vpn_server_id.setter
    def ssl_vpn_server_id(self, ssl_vpn_server_id):
        """Sets the ssl_vpn_server_id of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.


        :param ssl_vpn_server_id: The ssl_vpn_server_id of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :type: str
        """

        self._ssl_vpn_server_id = ssl_vpn_server_id

    @property
    def status(self):
        """Gets the status of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501


        :return: The status of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.


        :param status: The status of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501


        :return: The tags of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :rtype: list[TagForDescribeSslVpnClientCertsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.


        :param tags: The tags of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :type: list[TagForDescribeSslVpnClientCertsOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501


        :return: The update_time of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.


        :param update_time: The update_time of this SslVpnClientCertForDescribeSslVpnClientCertsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SslVpnClientCertForDescribeSslVpnClientCertsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SslVpnClientCertForDescribeSslVpnClientCertsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SslVpnClientCertForDescribeSslVpnClientCertsOutput):
            return True

        return self.to_dict() != other.to_dict()
