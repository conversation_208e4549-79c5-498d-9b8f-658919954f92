# coding: utf-8

"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HealthCheckerForDescribeVpnConnectionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'check_interval': 'int',
        'check_result': 'str',
        'checker_id': 'str',
        'down_time': 'int',
        'local_ip': 'str',
        'remote_ip': 'str',
        'timeout': 'int',
        'up_time': 'int'
    }

    attribute_map = {
        'check_interval': 'CheckInterval',
        'check_result': 'CheckResult',
        'checker_id': 'CheckerId',
        'down_time': 'DownTime',
        'local_ip': 'LocalIp',
        'remote_ip': 'RemoteIp',
        'timeout': 'Timeout',
        'up_time': 'UpTime'
    }

    def __init__(self, check_interval=None, check_result=None, checker_id=None, down_time=None, local_ip=None, remote_ip=None, timeout=None, up_time=None, _configuration=None):  # noqa: E501
        """HealthCheckerForDescribeVpnConnectionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._check_interval = None
        self._check_result = None
        self._checker_id = None
        self._down_time = None
        self._local_ip = None
        self._remote_ip = None
        self._timeout = None
        self._up_time = None
        self.discriminator = None

        if check_interval is not None:
            self.check_interval = check_interval
        if check_result is not None:
            self.check_result = check_result
        if checker_id is not None:
            self.checker_id = checker_id
        if down_time is not None:
            self.down_time = down_time
        if local_ip is not None:
            self.local_ip = local_ip
        if remote_ip is not None:
            self.remote_ip = remote_ip
        if timeout is not None:
            self.timeout = timeout
        if up_time is not None:
            self.up_time = up_time

    @property
    def check_interval(self):
        """Gets the check_interval of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The check_interval of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._check_interval

    @check_interval.setter
    def check_interval(self, check_interval):
        """Sets the check_interval of this HealthCheckerForDescribeVpnConnectionsOutput.


        :param check_interval: The check_interval of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: int
        """

        self._check_interval = check_interval

    @property
    def check_result(self):
        """Gets the check_result of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The check_result of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._check_result

    @check_result.setter
    def check_result(self, check_result):
        """Sets the check_result of this HealthCheckerForDescribeVpnConnectionsOutput.


        :param check_result: The check_result of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._check_result = check_result

    @property
    def checker_id(self):
        """Gets the checker_id of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The checker_id of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._checker_id

    @checker_id.setter
    def checker_id(self, checker_id):
        """Sets the checker_id of this HealthCheckerForDescribeVpnConnectionsOutput.


        :param checker_id: The checker_id of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._checker_id = checker_id

    @property
    def down_time(self):
        """Gets the down_time of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The down_time of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._down_time

    @down_time.setter
    def down_time(self, down_time):
        """Sets the down_time of this HealthCheckerForDescribeVpnConnectionsOutput.


        :param down_time: The down_time of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: int
        """

        self._down_time = down_time

    @property
    def local_ip(self):
        """Gets the local_ip of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The local_ip of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._local_ip

    @local_ip.setter
    def local_ip(self, local_ip):
        """Sets the local_ip of this HealthCheckerForDescribeVpnConnectionsOutput.


        :param local_ip: The local_ip of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._local_ip = local_ip

    @property
    def remote_ip(self):
        """Gets the remote_ip of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The remote_ip of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._remote_ip

    @remote_ip.setter
    def remote_ip(self, remote_ip):
        """Sets the remote_ip of this HealthCheckerForDescribeVpnConnectionsOutput.


        :param remote_ip: The remote_ip of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._remote_ip = remote_ip

    @property
    def timeout(self):
        """Gets the timeout of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The timeout of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this HealthCheckerForDescribeVpnConnectionsOutput.


        :param timeout: The timeout of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: int
        """

        self._timeout = timeout

    @property
    def up_time(self):
        """Gets the up_time of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501


        :return: The up_time of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._up_time

    @up_time.setter
    def up_time(self, up_time):
        """Sets the up_time of this HealthCheckerForDescribeVpnConnectionsOutput.


        :param up_time: The up_time of this HealthCheckerForDescribeVpnConnectionsOutput.  # noqa: E501
        :type: int
        """

        self._up_time = up_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HealthCheckerForDescribeVpnConnectionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HealthCheckerForDescribeVpnConnectionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HealthCheckerForDescribeVpnConnectionsOutput):
            return True

        return self.to_dict() != other.to_dict()
