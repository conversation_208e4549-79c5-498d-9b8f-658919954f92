# coding: utf-8

# flake8: noqa
"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdkvpn.models.as_path_for_describe_vpn_gateway_route_attributes_output import AsPathForDescribeVpnGatewayRouteAttributesOutput
from volcenginesdkvpn.models.as_path_for_describe_vpn_gateway_routes_output import AsPathForDescribeVpnGatewayRoutesOutput
from volcenginesdkvpn.models.bgp_config_for_create_vpn_connection_input import BgpConfigForCreateVpnConnectionInput
from volcenginesdkvpn.models.bgp_config_for_modify_vpn_connection_attributes_input import BgpConfigForModifyVpnConnectionAttributesInput
from volcenginesdkvpn.models.bgp_info_for_describe_vpn_connection_attributes_output import BgpInfoForDescribeVpnConnectionAttributesOutput
from volcenginesdkvpn.models.bgp_info_for_describe_vpn_connections_output import BgpInfoForDescribeVpnConnectionsOutput
from volcenginesdkvpn.models.create_customer_gateway_request import CreateCustomerGatewayRequest
from volcenginesdkvpn.models.create_customer_gateway_response import CreateCustomerGatewayResponse
from volcenginesdkvpn.models.create_ssl_vpn_client_cert_request import CreateSslVpnClientCertRequest
from volcenginesdkvpn.models.create_ssl_vpn_client_cert_response import CreateSslVpnClientCertResponse
from volcenginesdkvpn.models.create_ssl_vpn_server_request import CreateSslVpnServerRequest
from volcenginesdkvpn.models.create_ssl_vpn_server_response import CreateSslVpnServerResponse
from volcenginesdkvpn.models.create_vpn_connection_health_checkers_request import CreateVpnConnectionHealthCheckersRequest
from volcenginesdkvpn.models.create_vpn_connection_health_checkers_response import CreateVpnConnectionHealthCheckersResponse
from volcenginesdkvpn.models.create_vpn_connection_request import CreateVpnConnectionRequest
from volcenginesdkvpn.models.create_vpn_connection_response import CreateVpnConnectionResponse
from volcenginesdkvpn.models.create_vpn_gateway_request import CreateVpnGatewayRequest
from volcenginesdkvpn.models.create_vpn_gateway_response import CreateVpnGatewayResponse
from volcenginesdkvpn.models.create_vpn_gateway_route_request import CreateVpnGatewayRouteRequest
from volcenginesdkvpn.models.create_vpn_gateway_route_response import CreateVpnGatewayRouteResponse
from volcenginesdkvpn.models.customer_gateway_for_describe_customer_gateways_output import CustomerGatewayForDescribeCustomerGatewaysOutput
from volcenginesdkvpn.models.delete_customer_gateway_request import DeleteCustomerGatewayRequest
from volcenginesdkvpn.models.delete_customer_gateway_response import DeleteCustomerGatewayResponse
from volcenginesdkvpn.models.delete_ssl_vpn_client_cert_request import DeleteSslVpnClientCertRequest
from volcenginesdkvpn.models.delete_ssl_vpn_client_cert_response import DeleteSslVpnClientCertResponse
from volcenginesdkvpn.models.delete_ssl_vpn_server_request import DeleteSslVpnServerRequest
from volcenginesdkvpn.models.delete_ssl_vpn_server_response import DeleteSslVpnServerResponse
from volcenginesdkvpn.models.delete_vpn_connection_health_checker_request import DeleteVpnConnectionHealthCheckerRequest
from volcenginesdkvpn.models.delete_vpn_connection_health_checker_response import DeleteVpnConnectionHealthCheckerResponse
from volcenginesdkvpn.models.delete_vpn_connection_request import DeleteVpnConnectionRequest
from volcenginesdkvpn.models.delete_vpn_connection_response import DeleteVpnConnectionResponse
from volcenginesdkvpn.models.delete_vpn_gateway_request import DeleteVpnGatewayRequest
from volcenginesdkvpn.models.delete_vpn_gateway_response import DeleteVpnGatewayResponse
from volcenginesdkvpn.models.delete_vpn_gateway_route_request import DeleteVpnGatewayRouteRequest
from volcenginesdkvpn.models.delete_vpn_gateway_route_response import DeleteVpnGatewayRouteResponse
from volcenginesdkvpn.models.describe_customer_gateway_attributes_request import DescribeCustomerGatewayAttributesRequest
from volcenginesdkvpn.models.describe_customer_gateway_attributes_response import DescribeCustomerGatewayAttributesResponse
from volcenginesdkvpn.models.describe_customer_gateways_request import DescribeCustomerGatewaysRequest
from volcenginesdkvpn.models.describe_customer_gateways_response import DescribeCustomerGatewaysResponse
from volcenginesdkvpn.models.describe_ssl_vpn_client_cert_attributes_request import DescribeSslVpnClientCertAttributesRequest
from volcenginesdkvpn.models.describe_ssl_vpn_client_cert_attributes_response import DescribeSslVpnClientCertAttributesResponse
from volcenginesdkvpn.models.describe_ssl_vpn_client_certs_request import DescribeSslVpnClientCertsRequest
from volcenginesdkvpn.models.describe_ssl_vpn_client_certs_response import DescribeSslVpnClientCertsResponse
from volcenginesdkvpn.models.describe_ssl_vpn_servers_request import DescribeSslVpnServersRequest
from volcenginesdkvpn.models.describe_ssl_vpn_servers_response import DescribeSslVpnServersResponse
from volcenginesdkvpn.models.describe_vpn_connection_attributes_request import DescribeVpnConnectionAttributesRequest
from volcenginesdkvpn.models.describe_vpn_connection_attributes_response import DescribeVpnConnectionAttributesResponse
from volcenginesdkvpn.models.describe_vpn_connections_request import DescribeVpnConnectionsRequest
from volcenginesdkvpn.models.describe_vpn_connections_response import DescribeVpnConnectionsResponse
from volcenginesdkvpn.models.describe_vpn_gateway_attributes_request import DescribeVpnGatewayAttributesRequest
from volcenginesdkvpn.models.describe_vpn_gateway_attributes_response import DescribeVpnGatewayAttributesResponse
from volcenginesdkvpn.models.describe_vpn_gateway_route_attributes_request import DescribeVpnGatewayRouteAttributesRequest
from volcenginesdkvpn.models.describe_vpn_gateway_route_attributes_response import DescribeVpnGatewayRouteAttributesResponse
from volcenginesdkvpn.models.describe_vpn_gateway_routes_request import DescribeVpnGatewayRoutesRequest
from volcenginesdkvpn.models.describe_vpn_gateway_routes_response import DescribeVpnGatewayRoutesResponse
from volcenginesdkvpn.models.describe_vpn_gateways_billing_request import DescribeVpnGatewaysBillingRequest
from volcenginesdkvpn.models.describe_vpn_gateways_billing_response import DescribeVpnGatewaysBillingResponse
from volcenginesdkvpn.models.describe_vpn_gateways_request import DescribeVpnGatewaysRequest
from volcenginesdkvpn.models.describe_vpn_gateways_response import DescribeVpnGatewaysResponse
from volcenginesdkvpn.models.health_check_config_for_create_vpn_connection_health_checkers_input import HealthCheckConfigForCreateVpnConnectionHealthCheckersInput
from volcenginesdkvpn.models.health_checker_for_describe_vpn_connection_attributes_output import HealthCheckerForDescribeVpnConnectionAttributesOutput
from volcenginesdkvpn.models.health_checker_for_describe_vpn_connections_output import HealthCheckerForDescribeVpnConnectionsOutput
from volcenginesdkvpn.models.ike_config_for_create_vpn_connection_input import IkeConfigForCreateVpnConnectionInput
from volcenginesdkvpn.models.ike_config_for_describe_vpn_connection_attributes_output import IkeConfigForDescribeVpnConnectionAttributesOutput
from volcenginesdkvpn.models.ike_config_for_describe_vpn_connections_output import IkeConfigForDescribeVpnConnectionsOutput
from volcenginesdkvpn.models.ike_config_for_modify_vpn_connection_attributes_input import IkeConfigForModifyVpnConnectionAttributesInput
from volcenginesdkvpn.models.ike_config_for_modify_vpn_connection_tunnel_attributes_input import IkeConfigForModifyVpnConnectionTunnelAttributesInput
from volcenginesdkvpn.models.ipsec_config_for_create_vpn_connection_input import IpsecConfigForCreateVpnConnectionInput
from volcenginesdkvpn.models.ipsec_config_for_describe_vpn_connection_attributes_output import IpsecConfigForDescribeVpnConnectionAttributesOutput
from volcenginesdkvpn.models.ipsec_config_for_describe_vpn_connections_output import IpsecConfigForDescribeVpnConnectionsOutput
from volcenginesdkvpn.models.ipsec_config_for_modify_vpn_connection_attributes_input import IpsecConfigForModifyVpnConnectionAttributesInput
from volcenginesdkvpn.models.ipsec_config_for_modify_vpn_connection_tunnel_attributes_input import IpsecConfigForModifyVpnConnectionTunnelAttributesInput
from volcenginesdkvpn.models.modify_customer_gateway_attributes_request import ModifyCustomerGatewayAttributesRequest
from volcenginesdkvpn.models.modify_customer_gateway_attributes_response import ModifyCustomerGatewayAttributesResponse
from volcenginesdkvpn.models.modify_ssl_vpn_client_cert_request import ModifySslVpnClientCertRequest
from volcenginesdkvpn.models.modify_ssl_vpn_client_cert_response import ModifySslVpnClientCertResponse
from volcenginesdkvpn.models.modify_ssl_vpn_server_request import ModifySslVpnServerRequest
from volcenginesdkvpn.models.modify_ssl_vpn_server_response import ModifySslVpnServerResponse
from volcenginesdkvpn.models.modify_vpn_connection_attributes_request import ModifyVpnConnectionAttributesRequest
from volcenginesdkvpn.models.modify_vpn_connection_attributes_response import ModifyVpnConnectionAttributesResponse
from volcenginesdkvpn.models.modify_vpn_connection_health_checker_request import ModifyVpnConnectionHealthCheckerRequest
from volcenginesdkvpn.models.modify_vpn_connection_health_checker_response import ModifyVpnConnectionHealthCheckerResponse
from volcenginesdkvpn.models.modify_vpn_connection_tunnel_attributes_request import ModifyVpnConnectionTunnelAttributesRequest
from volcenginesdkvpn.models.modify_vpn_connection_tunnel_attributes_response import ModifyVpnConnectionTunnelAttributesResponse
from volcenginesdkvpn.models.modify_vpn_gateway_attributes_request import ModifyVpnGatewayAttributesRequest
from volcenginesdkvpn.models.modify_vpn_gateway_attributes_response import ModifyVpnGatewayAttributesResponse
from volcenginesdkvpn.models.renew_vpn_gateway_request import RenewVpnGatewayRequest
from volcenginesdkvpn.models.renew_vpn_gateway_response import RenewVpnGatewayResponse
from volcenginesdkvpn.models.reset_vpn_connection_request import ResetVpnConnectionRequest
from volcenginesdkvpn.models.reset_vpn_connection_response import ResetVpnConnectionResponse
from volcenginesdkvpn.models.set_vpn_gateway_renewal_request import SetVpnGatewayRenewalRequest
from volcenginesdkvpn.models.set_vpn_gateway_renewal_response import SetVpnGatewayRenewalResponse
from volcenginesdkvpn.models.ssl_vpn_client_cert_for_describe_ssl_vpn_client_certs_output import SslVpnClientCertForDescribeSslVpnClientCertsOutput
from volcenginesdkvpn.models.ssl_vpn_server_for_describe_ssl_vpn_servers_output import SslVpnServerForDescribeSslVpnServersOutput
from volcenginesdkvpn.models.tag_filter_for_describe_customer_gateways_input import TagFilterForDescribeCustomerGatewaysInput
from volcenginesdkvpn.models.tag_filter_for_describe_ssl_vpn_client_certs_input import TagFilterForDescribeSslVpnClientCertsInput
from volcenginesdkvpn.models.tag_filter_for_describe_ssl_vpn_servers_input import TagFilterForDescribeSslVpnServersInput
from volcenginesdkvpn.models.tag_filter_for_describe_vpn_connections_input import TagFilterForDescribeVpnConnectionsInput
from volcenginesdkvpn.models.tag_filter_for_describe_vpn_gateways_input import TagFilterForDescribeVpnGatewaysInput
from volcenginesdkvpn.models.tag_for_create_vpn_gateway_input import TagForCreateVpnGatewayInput
from volcenginesdkvpn.models.tag_for_describe_customer_gateway_attributes_output import TagForDescribeCustomerGatewayAttributesOutput
from volcenginesdkvpn.models.tag_for_describe_ssl_vpn_client_certs_output import TagForDescribeSslVpnClientCertsOutput
from volcenginesdkvpn.models.tag_for_describe_ssl_vpn_servers_output import TagForDescribeSslVpnServersOutput
from volcenginesdkvpn.models.tag_for_describe_vpn_connection_attributes_output import TagForDescribeVpnConnectionAttributesOutput
from volcenginesdkvpn.models.tag_for_describe_vpn_connections_output import TagForDescribeVpnConnectionsOutput
from volcenginesdkvpn.models.tag_for_describe_vpn_gateway_attributes_output import TagForDescribeVpnGatewayAttributesOutput
from volcenginesdkvpn.models.tag_for_describe_vpn_gateways_billing_output import TagForDescribeVpnGatewaysBillingOutput
from volcenginesdkvpn.models.tag_for_describe_vpn_gateways_output import TagForDescribeVpnGatewaysOutput
from volcenginesdkvpn.models.tunnel_option_for_create_vpn_connection_input import TunnelOptionForCreateVpnConnectionInput
from volcenginesdkvpn.models.tunnel_option_for_describe_vpn_connection_attributes_output import TunnelOptionForDescribeVpnConnectionAttributesOutput
from volcenginesdkvpn.models.tunnel_option_for_describe_vpn_connections_output import TunnelOptionForDescribeVpnConnectionsOutput
from volcenginesdkvpn.models.tunnel_option_for_modify_vpn_connection_attributes_input import TunnelOptionForModifyVpnConnectionAttributesInput
from volcenginesdkvpn.models.vpn_connection_for_describe_vpn_connections_output import VpnConnectionForDescribeVpnConnectionsOutput
from volcenginesdkvpn.models.vpn_gateway_for_describe_vpn_gateways_billing_output import VpnGatewayForDescribeVpnGatewaysBillingOutput
from volcenginesdkvpn.models.vpn_gateway_for_describe_vpn_gateways_output import VpnGatewayForDescribeVpnGatewaysOutput
from volcenginesdkvpn.models.vpn_gateway_route_for_describe_vpn_gateway_routes_output import VpnGatewayRouteForDescribeVpnGatewayRoutesOutput
