# coding: utf-8

"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeVpnGatewaysRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ip_address': 'str',
        'ipsec_enabled': 'bool',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'ssl_enabled': 'bool',
        'subnet_id': 'str',
        'tag_filters': 'list[TagFilterForDescribeVpnGatewaysInput]',
        'vpc_id': 'str',
        'vpn_gateway_ids': 'list[str]',
        'vpn_gateway_name': 'str'
    }

    attribute_map = {
        'ip_address': 'IpAddress',
        'ipsec_enabled': 'IpsecEnabled',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'ssl_enabled': 'SslEnabled',
        'subnet_id': 'SubnetId',
        'tag_filters': 'TagFilters',
        'vpc_id': 'VpcId',
        'vpn_gateway_ids': 'VpnGatewayIds',
        'vpn_gateway_name': 'VpnGatewayName'
    }

    def __init__(self, ip_address=None, ipsec_enabled=None, page_number=None, page_size=None, project_name=None, ssl_enabled=None, subnet_id=None, tag_filters=None, vpc_id=None, vpn_gateway_ids=None, vpn_gateway_name=None, _configuration=None):  # noqa: E501
        """DescribeVpnGatewaysRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ip_address = None
        self._ipsec_enabled = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._ssl_enabled = None
        self._subnet_id = None
        self._tag_filters = None
        self._vpc_id = None
        self._vpn_gateway_ids = None
        self._vpn_gateway_name = None
        self.discriminator = None

        if ip_address is not None:
            self.ip_address = ip_address
        if ipsec_enabled is not None:
            self.ipsec_enabled = ipsec_enabled
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if ssl_enabled is not None:
            self.ssl_enabled = ssl_enabled
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if vpn_gateway_ids is not None:
            self.vpn_gateway_ids = vpn_gateway_ids
        if vpn_gateway_name is not None:
            self.vpn_gateway_name = vpn_gateway_name

    @property
    def ip_address(self):
        """Gets the ip_address of this DescribeVpnGatewaysRequest.  # noqa: E501


        :return: The ip_address of this DescribeVpnGatewaysRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_address

    @ip_address.setter
    def ip_address(self, ip_address):
        """Sets the ip_address of this DescribeVpnGatewaysRequest.


        :param ip_address: The ip_address of this DescribeVpnGatewaysRequest.  # noqa: E501
        :type: str
        """

        self._ip_address = ip_address

    @property
    def ipsec_enabled(self):
        """Gets the ipsec_enabled of this DescribeVpnGatewaysRequest.  # noqa: E501


        :return: The ipsec_enabled of this DescribeVpnGatewaysRequest.  # noqa: E501
        :rtype: bool
        """
        return self._ipsec_enabled

    @ipsec_enabled.setter
    def ipsec_enabled(self, ipsec_enabled):
        """Sets the ipsec_enabled of this DescribeVpnGatewaysRequest.


        :param ipsec_enabled: The ipsec_enabled of this DescribeVpnGatewaysRequest.  # noqa: E501
        :type: bool
        """

        self._ipsec_enabled = ipsec_enabled

    @property
    def page_number(self):
        """Gets the page_number of this DescribeVpnGatewaysRequest.  # noqa: E501


        :return: The page_number of this DescribeVpnGatewaysRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeVpnGatewaysRequest.


        :param page_number: The page_number of this DescribeVpnGatewaysRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeVpnGatewaysRequest.  # noqa: E501


        :return: The page_size of this DescribeVpnGatewaysRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeVpnGatewaysRequest.


        :param page_size: The page_size of this DescribeVpnGatewaysRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeVpnGatewaysRequest.  # noqa: E501


        :return: The project_name of this DescribeVpnGatewaysRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeVpnGatewaysRequest.


        :param project_name: The project_name of this DescribeVpnGatewaysRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def ssl_enabled(self):
        """Gets the ssl_enabled of this DescribeVpnGatewaysRequest.  # noqa: E501


        :return: The ssl_enabled of this DescribeVpnGatewaysRequest.  # noqa: E501
        :rtype: bool
        """
        return self._ssl_enabled

    @ssl_enabled.setter
    def ssl_enabled(self, ssl_enabled):
        """Sets the ssl_enabled of this DescribeVpnGatewaysRequest.


        :param ssl_enabled: The ssl_enabled of this DescribeVpnGatewaysRequest.  # noqa: E501
        :type: bool
        """

        self._ssl_enabled = ssl_enabled

    @property
    def subnet_id(self):
        """Gets the subnet_id of this DescribeVpnGatewaysRequest.  # noqa: E501


        :return: The subnet_id of this DescribeVpnGatewaysRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this DescribeVpnGatewaysRequest.


        :param subnet_id: The subnet_id of this DescribeVpnGatewaysRequest.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeVpnGatewaysRequest.  # noqa: E501


        :return: The tag_filters of this DescribeVpnGatewaysRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeVpnGatewaysInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeVpnGatewaysRequest.


        :param tag_filters: The tag_filters of this DescribeVpnGatewaysRequest.  # noqa: E501
        :type: list[TagFilterForDescribeVpnGatewaysInput]
        """

        self._tag_filters = tag_filters

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribeVpnGatewaysRequest.  # noqa: E501


        :return: The vpc_id of this DescribeVpnGatewaysRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribeVpnGatewaysRequest.


        :param vpc_id: The vpc_id of this DescribeVpnGatewaysRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def vpn_gateway_ids(self):
        """Gets the vpn_gateway_ids of this DescribeVpnGatewaysRequest.  # noqa: E501


        :return: The vpn_gateway_ids of this DescribeVpnGatewaysRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vpn_gateway_ids

    @vpn_gateway_ids.setter
    def vpn_gateway_ids(self, vpn_gateway_ids):
        """Sets the vpn_gateway_ids of this DescribeVpnGatewaysRequest.


        :param vpn_gateway_ids: The vpn_gateway_ids of this DescribeVpnGatewaysRequest.  # noqa: E501
        :type: list[str]
        """

        self._vpn_gateway_ids = vpn_gateway_ids

    @property
    def vpn_gateway_name(self):
        """Gets the vpn_gateway_name of this DescribeVpnGatewaysRequest.  # noqa: E501


        :return: The vpn_gateway_name of this DescribeVpnGatewaysRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpn_gateway_name

    @vpn_gateway_name.setter
    def vpn_gateway_name(self, vpn_gateway_name):
        """Sets the vpn_gateway_name of this DescribeVpnGatewaysRequest.


        :param vpn_gateway_name: The vpn_gateway_name of this DescribeVpnGatewaysRequest.  # noqa: E501
        :type: str
        """

        self._vpn_gateway_name = vpn_gateway_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeVpnGatewaysRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeVpnGatewaysRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeVpnGatewaysRequest):
            return True

        return self.to_dict() != other.to_dict()
