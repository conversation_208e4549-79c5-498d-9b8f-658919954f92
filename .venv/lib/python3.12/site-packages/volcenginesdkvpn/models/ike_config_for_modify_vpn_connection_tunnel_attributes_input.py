# coding: utf-8

"""
    vpn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class IkeConfigForModifyVpnConnectionTunnelAttributesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auth_alg': 'str',
        'dh_group': 'str',
        'enc_alg': 'str',
        'lifetime': 'int',
        'local_id': 'str',
        'mode': 'str',
        'psk': 'str',
        'remote_id': 'str',
        'version': 'str'
    }

    attribute_map = {
        'auth_alg': 'AuthAlg',
        'dh_group': 'DhGroup',
        'enc_alg': 'EncAlg',
        'lifetime': 'Lifetime',
        'local_id': 'LocalId',
        'mode': 'Mode',
        'psk': 'Psk',
        'remote_id': 'RemoteId',
        'version': 'Version'
    }

    def __init__(self, auth_alg=None, dh_group=None, enc_alg=None, lifetime=None, local_id=None, mode=None, psk=None, remote_id=None, version=None, _configuration=None):  # noqa: E501
        """IkeConfigForModifyVpnConnectionTunnelAttributesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auth_alg = None
        self._dh_group = None
        self._enc_alg = None
        self._lifetime = None
        self._local_id = None
        self._mode = None
        self._psk = None
        self._remote_id = None
        self._version = None
        self.discriminator = None

        if auth_alg is not None:
            self.auth_alg = auth_alg
        if dh_group is not None:
            self.dh_group = dh_group
        if enc_alg is not None:
            self.enc_alg = enc_alg
        if lifetime is not None:
            self.lifetime = lifetime
        if local_id is not None:
            self.local_id = local_id
        if mode is not None:
            self.mode = mode
        if psk is not None:
            self.psk = psk
        if remote_id is not None:
            self.remote_id = remote_id
        if version is not None:
            self.version = version

    @property
    def auth_alg(self):
        """Gets the auth_alg of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501


        :return: The auth_alg of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :rtype: str
        """
        return self._auth_alg

    @auth_alg.setter
    def auth_alg(self, auth_alg):
        """Sets the auth_alg of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.


        :param auth_alg: The auth_alg of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :type: str
        """

        self._auth_alg = auth_alg

    @property
    def dh_group(self):
        """Gets the dh_group of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501


        :return: The dh_group of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :rtype: str
        """
        return self._dh_group

    @dh_group.setter
    def dh_group(self, dh_group):
        """Sets the dh_group of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.


        :param dh_group: The dh_group of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :type: str
        """

        self._dh_group = dh_group

    @property
    def enc_alg(self):
        """Gets the enc_alg of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501


        :return: The enc_alg of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :rtype: str
        """
        return self._enc_alg

    @enc_alg.setter
    def enc_alg(self, enc_alg):
        """Sets the enc_alg of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.


        :param enc_alg: The enc_alg of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :type: str
        """

        self._enc_alg = enc_alg

    @property
    def lifetime(self):
        """Gets the lifetime of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501


        :return: The lifetime of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :rtype: int
        """
        return self._lifetime

    @lifetime.setter
    def lifetime(self, lifetime):
        """Sets the lifetime of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.


        :param lifetime: The lifetime of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :type: int
        """

        self._lifetime = lifetime

    @property
    def local_id(self):
        """Gets the local_id of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501


        :return: The local_id of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :rtype: str
        """
        return self._local_id

    @local_id.setter
    def local_id(self, local_id):
        """Sets the local_id of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.


        :param local_id: The local_id of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :type: str
        """

        self._local_id = local_id

    @property
    def mode(self):
        """Gets the mode of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501


        :return: The mode of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :rtype: str
        """
        return self._mode

    @mode.setter
    def mode(self, mode):
        """Sets the mode of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.


        :param mode: The mode of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :type: str
        """

        self._mode = mode

    @property
    def psk(self):
        """Gets the psk of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501


        :return: The psk of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :rtype: str
        """
        return self._psk

    @psk.setter
    def psk(self, psk):
        """Sets the psk of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.


        :param psk: The psk of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :type: str
        """

        self._psk = psk

    @property
    def remote_id(self):
        """Gets the remote_id of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501


        :return: The remote_id of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :rtype: str
        """
        return self._remote_id

    @remote_id.setter
    def remote_id(self, remote_id):
        """Sets the remote_id of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.


        :param remote_id: The remote_id of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :type: str
        """

        self._remote_id = remote_id

    @property
    def version(self):
        """Gets the version of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501


        :return: The version of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :rtype: str
        """
        return self._version

    @version.setter
    def version(self, version):
        """Sets the version of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.


        :param version: The version of this IkeConfigForModifyVpnConnectionTunnelAttributesInput.  # noqa: E501
        :type: str
        """

        self._version = version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(IkeConfigForModifyVpnConnectionTunnelAttributesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, IkeConfigForModifyVpnConnectionTunnelAttributesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, IkeConfigForModifyVpnConnectionTunnelAttributesInput):
            return True

        return self.to_dict() != other.to_dict()
