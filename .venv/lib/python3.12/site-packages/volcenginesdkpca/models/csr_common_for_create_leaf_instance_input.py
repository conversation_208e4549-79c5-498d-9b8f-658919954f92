# coding: utf-8

"""
    pca

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CsrCommonForCreateLeafInstanceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'email': 'str',
        'san': 'list[str]',
        'subject': 'SubjectForCreateLeafInstanceInput'
    }

    attribute_map = {
        'email': 'email',
        'san': 'san',
        'subject': 'subject'
    }

    def __init__(self, email=None, san=None, subject=None, _configuration=None):  # noqa: E501
        """CsrCommonForCreateLeafInstanceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._email = None
        self._san = None
        self._subject = None
        self.discriminator = None

        if email is not None:
            self.email = email
        if san is not None:
            self.san = san
        if subject is not None:
            self.subject = subject

    @property
    def email(self):
        """Gets the email of this CsrCommonForCreateLeafInstanceInput.  # noqa: E501


        :return: The email of this CsrCommonForCreateLeafInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this CsrCommonForCreateLeafInstanceInput.


        :param email: The email of this CsrCommonForCreateLeafInstanceInput.  # noqa: E501
        :type: str
        """

        self._email = email

    @property
    def san(self):
        """Gets the san of this CsrCommonForCreateLeafInstanceInput.  # noqa: E501


        :return: The san of this CsrCommonForCreateLeafInstanceInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._san

    @san.setter
    def san(self, san):
        """Sets the san of this CsrCommonForCreateLeafInstanceInput.


        :param san: The san of this CsrCommonForCreateLeafInstanceInput.  # noqa: E501
        :type: list[str]
        """

        self._san = san

    @property
    def subject(self):
        """Gets the subject of this CsrCommonForCreateLeafInstanceInput.  # noqa: E501


        :return: The subject of this CsrCommonForCreateLeafInstanceInput.  # noqa: E501
        :rtype: SubjectForCreateLeafInstanceInput
        """
        return self._subject

    @subject.setter
    def subject(self, subject):
        """Sets the subject of this CsrCommonForCreateLeafInstanceInput.


        :param subject: The subject of this CsrCommonForCreateLeafInstanceInput.  # noqa: E501
        :type: SubjectForCreateLeafInstanceInput
        """

        self._subject = subject

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CsrCommonForCreateLeafInstanceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CsrCommonForCreateLeafInstanceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CsrCommonForCreateLeafInstanceInput):
            return True

        return self.to_dict() != other.to_dict()
