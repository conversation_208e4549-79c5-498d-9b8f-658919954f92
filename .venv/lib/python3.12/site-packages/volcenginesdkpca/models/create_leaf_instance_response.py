# coding: utf-8

"""
    pca

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateLeafInstanceResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'certificate': 'str',
        'chain': 'str',
        'encryption_certificate': 'str',
        'encryption_chain': 'str',
        'encryption_private_key': 'str',
        'instance_id': 'str',
        'parent_certificate': 'str',
        'private_key': 'str',
        'root_certificate': 'str'
    }

    attribute_map = {
        'certificate': 'certificate',
        'chain': 'chain',
        'encryption_certificate': 'encryption_certificate',
        'encryption_chain': 'encryption_chain',
        'encryption_private_key': 'encryption_private_key',
        'instance_id': 'instance_id',
        'parent_certificate': 'parent_certificate',
        'private_key': 'private_key',
        'root_certificate': 'root_certificate'
    }

    def __init__(self, certificate=None, chain=None, encryption_certificate=None, encryption_chain=None, encryption_private_key=None, instance_id=None, parent_certificate=None, private_key=None, root_certificate=None, _configuration=None):  # noqa: E501
        """CreateLeafInstanceResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._certificate = None
        self._chain = None
        self._encryption_certificate = None
        self._encryption_chain = None
        self._encryption_private_key = None
        self._instance_id = None
        self._parent_certificate = None
        self._private_key = None
        self._root_certificate = None
        self.discriminator = None

        if certificate is not None:
            self.certificate = certificate
        if chain is not None:
            self.chain = chain
        if encryption_certificate is not None:
            self.encryption_certificate = encryption_certificate
        if encryption_chain is not None:
            self.encryption_chain = encryption_chain
        if encryption_private_key is not None:
            self.encryption_private_key = encryption_private_key
        if instance_id is not None:
            self.instance_id = instance_id
        if parent_certificate is not None:
            self.parent_certificate = parent_certificate
        if private_key is not None:
            self.private_key = private_key
        if root_certificate is not None:
            self.root_certificate = root_certificate

    @property
    def certificate(self):
        """Gets the certificate of this CreateLeafInstanceResponse.  # noqa: E501


        :return: The certificate of this CreateLeafInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._certificate

    @certificate.setter
    def certificate(self, certificate):
        """Sets the certificate of this CreateLeafInstanceResponse.


        :param certificate: The certificate of this CreateLeafInstanceResponse.  # noqa: E501
        :type: str
        """

        self._certificate = certificate

    @property
    def chain(self):
        """Gets the chain of this CreateLeafInstanceResponse.  # noqa: E501


        :return: The chain of this CreateLeafInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._chain

    @chain.setter
    def chain(self, chain):
        """Sets the chain of this CreateLeafInstanceResponse.


        :param chain: The chain of this CreateLeafInstanceResponse.  # noqa: E501
        :type: str
        """

        self._chain = chain

    @property
    def encryption_certificate(self):
        """Gets the encryption_certificate of this CreateLeafInstanceResponse.  # noqa: E501


        :return: The encryption_certificate of this CreateLeafInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._encryption_certificate

    @encryption_certificate.setter
    def encryption_certificate(self, encryption_certificate):
        """Sets the encryption_certificate of this CreateLeafInstanceResponse.


        :param encryption_certificate: The encryption_certificate of this CreateLeafInstanceResponse.  # noqa: E501
        :type: str
        """

        self._encryption_certificate = encryption_certificate

    @property
    def encryption_chain(self):
        """Gets the encryption_chain of this CreateLeafInstanceResponse.  # noqa: E501


        :return: The encryption_chain of this CreateLeafInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._encryption_chain

    @encryption_chain.setter
    def encryption_chain(self, encryption_chain):
        """Sets the encryption_chain of this CreateLeafInstanceResponse.


        :param encryption_chain: The encryption_chain of this CreateLeafInstanceResponse.  # noqa: E501
        :type: str
        """

        self._encryption_chain = encryption_chain

    @property
    def encryption_private_key(self):
        """Gets the encryption_private_key of this CreateLeafInstanceResponse.  # noqa: E501


        :return: The encryption_private_key of this CreateLeafInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._encryption_private_key

    @encryption_private_key.setter
    def encryption_private_key(self, encryption_private_key):
        """Sets the encryption_private_key of this CreateLeafInstanceResponse.


        :param encryption_private_key: The encryption_private_key of this CreateLeafInstanceResponse.  # noqa: E501
        :type: str
        """

        self._encryption_private_key = encryption_private_key

    @property
    def instance_id(self):
        """Gets the instance_id of this CreateLeafInstanceResponse.  # noqa: E501


        :return: The instance_id of this CreateLeafInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this CreateLeafInstanceResponse.


        :param instance_id: The instance_id of this CreateLeafInstanceResponse.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def parent_certificate(self):
        """Gets the parent_certificate of this CreateLeafInstanceResponse.  # noqa: E501


        :return: The parent_certificate of this CreateLeafInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._parent_certificate

    @parent_certificate.setter
    def parent_certificate(self, parent_certificate):
        """Sets the parent_certificate of this CreateLeafInstanceResponse.


        :param parent_certificate: The parent_certificate of this CreateLeafInstanceResponse.  # noqa: E501
        :type: str
        """

        self._parent_certificate = parent_certificate

    @property
    def private_key(self):
        """Gets the private_key of this CreateLeafInstanceResponse.  # noqa: E501


        :return: The private_key of this CreateLeafInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._private_key

    @private_key.setter
    def private_key(self, private_key):
        """Sets the private_key of this CreateLeafInstanceResponse.


        :param private_key: The private_key of this CreateLeafInstanceResponse.  # noqa: E501
        :type: str
        """

        self._private_key = private_key

    @property
    def root_certificate(self):
        """Gets the root_certificate of this CreateLeafInstanceResponse.  # noqa: E501


        :return: The root_certificate of this CreateLeafInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._root_certificate

    @root_certificate.setter
    def root_certificate(self, root_certificate):
        """Sets the root_certificate of this CreateLeafInstanceResponse.


        :param root_certificate: The root_certificate of this CreateLeafInstanceResponse.  # noqa: E501
        :type: str
        """

        self._root_certificate = root_certificate

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateLeafInstanceResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateLeafInstanceResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateLeafInstanceResponse):
            return True

        return self.to_dict() != other.to_dict()
