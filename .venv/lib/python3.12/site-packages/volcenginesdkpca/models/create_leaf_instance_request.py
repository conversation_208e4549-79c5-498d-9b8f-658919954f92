# coding: utf-8

"""
    pca

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateLeafInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'contact': 'ContactForCreateLeafInstanceInput',
        'csr': 'str',
        'csr_common': 'CsrCommonForCreateLeafInstanceInput',
        'duration_type': 'str',
        'extensions': 'ExtensionsForCreateLeafInstanceInput',
        'hash_alg': 'str',
        'idempotence_token': 'str',
        'issuer_id': 'str',
        'key_alg': 'str',
        'key_param': 'str',
        'quantity': 'int',
        'tag': 'str',
        'use_quota': 'bool'
    }

    attribute_map = {
        'contact': 'contact',
        'csr': 'csr',
        'csr_common': 'csr_common',
        'duration_type': 'duration_type',
        'extensions': 'extensions',
        'hash_alg': 'hash_alg',
        'idempotence_token': 'idempotence_token',
        'issuer_id': 'issuer_id',
        'key_alg': 'key_alg',
        'key_param': 'key_param',
        'quantity': 'quantity',
        'tag': 'tag',
        'use_quota': 'use_quota'
    }

    def __init__(self, contact=None, csr=None, csr_common=None, duration_type=None, extensions=None, hash_alg=None, idempotence_token=None, issuer_id=None, key_alg=None, key_param=None, quantity=None, tag=None, use_quota=None, _configuration=None):  # noqa: E501
        """CreateLeafInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._contact = None
        self._csr = None
        self._csr_common = None
        self._duration_type = None
        self._extensions = None
        self._hash_alg = None
        self._idempotence_token = None
        self._issuer_id = None
        self._key_alg = None
        self._key_param = None
        self._quantity = None
        self._tag = None
        self._use_quota = None
        self.discriminator = None

        if contact is not None:
            self.contact = contact
        if csr is not None:
            self.csr = csr
        if csr_common is not None:
            self.csr_common = csr_common
        self.duration_type = duration_type
        if extensions is not None:
            self.extensions = extensions
        self.hash_alg = hash_alg
        if idempotence_token is not None:
            self.idempotence_token = idempotence_token
        self.issuer_id = issuer_id
        self.key_alg = key_alg
        self.key_param = key_param
        self.quantity = quantity
        if tag is not None:
            self.tag = tag
        if use_quota is not None:
            self.use_quota = use_quota

    @property
    def contact(self):
        """Gets the contact of this CreateLeafInstanceRequest.  # noqa: E501


        :return: The contact of this CreateLeafInstanceRequest.  # noqa: E501
        :rtype: ContactForCreateLeafInstanceInput
        """
        return self._contact

    @contact.setter
    def contact(self, contact):
        """Sets the contact of this CreateLeafInstanceRequest.


        :param contact: The contact of this CreateLeafInstanceRequest.  # noqa: E501
        :type: ContactForCreateLeafInstanceInput
        """

        self._contact = contact

    @property
    def csr(self):
        """Gets the csr of this CreateLeafInstanceRequest.  # noqa: E501


        :return: The csr of this CreateLeafInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._csr

    @csr.setter
    def csr(self, csr):
        """Sets the csr of this CreateLeafInstanceRequest.


        :param csr: The csr of this CreateLeafInstanceRequest.  # noqa: E501
        :type: str
        """

        self._csr = csr

    @property
    def csr_common(self):
        """Gets the csr_common of this CreateLeafInstanceRequest.  # noqa: E501


        :return: The csr_common of this CreateLeafInstanceRequest.  # noqa: E501
        :rtype: CsrCommonForCreateLeafInstanceInput
        """
        return self._csr_common

    @csr_common.setter
    def csr_common(self, csr_common):
        """Sets the csr_common of this CreateLeafInstanceRequest.


        :param csr_common: The csr_common of this CreateLeafInstanceRequest.  # noqa: E501
        :type: CsrCommonForCreateLeafInstanceInput
        """

        self._csr_common = csr_common

    @property
    def duration_type(self):
        """Gets the duration_type of this CreateLeafInstanceRequest.  # noqa: E501


        :return: The duration_type of this CreateLeafInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._duration_type

    @duration_type.setter
    def duration_type(self, duration_type):
        """Sets the duration_type of this CreateLeafInstanceRequest.


        :param duration_type: The duration_type of this CreateLeafInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and duration_type is None:
            raise ValueError("Invalid value for `duration_type`, must not be `None`")  # noqa: E501

        self._duration_type = duration_type

    @property
    def extensions(self):
        """Gets the extensions of this CreateLeafInstanceRequest.  # noqa: E501


        :return: The extensions of this CreateLeafInstanceRequest.  # noqa: E501
        :rtype: ExtensionsForCreateLeafInstanceInput
        """
        return self._extensions

    @extensions.setter
    def extensions(self, extensions):
        """Sets the extensions of this CreateLeafInstanceRequest.


        :param extensions: The extensions of this CreateLeafInstanceRequest.  # noqa: E501
        :type: ExtensionsForCreateLeafInstanceInput
        """

        self._extensions = extensions

    @property
    def hash_alg(self):
        """Gets the hash_alg of this CreateLeafInstanceRequest.  # noqa: E501


        :return: The hash_alg of this CreateLeafInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._hash_alg

    @hash_alg.setter
    def hash_alg(self, hash_alg):
        """Sets the hash_alg of this CreateLeafInstanceRequest.


        :param hash_alg: The hash_alg of this CreateLeafInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and hash_alg is None:
            raise ValueError("Invalid value for `hash_alg`, must not be `None`")  # noqa: E501

        self._hash_alg = hash_alg

    @property
    def idempotence_token(self):
        """Gets the idempotence_token of this CreateLeafInstanceRequest.  # noqa: E501


        :return: The idempotence_token of this CreateLeafInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._idempotence_token

    @idempotence_token.setter
    def idempotence_token(self, idempotence_token):
        """Sets the idempotence_token of this CreateLeafInstanceRequest.


        :param idempotence_token: The idempotence_token of this CreateLeafInstanceRequest.  # noqa: E501
        :type: str
        """

        self._idempotence_token = idempotence_token

    @property
    def issuer_id(self):
        """Gets the issuer_id of this CreateLeafInstanceRequest.  # noqa: E501


        :return: The issuer_id of this CreateLeafInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._issuer_id

    @issuer_id.setter
    def issuer_id(self, issuer_id):
        """Sets the issuer_id of this CreateLeafInstanceRequest.


        :param issuer_id: The issuer_id of this CreateLeafInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and issuer_id is None:
            raise ValueError("Invalid value for `issuer_id`, must not be `None`")  # noqa: E501

        self._issuer_id = issuer_id

    @property
    def key_alg(self):
        """Gets the key_alg of this CreateLeafInstanceRequest.  # noqa: E501


        :return: The key_alg of this CreateLeafInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._key_alg

    @key_alg.setter
    def key_alg(self, key_alg):
        """Sets the key_alg of this CreateLeafInstanceRequest.


        :param key_alg: The key_alg of this CreateLeafInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and key_alg is None:
            raise ValueError("Invalid value for `key_alg`, must not be `None`")  # noqa: E501

        self._key_alg = key_alg

    @property
    def key_param(self):
        """Gets the key_param of this CreateLeafInstanceRequest.  # noqa: E501


        :return: The key_param of this CreateLeafInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._key_param

    @key_param.setter
    def key_param(self, key_param):
        """Sets the key_param of this CreateLeafInstanceRequest.


        :param key_param: The key_param of this CreateLeafInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and key_param is None:
            raise ValueError("Invalid value for `key_param`, must not be `None`")  # noqa: E501

        self._key_param = key_param

    @property
    def quantity(self):
        """Gets the quantity of this CreateLeafInstanceRequest.  # noqa: E501


        :return: The quantity of this CreateLeafInstanceRequest.  # noqa: E501
        :rtype: int
        """
        return self._quantity

    @quantity.setter
    def quantity(self, quantity):
        """Sets the quantity of this CreateLeafInstanceRequest.


        :param quantity: The quantity of this CreateLeafInstanceRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and quantity is None:
            raise ValueError("Invalid value for `quantity`, must not be `None`")  # noqa: E501

        self._quantity = quantity

    @property
    def tag(self):
        """Gets the tag of this CreateLeafInstanceRequest.  # noqa: E501


        :return: The tag of this CreateLeafInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this CreateLeafInstanceRequest.


        :param tag: The tag of this CreateLeafInstanceRequest.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def use_quota(self):
        """Gets the use_quota of this CreateLeafInstanceRequest.  # noqa: E501


        :return: The use_quota of this CreateLeafInstanceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._use_quota

    @use_quota.setter
    def use_quota(self, use_quota):
        """Sets the use_quota of this CreateLeafInstanceRequest.


        :param use_quota: The use_quota of this CreateLeafInstanceRequest.  # noqa: E501
        :type: bool
        """

        self._use_quota = use_quota

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateLeafInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateLeafInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateLeafInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
