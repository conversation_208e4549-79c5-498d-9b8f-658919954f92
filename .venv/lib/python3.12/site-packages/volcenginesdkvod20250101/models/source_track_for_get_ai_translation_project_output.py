# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SourceTrackForGetAITranslationProjectOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'blueprint_id': 'str',
        'id': 'str',
        'speaker_id': 'str',
        'translation_type': 'str'
    }

    attribute_map = {
        'blueprint_id': 'BlueprintId',
        'id': 'Id',
        'speaker_id': 'SpeakerId',
        'translation_type': 'TranslationType'
    }

    def __init__(self, blueprint_id=None, id=None, speaker_id=None, translation_type=None, _configuration=None):  # noqa: E501
        """SourceTrackForGetAITranslationProjectOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._blueprint_id = None
        self._id = None
        self._speaker_id = None
        self._translation_type = None
        self.discriminator = None

        if blueprint_id is not None:
            self.blueprint_id = blueprint_id
        if id is not None:
            self.id = id
        if speaker_id is not None:
            self.speaker_id = speaker_id
        if translation_type is not None:
            self.translation_type = translation_type

    @property
    def blueprint_id(self):
        """Gets the blueprint_id of this SourceTrackForGetAITranslationProjectOutput.  # noqa: E501


        :return: The blueprint_id of this SourceTrackForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._blueprint_id

    @blueprint_id.setter
    def blueprint_id(self, blueprint_id):
        """Sets the blueprint_id of this SourceTrackForGetAITranslationProjectOutput.


        :param blueprint_id: The blueprint_id of this SourceTrackForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._blueprint_id = blueprint_id

    @property
    def id(self):
        """Gets the id of this SourceTrackForGetAITranslationProjectOutput.  # noqa: E501


        :return: The id of this SourceTrackForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this SourceTrackForGetAITranslationProjectOutput.


        :param id: The id of this SourceTrackForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def speaker_id(self):
        """Gets the speaker_id of this SourceTrackForGetAITranslationProjectOutput.  # noqa: E501


        :return: The speaker_id of this SourceTrackForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._speaker_id

    @speaker_id.setter
    def speaker_id(self, speaker_id):
        """Sets the speaker_id of this SourceTrackForGetAITranslationProjectOutput.


        :param speaker_id: The speaker_id of this SourceTrackForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._speaker_id = speaker_id

    @property
    def translation_type(self):
        """Gets the translation_type of this SourceTrackForGetAITranslationProjectOutput.  # noqa: E501


        :return: The translation_type of this SourceTrackForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._translation_type

    @translation_type.setter
    def translation_type(self, translation_type):
        """Sets the translation_type of this SourceTrackForGetAITranslationProjectOutput.


        :param translation_type: The translation_type of this SourceTrackForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._translation_type = translation_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SourceTrackForGetAITranslationProjectOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SourceTrackForGetAITranslationProjectOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SourceTrackForGetAITranslationProjectOutput):
            return True

        return self.to_dict() != other.to_dict()
