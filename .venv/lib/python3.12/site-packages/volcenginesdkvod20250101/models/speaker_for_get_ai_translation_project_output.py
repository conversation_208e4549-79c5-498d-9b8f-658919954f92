# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SpeakerForGetAITranslationProjectOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'name': 'str',
        'project_id': 'str',
        'voice': 'str'
    }

    attribute_map = {
        'id': 'Id',
        'name': 'Name',
        'project_id': 'ProjectId',
        'voice': 'Voice'
    }

    def __init__(self, id=None, name=None, project_id=None, voice=None, _configuration=None):  # noqa: E501
        """SpeakerForGetAITranslationProjectOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._project_id = None
        self._voice = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if project_id is not None:
            self.project_id = project_id
        if voice is not None:
            self.voice = voice

    @property
    def id(self):
        """Gets the id of this SpeakerForGetAITranslationProjectOutput.  # noqa: E501


        :return: The id of this SpeakerForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this SpeakerForGetAITranslationProjectOutput.


        :param id: The id of this SpeakerForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this SpeakerForGetAITranslationProjectOutput.  # noqa: E501


        :return: The name of this SpeakerForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this SpeakerForGetAITranslationProjectOutput.


        :param name: The name of this SpeakerForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def project_id(self):
        """Gets the project_id of this SpeakerForGetAITranslationProjectOutput.  # noqa: E501


        :return: The project_id of this SpeakerForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this SpeakerForGetAITranslationProjectOutput.


        :param project_id: The project_id of this SpeakerForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._project_id = project_id

    @property
    def voice(self):
        """Gets the voice of this SpeakerForGetAITranslationProjectOutput.  # noqa: E501


        :return: The voice of this SpeakerForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._voice

    @voice.setter
    def voice(self, voice):
        """Sets the voice of this SpeakerForGetAITranslationProjectOutput.


        :param voice: The voice of this SpeakerForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._voice = voice

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SpeakerForGetAITranslationProjectOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SpeakerForGetAITranslationProjectOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SpeakerForGetAITranslationProjectOutput):
            return True

        return self.to_dict() != other.to_dict()
