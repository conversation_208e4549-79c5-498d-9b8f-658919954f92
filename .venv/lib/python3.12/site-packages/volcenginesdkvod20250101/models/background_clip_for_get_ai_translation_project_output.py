# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BackgroundClipForGetAITranslationProjectOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'language': 'str',
        'project_id': 'str',
        'translation_type': 'str'
    }

    attribute_map = {
        'id': 'Id',
        'language': 'Language',
        'project_id': 'ProjectId',
        'translation_type': 'TranslationType'
    }

    def __init__(self, id=None, language=None, project_id=None, translation_type=None, _configuration=None):  # noqa: E501
        """BackgroundClipForGetAITranslationProjectOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._language = None
        self._project_id = None
        self._translation_type = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if language is not None:
            self.language = language
        if project_id is not None:
            self.project_id = project_id
        if translation_type is not None:
            self.translation_type = translation_type

    @property
    def id(self):
        """Gets the id of this BackgroundClipForGetAITranslationProjectOutput.  # noqa: E501


        :return: The id of this BackgroundClipForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this BackgroundClipForGetAITranslationProjectOutput.


        :param id: The id of this BackgroundClipForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def language(self):
        """Gets the language of this BackgroundClipForGetAITranslationProjectOutput.  # noqa: E501


        :return: The language of this BackgroundClipForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._language

    @language.setter
    def language(self, language):
        """Sets the language of this BackgroundClipForGetAITranslationProjectOutput.


        :param language: The language of this BackgroundClipForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._language = language

    @property
    def project_id(self):
        """Gets the project_id of this BackgroundClipForGetAITranslationProjectOutput.  # noqa: E501


        :return: The project_id of this BackgroundClipForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this BackgroundClipForGetAITranslationProjectOutput.


        :param project_id: The project_id of this BackgroundClipForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._project_id = project_id

    @property
    def translation_type(self):
        """Gets the translation_type of this BackgroundClipForGetAITranslationProjectOutput.  # noqa: E501


        :return: The translation_type of this BackgroundClipForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._translation_type

    @translation_type.setter
    def translation_type(self, translation_type):
        """Sets the translation_type of this BackgroundClipForGetAITranslationProjectOutput.


        :param translation_type: The translation_type of this BackgroundClipForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._translation_type = translation_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BackgroundClipForGetAITranslationProjectOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackgroundClipForGetAITranslationProjectOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackgroundClipForGetAITranslationProjectOutput):
            return True

        return self.to_dict() != other.to_dict()
