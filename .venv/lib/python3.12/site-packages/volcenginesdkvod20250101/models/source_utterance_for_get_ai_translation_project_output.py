# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SourceUtteranceForGetAITranslationProjectOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'blueprint_id': 'str',
        'clip_id': 'str',
        'id': 'str',
        'is_facial_translation': 'bool',
        'project_id': 'str',
        'source_id': 'str',
        'speaker_id': 'str',
        'status': 'str',
        'text': 'str',
        'translation_type': 'str'
    }

    attribute_map = {
        'blueprint_id': 'BlueprintId',
        'clip_id': 'ClipId',
        'id': 'Id',
        'is_facial_translation': 'IsFacialTranslation',
        'project_id': 'ProjectId',
        'source_id': 'SourceId',
        'speaker_id': 'SpeakerId',
        'status': 'Status',
        'text': 'Text',
        'translation_type': 'TranslationType'
    }

    def __init__(self, blueprint_id=None, clip_id=None, id=None, is_facial_translation=None, project_id=None, source_id=None, speaker_id=None, status=None, text=None, translation_type=None, _configuration=None):  # noqa: E501
        """SourceUtteranceForGetAITranslationProjectOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._blueprint_id = None
        self._clip_id = None
        self._id = None
        self._is_facial_translation = None
        self._project_id = None
        self._source_id = None
        self._speaker_id = None
        self._status = None
        self._text = None
        self._translation_type = None
        self.discriminator = None

        if blueprint_id is not None:
            self.blueprint_id = blueprint_id
        if clip_id is not None:
            self.clip_id = clip_id
        if id is not None:
            self.id = id
        if is_facial_translation is not None:
            self.is_facial_translation = is_facial_translation
        if project_id is not None:
            self.project_id = project_id
        if source_id is not None:
            self.source_id = source_id
        if speaker_id is not None:
            self.speaker_id = speaker_id
        if status is not None:
            self.status = status
        if text is not None:
            self.text = text
        if translation_type is not None:
            self.translation_type = translation_type

    @property
    def blueprint_id(self):
        """Gets the blueprint_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501


        :return: The blueprint_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._blueprint_id

    @blueprint_id.setter
    def blueprint_id(self, blueprint_id):
        """Sets the blueprint_id of this SourceUtteranceForGetAITranslationProjectOutput.


        :param blueprint_id: The blueprint_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._blueprint_id = blueprint_id

    @property
    def clip_id(self):
        """Gets the clip_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501


        :return: The clip_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._clip_id

    @clip_id.setter
    def clip_id(self, clip_id):
        """Sets the clip_id of this SourceUtteranceForGetAITranslationProjectOutput.


        :param clip_id: The clip_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._clip_id = clip_id

    @property
    def id(self):
        """Gets the id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501


        :return: The id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this SourceUtteranceForGetAITranslationProjectOutput.


        :param id: The id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def is_facial_translation(self):
        """Gets the is_facial_translation of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501


        :return: The is_facial_translation of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_facial_translation

    @is_facial_translation.setter
    def is_facial_translation(self, is_facial_translation):
        """Sets the is_facial_translation of this SourceUtteranceForGetAITranslationProjectOutput.


        :param is_facial_translation: The is_facial_translation of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :type: bool
        """

        self._is_facial_translation = is_facial_translation

    @property
    def project_id(self):
        """Gets the project_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501


        :return: The project_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this SourceUtteranceForGetAITranslationProjectOutput.


        :param project_id: The project_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._project_id = project_id

    @property
    def source_id(self):
        """Gets the source_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501


        :return: The source_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_id

    @source_id.setter
    def source_id(self, source_id):
        """Sets the source_id of this SourceUtteranceForGetAITranslationProjectOutput.


        :param source_id: The source_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._source_id = source_id

    @property
    def speaker_id(self):
        """Gets the speaker_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501


        :return: The speaker_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._speaker_id

    @speaker_id.setter
    def speaker_id(self, speaker_id):
        """Sets the speaker_id of this SourceUtteranceForGetAITranslationProjectOutput.


        :param speaker_id: The speaker_id of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._speaker_id = speaker_id

    @property
    def status(self):
        """Gets the status of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501


        :return: The status of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this SourceUtteranceForGetAITranslationProjectOutput.


        :param status: The status of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def text(self):
        """Gets the text of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501


        :return: The text of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._text

    @text.setter
    def text(self, text):
        """Sets the text of this SourceUtteranceForGetAITranslationProjectOutput.


        :param text: The text of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._text = text

    @property
    def translation_type(self):
        """Gets the translation_type of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501


        :return: The translation_type of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._translation_type

    @translation_type.setter
    def translation_type(self, translation_type):
        """Sets the translation_type of this SourceUtteranceForGetAITranslationProjectOutput.


        :param translation_type: The translation_type of this SourceUtteranceForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._translation_type = translation_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SourceUtteranceForGetAITranslationProjectOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SourceUtteranceForGetAITranslationProjectOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SourceUtteranceForGetAITranslationProjectOutput):
            return True

        return self.to_dict() != other.to_dict()
