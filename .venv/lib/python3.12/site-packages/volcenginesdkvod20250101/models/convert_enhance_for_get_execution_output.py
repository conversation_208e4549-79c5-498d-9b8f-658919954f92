# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConvertEnhanceForGetExecutionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'audio_stream_meta': 'AudioStreamMetaForGetExecutionOutput',
        'create_time': 'str',
        'duration': 'float',
        'dynamic_range': 'str',
        'encoded_type': 'str',
        'encrypt': 'bool',
        'encryption': 'EncryptionForGetExecutionOutput',
        'file_id': 'str',
        'file_type': 'str',
        'format': 'str',
        'logo_type': 'str',
        'md5': 'str',
        'size': 'float',
        'store_uri': 'str',
        'tos_storage_class': 'str',
        'video_stream_meta': 'VideoStreamMetaForGetExecutionOutput'
    }

    attribute_map = {
        'audio_stream_meta': 'AudioStreamMeta',
        'create_time': 'CreateTime',
        'duration': 'Duration',
        'dynamic_range': 'DynamicRange',
        'encoded_type': 'EncodedType',
        'encrypt': 'Encrypt',
        'encryption': 'Encryption',
        'file_id': 'FileId',
        'file_type': 'FileType',
        'format': 'Format',
        'logo_type': 'LogoType',
        'md5': 'Md5',
        'size': 'Size',
        'store_uri': 'StoreUri',
        'tos_storage_class': 'TosStorageClass',
        'video_stream_meta': 'VideoStreamMeta'
    }

    def __init__(self, audio_stream_meta=None, create_time=None, duration=None, dynamic_range=None, encoded_type=None, encrypt=None, encryption=None, file_id=None, file_type=None, format=None, logo_type=None, md5=None, size=None, store_uri=None, tos_storage_class=None, video_stream_meta=None, _configuration=None):  # noqa: E501
        """ConvertEnhanceForGetExecutionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._audio_stream_meta = None
        self._create_time = None
        self._duration = None
        self._dynamic_range = None
        self._encoded_type = None
        self._encrypt = None
        self._encryption = None
        self._file_id = None
        self._file_type = None
        self._format = None
        self._logo_type = None
        self._md5 = None
        self._size = None
        self._store_uri = None
        self._tos_storage_class = None
        self._video_stream_meta = None
        self.discriminator = None

        if audio_stream_meta is not None:
            self.audio_stream_meta = audio_stream_meta
        if create_time is not None:
            self.create_time = create_time
        if duration is not None:
            self.duration = duration
        if dynamic_range is not None:
            self.dynamic_range = dynamic_range
        if encoded_type is not None:
            self.encoded_type = encoded_type
        if encrypt is not None:
            self.encrypt = encrypt
        if encryption is not None:
            self.encryption = encryption
        if file_id is not None:
            self.file_id = file_id
        if file_type is not None:
            self.file_type = file_type
        if format is not None:
            self.format = format
        if logo_type is not None:
            self.logo_type = logo_type
        if md5 is not None:
            self.md5 = md5
        if size is not None:
            self.size = size
        if store_uri is not None:
            self.store_uri = store_uri
        if tos_storage_class is not None:
            self.tos_storage_class = tos_storage_class
        if video_stream_meta is not None:
            self.video_stream_meta = video_stream_meta

    @property
    def audio_stream_meta(self):
        """Gets the audio_stream_meta of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The audio_stream_meta of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: AudioStreamMetaForGetExecutionOutput
        """
        return self._audio_stream_meta

    @audio_stream_meta.setter
    def audio_stream_meta(self, audio_stream_meta):
        """Sets the audio_stream_meta of this ConvertEnhanceForGetExecutionOutput.


        :param audio_stream_meta: The audio_stream_meta of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: AudioStreamMetaForGetExecutionOutput
        """

        self._audio_stream_meta = audio_stream_meta

    @property
    def create_time(self):
        """Gets the create_time of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The create_time of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ConvertEnhanceForGetExecutionOutput.


        :param create_time: The create_time of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def duration(self):
        """Gets the duration of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The duration of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this ConvertEnhanceForGetExecutionOutput.


        :param duration: The duration of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._duration = duration

    @property
    def dynamic_range(self):
        """Gets the dynamic_range of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The dynamic_range of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._dynamic_range

    @dynamic_range.setter
    def dynamic_range(self, dynamic_range):
        """Sets the dynamic_range of this ConvertEnhanceForGetExecutionOutput.


        :param dynamic_range: The dynamic_range of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._dynamic_range = dynamic_range

    @property
    def encoded_type(self):
        """Gets the encoded_type of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The encoded_type of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._encoded_type

    @encoded_type.setter
    def encoded_type(self, encoded_type):
        """Sets the encoded_type of this ConvertEnhanceForGetExecutionOutput.


        :param encoded_type: The encoded_type of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._encoded_type = encoded_type

    @property
    def encrypt(self):
        """Gets the encrypt of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The encrypt of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: bool
        """
        return self._encrypt

    @encrypt.setter
    def encrypt(self, encrypt):
        """Sets the encrypt of this ConvertEnhanceForGetExecutionOutput.


        :param encrypt: The encrypt of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: bool
        """

        self._encrypt = encrypt

    @property
    def encryption(self):
        """Gets the encryption of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The encryption of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: EncryptionForGetExecutionOutput
        """
        return self._encryption

    @encryption.setter
    def encryption(self, encryption):
        """Sets the encryption of this ConvertEnhanceForGetExecutionOutput.


        :param encryption: The encryption of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: EncryptionForGetExecutionOutput
        """

        self._encryption = encryption

    @property
    def file_id(self):
        """Gets the file_id of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The file_id of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_id

    @file_id.setter
    def file_id(self, file_id):
        """Sets the file_id of this ConvertEnhanceForGetExecutionOutput.


        :param file_id: The file_id of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._file_id = file_id

    @property
    def file_type(self):
        """Gets the file_type of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The file_type of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_type

    @file_type.setter
    def file_type(self, file_type):
        """Sets the file_type of this ConvertEnhanceForGetExecutionOutput.


        :param file_type: The file_type of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._file_type = file_type

    @property
    def format(self):
        """Gets the format of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The format of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._format

    @format.setter
    def format(self, format):
        """Sets the format of this ConvertEnhanceForGetExecutionOutput.


        :param format: The format of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._format = format

    @property
    def logo_type(self):
        """Gets the logo_type of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The logo_type of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._logo_type

    @logo_type.setter
    def logo_type(self, logo_type):
        """Sets the logo_type of this ConvertEnhanceForGetExecutionOutput.


        :param logo_type: The logo_type of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._logo_type = logo_type

    @property
    def md5(self):
        """Gets the md5 of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The md5 of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._md5

    @md5.setter
    def md5(self, md5):
        """Sets the md5 of this ConvertEnhanceForGetExecutionOutput.


        :param md5: The md5 of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._md5 = md5

    @property
    def size(self):
        """Gets the size of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The size of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this ConvertEnhanceForGetExecutionOutput.


        :param size: The size of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._size = size

    @property
    def store_uri(self):
        """Gets the store_uri of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The store_uri of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._store_uri

    @store_uri.setter
    def store_uri(self, store_uri):
        """Sets the store_uri of this ConvertEnhanceForGetExecutionOutput.


        :param store_uri: The store_uri of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._store_uri = store_uri

    @property
    def tos_storage_class(self):
        """Gets the tos_storage_class of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The tos_storage_class of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._tos_storage_class

    @tos_storage_class.setter
    def tos_storage_class(self, tos_storage_class):
        """Sets the tos_storage_class of this ConvertEnhanceForGetExecutionOutput.


        :param tos_storage_class: The tos_storage_class of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._tos_storage_class = tos_storage_class

    @property
    def video_stream_meta(self):
        """Gets the video_stream_meta of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501


        :return: The video_stream_meta of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :rtype: VideoStreamMetaForGetExecutionOutput
        """
        return self._video_stream_meta

    @video_stream_meta.setter
    def video_stream_meta(self, video_stream_meta):
        """Sets the video_stream_meta of this ConvertEnhanceForGetExecutionOutput.


        :param video_stream_meta: The video_stream_meta of this ConvertEnhanceForGetExecutionOutput.  # noqa: E501
        :type: VideoStreamMetaForGetExecutionOutput
        """

        self._video_stream_meta = video_stream_meta

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConvertEnhanceForGetExecutionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConvertEnhanceForGetExecutionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConvertEnhanceForGetExecutionOutput):
            return True

        return self.to_dict() != other.to_dict()
