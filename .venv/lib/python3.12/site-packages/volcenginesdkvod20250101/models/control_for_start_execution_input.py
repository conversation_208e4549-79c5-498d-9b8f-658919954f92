# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ControlForStartExecutionInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'callback_args': 'str',
        'client_token': 'str',
        'enable_low_priority': 'bool',
        'priority': 'int'
    }

    attribute_map = {
        'callback_args': 'CallbackArgs',
        'client_token': 'ClientToken',
        'enable_low_priority': 'EnableLowPriority',
        'priority': 'Priority'
    }

    def __init__(self, callback_args=None, client_token=None, enable_low_priority=None, priority=None, _configuration=None):  # noqa: E501
        """ControlForStartExecutionInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._callback_args = None
        self._client_token = None
        self._enable_low_priority = None
        self._priority = None
        self.discriminator = None

        if callback_args is not None:
            self.callback_args = callback_args
        if client_token is not None:
            self.client_token = client_token
        if enable_low_priority is not None:
            self.enable_low_priority = enable_low_priority
        if priority is not None:
            self.priority = priority

    @property
    def callback_args(self):
        """Gets the callback_args of this ControlForStartExecutionInput.  # noqa: E501


        :return: The callback_args of this ControlForStartExecutionInput.  # noqa: E501
        :rtype: str
        """
        return self._callback_args

    @callback_args.setter
    def callback_args(self, callback_args):
        """Sets the callback_args of this ControlForStartExecutionInput.


        :param callback_args: The callback_args of this ControlForStartExecutionInput.  # noqa: E501
        :type: str
        """

        self._callback_args = callback_args

    @property
    def client_token(self):
        """Gets the client_token of this ControlForStartExecutionInput.  # noqa: E501


        :return: The client_token of this ControlForStartExecutionInput.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this ControlForStartExecutionInput.


        :param client_token: The client_token of this ControlForStartExecutionInput.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def enable_low_priority(self):
        """Gets the enable_low_priority of this ControlForStartExecutionInput.  # noqa: E501


        :return: The enable_low_priority of this ControlForStartExecutionInput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_low_priority

    @enable_low_priority.setter
    def enable_low_priority(self, enable_low_priority):
        """Sets the enable_low_priority of this ControlForStartExecutionInput.


        :param enable_low_priority: The enable_low_priority of this ControlForStartExecutionInput.  # noqa: E501
        :type: bool
        """

        self._enable_low_priority = enable_low_priority

    @property
    def priority(self):
        """Gets the priority of this ControlForStartExecutionInput.  # noqa: E501


        :return: The priority of this ControlForStartExecutionInput.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this ControlForStartExecutionInput.


        :param priority: The priority of this ControlForStartExecutionInput.  # noqa: E501
        :type: int
        """

        self._priority = priority

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ControlForStartExecutionInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ControlForStartExecutionInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ControlForStartExecutionInput):
            return True

        return self.to_dict() != other.to_dict()
