# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TaskForStartExecutionInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ad_audit': 'AdAuditForStartExecutionInput',
        'asr': 'AsrForStartExecutionInput',
        'audio_extract': 'AudioExtractForStartExecutionInput',
        'highlight': 'HighlightForStartExecutionInput',
        'ocr': 'OcrForStartExecutionInput',
        'segment': 'SegmentForStartExecutionInput',
        'storyline': 'StorylineForStartExecutionInput',
        'type': 'str',
        'vision': 'VisionForStartExecutionInput'
    }

    attribute_map = {
        'ad_audit': 'AdAudit',
        'asr': 'Asr',
        'audio_extract': 'AudioExtract',
        'highlight': 'Highlight',
        'ocr': 'Ocr',
        'segment': 'Segment',
        'storyline': 'Storyline',
        'type': 'Type',
        'vision': 'Vision'
    }

    def __init__(self, ad_audit=None, asr=None, audio_extract=None, highlight=None, ocr=None, segment=None, storyline=None, type=None, vision=None, _configuration=None):  # noqa: E501
        """TaskForStartExecutionInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ad_audit = None
        self._asr = None
        self._audio_extract = None
        self._highlight = None
        self._ocr = None
        self._segment = None
        self._storyline = None
        self._type = None
        self._vision = None
        self.discriminator = None

        if ad_audit is not None:
            self.ad_audit = ad_audit
        if asr is not None:
            self.asr = asr
        if audio_extract is not None:
            self.audio_extract = audio_extract
        if highlight is not None:
            self.highlight = highlight
        if ocr is not None:
            self.ocr = ocr
        if segment is not None:
            self.segment = segment
        if storyline is not None:
            self.storyline = storyline
        if type is not None:
            self.type = type
        if vision is not None:
            self.vision = vision

    @property
    def ad_audit(self):
        """Gets the ad_audit of this TaskForStartExecutionInput.  # noqa: E501


        :return: The ad_audit of this TaskForStartExecutionInput.  # noqa: E501
        :rtype: AdAuditForStartExecutionInput
        """
        return self._ad_audit

    @ad_audit.setter
    def ad_audit(self, ad_audit):
        """Sets the ad_audit of this TaskForStartExecutionInput.


        :param ad_audit: The ad_audit of this TaskForStartExecutionInput.  # noqa: E501
        :type: AdAuditForStartExecutionInput
        """

        self._ad_audit = ad_audit

    @property
    def asr(self):
        """Gets the asr of this TaskForStartExecutionInput.  # noqa: E501


        :return: The asr of this TaskForStartExecutionInput.  # noqa: E501
        :rtype: AsrForStartExecutionInput
        """
        return self._asr

    @asr.setter
    def asr(self, asr):
        """Sets the asr of this TaskForStartExecutionInput.


        :param asr: The asr of this TaskForStartExecutionInput.  # noqa: E501
        :type: AsrForStartExecutionInput
        """

        self._asr = asr

    @property
    def audio_extract(self):
        """Gets the audio_extract of this TaskForStartExecutionInput.  # noqa: E501


        :return: The audio_extract of this TaskForStartExecutionInput.  # noqa: E501
        :rtype: AudioExtractForStartExecutionInput
        """
        return self._audio_extract

    @audio_extract.setter
    def audio_extract(self, audio_extract):
        """Sets the audio_extract of this TaskForStartExecutionInput.


        :param audio_extract: The audio_extract of this TaskForStartExecutionInput.  # noqa: E501
        :type: AudioExtractForStartExecutionInput
        """

        self._audio_extract = audio_extract

    @property
    def highlight(self):
        """Gets the highlight of this TaskForStartExecutionInput.  # noqa: E501


        :return: The highlight of this TaskForStartExecutionInput.  # noqa: E501
        :rtype: HighlightForStartExecutionInput
        """
        return self._highlight

    @highlight.setter
    def highlight(self, highlight):
        """Sets the highlight of this TaskForStartExecutionInput.


        :param highlight: The highlight of this TaskForStartExecutionInput.  # noqa: E501
        :type: HighlightForStartExecutionInput
        """

        self._highlight = highlight

    @property
    def ocr(self):
        """Gets the ocr of this TaskForStartExecutionInput.  # noqa: E501


        :return: The ocr of this TaskForStartExecutionInput.  # noqa: E501
        :rtype: OcrForStartExecutionInput
        """
        return self._ocr

    @ocr.setter
    def ocr(self, ocr):
        """Sets the ocr of this TaskForStartExecutionInput.


        :param ocr: The ocr of this TaskForStartExecutionInput.  # noqa: E501
        :type: OcrForStartExecutionInput
        """

        self._ocr = ocr

    @property
    def segment(self):
        """Gets the segment of this TaskForStartExecutionInput.  # noqa: E501


        :return: The segment of this TaskForStartExecutionInput.  # noqa: E501
        :rtype: SegmentForStartExecutionInput
        """
        return self._segment

    @segment.setter
    def segment(self, segment):
        """Sets the segment of this TaskForStartExecutionInput.


        :param segment: The segment of this TaskForStartExecutionInput.  # noqa: E501
        :type: SegmentForStartExecutionInput
        """

        self._segment = segment

    @property
    def storyline(self):
        """Gets the storyline of this TaskForStartExecutionInput.  # noqa: E501


        :return: The storyline of this TaskForStartExecutionInput.  # noqa: E501
        :rtype: StorylineForStartExecutionInput
        """
        return self._storyline

    @storyline.setter
    def storyline(self, storyline):
        """Sets the storyline of this TaskForStartExecutionInput.


        :param storyline: The storyline of this TaskForStartExecutionInput.  # noqa: E501
        :type: StorylineForStartExecutionInput
        """

        self._storyline = storyline

    @property
    def type(self):
        """Gets the type of this TaskForStartExecutionInput.  # noqa: E501


        :return: The type of this TaskForStartExecutionInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this TaskForStartExecutionInput.


        :param type: The type of this TaskForStartExecutionInput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def vision(self):
        """Gets the vision of this TaskForStartExecutionInput.  # noqa: E501


        :return: The vision of this TaskForStartExecutionInput.  # noqa: E501
        :rtype: VisionForStartExecutionInput
        """
        return self._vision

    @vision.setter
    def vision(self, vision):
        """Sets the vision of this TaskForStartExecutionInput.


        :param vision: The vision of this TaskForStartExecutionInput.  # noqa: E501
        :type: VisionForStartExecutionInput
        """

        self._vision = vision

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TaskForStartExecutionInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TaskForStartExecutionInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TaskForStartExecutionInput):
            return True

        return self.to_dict() != other.to_dict()
