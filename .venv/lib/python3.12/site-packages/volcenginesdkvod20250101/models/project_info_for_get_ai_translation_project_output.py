# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProjectInfoForGetAITranslationProjectOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'background_audio': 'BackgroundAudioForGetAITranslationProjectOutput',
        'background_clip': 'BackgroundClipForGetAITranslationProjectOutput',
        'created_at': 'str',
        'error_code': 'int',
        'error_msg': 'str',
        'facial_translation_video': 'FacialTranslationVideoForGetAITranslationProjectOutput',
        'foreground_audio': 'ForegroundAudioForGetAITranslationProjectOutput',
        'input_video': 'InputVideoForGetAITranslationProjectOutput',
        'input_video_title': 'str',
        'operator_config': 'OperatorConfigForGetAITranslationProjectOutput',
        'output_video': 'OutputVideoForGetAITranslationProjectOutput',
        'preview_video': 'PreviewVideoForGetAITranslationProjectOutput',
        'preview_video_muted': 'PreviewVideoMutedForGetAITranslationProjectOutput',
        'project_id': 'str',
        'project_version': 'str',
        'source_blueprints': 'list[SourceBlueprintForGetAITranslationProjectOutput]',
        'source_clips': 'list[SourceClipForGetAITranslationProjectOutput]',
        'source_tracks': 'list[SourceTrackForGetAITranslationProjectOutput]',
        'source_utterances': 'list[SourceUtteranceForGetAITranslationProjectOutput]',
        'speakers': 'list[SpeakerForGetAITranslationProjectOutput]',
        'status': 'str',
        'target_blueprints': 'list[TargetBlueprintForGetAITranslationProjectOutput]',
        'target_clips': 'list[TargetClipForGetAITranslationProjectOutput]',
        'target_tracks': 'list[TargetTrackForGetAITranslationProjectOutput]',
        'target_utterances': 'list[TargetUtteranceForGetAITranslationProjectOutput]',
        'translation_type_list': 'list[str]',
        'updated_at': 'str',
        'voice_translation_video': 'VoiceTranslationVideoForGetAITranslationProjectOutput',
        'workflow_id': 'str'
    }

    attribute_map = {
        'background_audio': 'BackgroundAudio',
        'background_clip': 'BackgroundClip',
        'created_at': 'CreatedAt',
        'error_code': 'ErrorCode',
        'error_msg': 'ErrorMsg',
        'facial_translation_video': 'FacialTranslationVideo',
        'foreground_audio': 'ForegroundAudio',
        'input_video': 'InputVideo',
        'input_video_title': 'InputVideoTitle',
        'operator_config': 'OperatorConfig',
        'output_video': 'OutputVideo',
        'preview_video': 'PreviewVideo',
        'preview_video_muted': 'PreviewVideoMuted',
        'project_id': 'ProjectId',
        'project_version': 'ProjectVersion',
        'source_blueprints': 'SourceBlueprints',
        'source_clips': 'SourceClips',
        'source_tracks': 'SourceTracks',
        'source_utterances': 'SourceUtterances',
        'speakers': 'Speakers',
        'status': 'Status',
        'target_blueprints': 'TargetBlueprints',
        'target_clips': 'TargetClips',
        'target_tracks': 'TargetTracks',
        'target_utterances': 'TargetUtterances',
        'translation_type_list': 'TranslationTypeList',
        'updated_at': 'UpdatedAt',
        'voice_translation_video': 'VoiceTranslationVideo',
        'workflow_id': 'WorkflowId'
    }

    def __init__(self, background_audio=None, background_clip=None, created_at=None, error_code=None, error_msg=None, facial_translation_video=None, foreground_audio=None, input_video=None, input_video_title=None, operator_config=None, output_video=None, preview_video=None, preview_video_muted=None, project_id=None, project_version=None, source_blueprints=None, source_clips=None, source_tracks=None, source_utterances=None, speakers=None, status=None, target_blueprints=None, target_clips=None, target_tracks=None, target_utterances=None, translation_type_list=None, updated_at=None, voice_translation_video=None, workflow_id=None, _configuration=None):  # noqa: E501
        """ProjectInfoForGetAITranslationProjectOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._background_audio = None
        self._background_clip = None
        self._created_at = None
        self._error_code = None
        self._error_msg = None
        self._facial_translation_video = None
        self._foreground_audio = None
        self._input_video = None
        self._input_video_title = None
        self._operator_config = None
        self._output_video = None
        self._preview_video = None
        self._preview_video_muted = None
        self._project_id = None
        self._project_version = None
        self._source_blueprints = None
        self._source_clips = None
        self._source_tracks = None
        self._source_utterances = None
        self._speakers = None
        self._status = None
        self._target_blueprints = None
        self._target_clips = None
        self._target_tracks = None
        self._target_utterances = None
        self._translation_type_list = None
        self._updated_at = None
        self._voice_translation_video = None
        self._workflow_id = None
        self.discriminator = None

        if background_audio is not None:
            self.background_audio = background_audio
        if background_clip is not None:
            self.background_clip = background_clip
        if created_at is not None:
            self.created_at = created_at
        if error_code is not None:
            self.error_code = error_code
        if error_msg is not None:
            self.error_msg = error_msg
        if facial_translation_video is not None:
            self.facial_translation_video = facial_translation_video
        if foreground_audio is not None:
            self.foreground_audio = foreground_audio
        if input_video is not None:
            self.input_video = input_video
        if input_video_title is not None:
            self.input_video_title = input_video_title
        if operator_config is not None:
            self.operator_config = operator_config
        if output_video is not None:
            self.output_video = output_video
        if preview_video is not None:
            self.preview_video = preview_video
        if preview_video_muted is not None:
            self.preview_video_muted = preview_video_muted
        if project_id is not None:
            self.project_id = project_id
        if project_version is not None:
            self.project_version = project_version
        if source_blueprints is not None:
            self.source_blueprints = source_blueprints
        if source_clips is not None:
            self.source_clips = source_clips
        if source_tracks is not None:
            self.source_tracks = source_tracks
        if source_utterances is not None:
            self.source_utterances = source_utterances
        if speakers is not None:
            self.speakers = speakers
        if status is not None:
            self.status = status
        if target_blueprints is not None:
            self.target_blueprints = target_blueprints
        if target_clips is not None:
            self.target_clips = target_clips
        if target_tracks is not None:
            self.target_tracks = target_tracks
        if target_utterances is not None:
            self.target_utterances = target_utterances
        if translation_type_list is not None:
            self.translation_type_list = translation_type_list
        if updated_at is not None:
            self.updated_at = updated_at
        if voice_translation_video is not None:
            self.voice_translation_video = voice_translation_video
        if workflow_id is not None:
            self.workflow_id = workflow_id

    @property
    def background_audio(self):
        """Gets the background_audio of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The background_audio of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: BackgroundAudioForGetAITranslationProjectOutput
        """
        return self._background_audio

    @background_audio.setter
    def background_audio(self, background_audio):
        """Sets the background_audio of this ProjectInfoForGetAITranslationProjectOutput.


        :param background_audio: The background_audio of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: BackgroundAudioForGetAITranslationProjectOutput
        """

        self._background_audio = background_audio

    @property
    def background_clip(self):
        """Gets the background_clip of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The background_clip of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: BackgroundClipForGetAITranslationProjectOutput
        """
        return self._background_clip

    @background_clip.setter
    def background_clip(self, background_clip):
        """Sets the background_clip of this ProjectInfoForGetAITranslationProjectOutput.


        :param background_clip: The background_clip of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: BackgroundClipForGetAITranslationProjectOutput
        """

        self._background_clip = background_clip

    @property
    def created_at(self):
        """Gets the created_at of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The created_at of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this ProjectInfoForGetAITranslationProjectOutput.


        :param created_at: The created_at of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def error_code(self):
        """Gets the error_code of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The error_code of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: int
        """
        return self._error_code

    @error_code.setter
    def error_code(self, error_code):
        """Sets the error_code of this ProjectInfoForGetAITranslationProjectOutput.


        :param error_code: The error_code of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: int
        """

        self._error_code = error_code

    @property
    def error_msg(self):
        """Gets the error_msg of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The error_msg of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._error_msg

    @error_msg.setter
    def error_msg(self, error_msg):
        """Sets the error_msg of this ProjectInfoForGetAITranslationProjectOutput.


        :param error_msg: The error_msg of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._error_msg = error_msg

    @property
    def facial_translation_video(self):
        """Gets the facial_translation_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The facial_translation_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: FacialTranslationVideoForGetAITranslationProjectOutput
        """
        return self._facial_translation_video

    @facial_translation_video.setter
    def facial_translation_video(self, facial_translation_video):
        """Sets the facial_translation_video of this ProjectInfoForGetAITranslationProjectOutput.


        :param facial_translation_video: The facial_translation_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: FacialTranslationVideoForGetAITranslationProjectOutput
        """

        self._facial_translation_video = facial_translation_video

    @property
    def foreground_audio(self):
        """Gets the foreground_audio of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The foreground_audio of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: ForegroundAudioForGetAITranslationProjectOutput
        """
        return self._foreground_audio

    @foreground_audio.setter
    def foreground_audio(self, foreground_audio):
        """Sets the foreground_audio of this ProjectInfoForGetAITranslationProjectOutput.


        :param foreground_audio: The foreground_audio of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: ForegroundAudioForGetAITranslationProjectOutput
        """

        self._foreground_audio = foreground_audio

    @property
    def input_video(self):
        """Gets the input_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The input_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: InputVideoForGetAITranslationProjectOutput
        """
        return self._input_video

    @input_video.setter
    def input_video(self, input_video):
        """Sets the input_video of this ProjectInfoForGetAITranslationProjectOutput.


        :param input_video: The input_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: InputVideoForGetAITranslationProjectOutput
        """

        self._input_video = input_video

    @property
    def input_video_title(self):
        """Gets the input_video_title of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The input_video_title of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._input_video_title

    @input_video_title.setter
    def input_video_title(self, input_video_title):
        """Sets the input_video_title of this ProjectInfoForGetAITranslationProjectOutput.


        :param input_video_title: The input_video_title of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._input_video_title = input_video_title

    @property
    def operator_config(self):
        """Gets the operator_config of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The operator_config of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: OperatorConfigForGetAITranslationProjectOutput
        """
        return self._operator_config

    @operator_config.setter
    def operator_config(self, operator_config):
        """Sets the operator_config of this ProjectInfoForGetAITranslationProjectOutput.


        :param operator_config: The operator_config of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: OperatorConfigForGetAITranslationProjectOutput
        """

        self._operator_config = operator_config

    @property
    def output_video(self):
        """Gets the output_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The output_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: OutputVideoForGetAITranslationProjectOutput
        """
        return self._output_video

    @output_video.setter
    def output_video(self, output_video):
        """Sets the output_video of this ProjectInfoForGetAITranslationProjectOutput.


        :param output_video: The output_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: OutputVideoForGetAITranslationProjectOutput
        """

        self._output_video = output_video

    @property
    def preview_video(self):
        """Gets the preview_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The preview_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: PreviewVideoForGetAITranslationProjectOutput
        """
        return self._preview_video

    @preview_video.setter
    def preview_video(self, preview_video):
        """Sets the preview_video of this ProjectInfoForGetAITranslationProjectOutput.


        :param preview_video: The preview_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: PreviewVideoForGetAITranslationProjectOutput
        """

        self._preview_video = preview_video

    @property
    def preview_video_muted(self):
        """Gets the preview_video_muted of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The preview_video_muted of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: PreviewVideoMutedForGetAITranslationProjectOutput
        """
        return self._preview_video_muted

    @preview_video_muted.setter
    def preview_video_muted(self, preview_video_muted):
        """Sets the preview_video_muted of this ProjectInfoForGetAITranslationProjectOutput.


        :param preview_video_muted: The preview_video_muted of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: PreviewVideoMutedForGetAITranslationProjectOutput
        """

        self._preview_video_muted = preview_video_muted

    @property
    def project_id(self):
        """Gets the project_id of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The project_id of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this ProjectInfoForGetAITranslationProjectOutput.


        :param project_id: The project_id of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._project_id = project_id

    @property
    def project_version(self):
        """Gets the project_version of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The project_version of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_version

    @project_version.setter
    def project_version(self, project_version):
        """Sets the project_version of this ProjectInfoForGetAITranslationProjectOutput.


        :param project_version: The project_version of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._project_version = project_version

    @property
    def source_blueprints(self):
        """Gets the source_blueprints of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The source_blueprints of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: list[SourceBlueprintForGetAITranslationProjectOutput]
        """
        return self._source_blueprints

    @source_blueprints.setter
    def source_blueprints(self, source_blueprints):
        """Sets the source_blueprints of this ProjectInfoForGetAITranslationProjectOutput.


        :param source_blueprints: The source_blueprints of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: list[SourceBlueprintForGetAITranslationProjectOutput]
        """

        self._source_blueprints = source_blueprints

    @property
    def source_clips(self):
        """Gets the source_clips of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The source_clips of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: list[SourceClipForGetAITranslationProjectOutput]
        """
        return self._source_clips

    @source_clips.setter
    def source_clips(self, source_clips):
        """Sets the source_clips of this ProjectInfoForGetAITranslationProjectOutput.


        :param source_clips: The source_clips of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: list[SourceClipForGetAITranslationProjectOutput]
        """

        self._source_clips = source_clips

    @property
    def source_tracks(self):
        """Gets the source_tracks of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The source_tracks of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: list[SourceTrackForGetAITranslationProjectOutput]
        """
        return self._source_tracks

    @source_tracks.setter
    def source_tracks(self, source_tracks):
        """Sets the source_tracks of this ProjectInfoForGetAITranslationProjectOutput.


        :param source_tracks: The source_tracks of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: list[SourceTrackForGetAITranslationProjectOutput]
        """

        self._source_tracks = source_tracks

    @property
    def source_utterances(self):
        """Gets the source_utterances of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The source_utterances of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: list[SourceUtteranceForGetAITranslationProjectOutput]
        """
        return self._source_utterances

    @source_utterances.setter
    def source_utterances(self, source_utterances):
        """Sets the source_utterances of this ProjectInfoForGetAITranslationProjectOutput.


        :param source_utterances: The source_utterances of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: list[SourceUtteranceForGetAITranslationProjectOutput]
        """

        self._source_utterances = source_utterances

    @property
    def speakers(self):
        """Gets the speakers of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The speakers of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: list[SpeakerForGetAITranslationProjectOutput]
        """
        return self._speakers

    @speakers.setter
    def speakers(self, speakers):
        """Sets the speakers of this ProjectInfoForGetAITranslationProjectOutput.


        :param speakers: The speakers of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: list[SpeakerForGetAITranslationProjectOutput]
        """

        self._speakers = speakers

    @property
    def status(self):
        """Gets the status of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The status of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ProjectInfoForGetAITranslationProjectOutput.


        :param status: The status of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def target_blueprints(self):
        """Gets the target_blueprints of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The target_blueprints of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: list[TargetBlueprintForGetAITranslationProjectOutput]
        """
        return self._target_blueprints

    @target_blueprints.setter
    def target_blueprints(self, target_blueprints):
        """Sets the target_blueprints of this ProjectInfoForGetAITranslationProjectOutput.


        :param target_blueprints: The target_blueprints of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: list[TargetBlueprintForGetAITranslationProjectOutput]
        """

        self._target_blueprints = target_blueprints

    @property
    def target_clips(self):
        """Gets the target_clips of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The target_clips of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: list[TargetClipForGetAITranslationProjectOutput]
        """
        return self._target_clips

    @target_clips.setter
    def target_clips(self, target_clips):
        """Sets the target_clips of this ProjectInfoForGetAITranslationProjectOutput.


        :param target_clips: The target_clips of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: list[TargetClipForGetAITranslationProjectOutput]
        """

        self._target_clips = target_clips

    @property
    def target_tracks(self):
        """Gets the target_tracks of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The target_tracks of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: list[TargetTrackForGetAITranslationProjectOutput]
        """
        return self._target_tracks

    @target_tracks.setter
    def target_tracks(self, target_tracks):
        """Sets the target_tracks of this ProjectInfoForGetAITranslationProjectOutput.


        :param target_tracks: The target_tracks of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: list[TargetTrackForGetAITranslationProjectOutput]
        """

        self._target_tracks = target_tracks

    @property
    def target_utterances(self):
        """Gets the target_utterances of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The target_utterances of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: list[TargetUtteranceForGetAITranslationProjectOutput]
        """
        return self._target_utterances

    @target_utterances.setter
    def target_utterances(self, target_utterances):
        """Sets the target_utterances of this ProjectInfoForGetAITranslationProjectOutput.


        :param target_utterances: The target_utterances of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: list[TargetUtteranceForGetAITranslationProjectOutput]
        """

        self._target_utterances = target_utterances

    @property
    def translation_type_list(self):
        """Gets the translation_type_list of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The translation_type_list of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._translation_type_list

    @translation_type_list.setter
    def translation_type_list(self, translation_type_list):
        """Sets the translation_type_list of this ProjectInfoForGetAITranslationProjectOutput.


        :param translation_type_list: The translation_type_list of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: list[str]
        """

        self._translation_type_list = translation_type_list

    @property
    def updated_at(self):
        """Gets the updated_at of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The updated_at of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this ProjectInfoForGetAITranslationProjectOutput.


        :param updated_at: The updated_at of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    @property
    def voice_translation_video(self):
        """Gets the voice_translation_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The voice_translation_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: VoiceTranslationVideoForGetAITranslationProjectOutput
        """
        return self._voice_translation_video

    @voice_translation_video.setter
    def voice_translation_video(self, voice_translation_video):
        """Sets the voice_translation_video of this ProjectInfoForGetAITranslationProjectOutput.


        :param voice_translation_video: The voice_translation_video of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: VoiceTranslationVideoForGetAITranslationProjectOutput
        """

        self._voice_translation_video = voice_translation_video

    @property
    def workflow_id(self):
        """Gets the workflow_id of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501


        :return: The workflow_id of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._workflow_id

    @workflow_id.setter
    def workflow_id(self, workflow_id):
        """Sets the workflow_id of this ProjectInfoForGetAITranslationProjectOutput.


        :param workflow_id: The workflow_id of this ProjectInfoForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._workflow_id = workflow_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProjectInfoForGetAITranslationProjectOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProjectInfoForGetAITranslationProjectOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProjectInfoForGetAITranslationProjectOutput):
            return True

        return self.to_dict() != other.to_dict()
