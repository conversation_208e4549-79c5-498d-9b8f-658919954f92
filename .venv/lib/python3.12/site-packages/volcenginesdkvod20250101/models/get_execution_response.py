# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetExecutionResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'code': 'str',
        'control': 'ControlForGetExecutionOutput',
        'input': 'InputForGetExecutionOutput',
        'meta': 'MetaForGetExecutionOutput',
        'multi_inputs': 'list[MultiInputForGetExecutionOutput]',
        'operation': 'OperationForGetExecutionOutput',
        'output': 'OutputForGetExecutionOutput',
        'run_id': 'str',
        'status': 'str'
    }

    attribute_map = {
        'code': 'Code',
        'control': 'Control',
        'input': 'Input',
        'meta': 'Meta',
        'multi_inputs': 'MultiInputs',
        'operation': 'Operation',
        'output': 'Output',
        'run_id': 'RunId',
        'status': 'Status'
    }

    def __init__(self, code=None, control=None, input=None, meta=None, multi_inputs=None, operation=None, output=None, run_id=None, status=None, _configuration=None):  # noqa: E501
        """GetExecutionResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._code = None
        self._control = None
        self._input = None
        self._meta = None
        self._multi_inputs = None
        self._operation = None
        self._output = None
        self._run_id = None
        self._status = None
        self.discriminator = None

        if code is not None:
            self.code = code
        if control is not None:
            self.control = control
        if input is not None:
            self.input = input
        if meta is not None:
            self.meta = meta
        if multi_inputs is not None:
            self.multi_inputs = multi_inputs
        if operation is not None:
            self.operation = operation
        if output is not None:
            self.output = output
        if run_id is not None:
            self.run_id = run_id
        if status is not None:
            self.status = status

    @property
    def code(self):
        """Gets the code of this GetExecutionResponse.  # noqa: E501


        :return: The code of this GetExecutionResponse.  # noqa: E501
        :rtype: str
        """
        return self._code

    @code.setter
    def code(self, code):
        """Sets the code of this GetExecutionResponse.


        :param code: The code of this GetExecutionResponse.  # noqa: E501
        :type: str
        """

        self._code = code

    @property
    def control(self):
        """Gets the control of this GetExecutionResponse.  # noqa: E501


        :return: The control of this GetExecutionResponse.  # noqa: E501
        :rtype: ControlForGetExecutionOutput
        """
        return self._control

    @control.setter
    def control(self, control):
        """Sets the control of this GetExecutionResponse.


        :param control: The control of this GetExecutionResponse.  # noqa: E501
        :type: ControlForGetExecutionOutput
        """

        self._control = control

    @property
    def input(self):
        """Gets the input of this GetExecutionResponse.  # noqa: E501


        :return: The input of this GetExecutionResponse.  # noqa: E501
        :rtype: InputForGetExecutionOutput
        """
        return self._input

    @input.setter
    def input(self, input):
        """Sets the input of this GetExecutionResponse.


        :param input: The input of this GetExecutionResponse.  # noqa: E501
        :type: InputForGetExecutionOutput
        """

        self._input = input

    @property
    def meta(self):
        """Gets the meta of this GetExecutionResponse.  # noqa: E501


        :return: The meta of this GetExecutionResponse.  # noqa: E501
        :rtype: MetaForGetExecutionOutput
        """
        return self._meta

    @meta.setter
    def meta(self, meta):
        """Sets the meta of this GetExecutionResponse.


        :param meta: The meta of this GetExecutionResponse.  # noqa: E501
        :type: MetaForGetExecutionOutput
        """

        self._meta = meta

    @property
    def multi_inputs(self):
        """Gets the multi_inputs of this GetExecutionResponse.  # noqa: E501


        :return: The multi_inputs of this GetExecutionResponse.  # noqa: E501
        :rtype: list[MultiInputForGetExecutionOutput]
        """
        return self._multi_inputs

    @multi_inputs.setter
    def multi_inputs(self, multi_inputs):
        """Sets the multi_inputs of this GetExecutionResponse.


        :param multi_inputs: The multi_inputs of this GetExecutionResponse.  # noqa: E501
        :type: list[MultiInputForGetExecutionOutput]
        """

        self._multi_inputs = multi_inputs

    @property
    def operation(self):
        """Gets the operation of this GetExecutionResponse.  # noqa: E501


        :return: The operation of this GetExecutionResponse.  # noqa: E501
        :rtype: OperationForGetExecutionOutput
        """
        return self._operation

    @operation.setter
    def operation(self, operation):
        """Sets the operation of this GetExecutionResponse.


        :param operation: The operation of this GetExecutionResponse.  # noqa: E501
        :type: OperationForGetExecutionOutput
        """

        self._operation = operation

    @property
    def output(self):
        """Gets the output of this GetExecutionResponse.  # noqa: E501


        :return: The output of this GetExecutionResponse.  # noqa: E501
        :rtype: OutputForGetExecutionOutput
        """
        return self._output

    @output.setter
    def output(self, output):
        """Sets the output of this GetExecutionResponse.


        :param output: The output of this GetExecutionResponse.  # noqa: E501
        :type: OutputForGetExecutionOutput
        """

        self._output = output

    @property
    def run_id(self):
        """Gets the run_id of this GetExecutionResponse.  # noqa: E501


        :return: The run_id of this GetExecutionResponse.  # noqa: E501
        :rtype: str
        """
        return self._run_id

    @run_id.setter
    def run_id(self, run_id):
        """Sets the run_id of this GetExecutionResponse.


        :param run_id: The run_id of this GetExecutionResponse.  # noqa: E501
        :type: str
        """

        self._run_id = run_id

    @property
    def status(self):
        """Gets the status of this GetExecutionResponse.  # noqa: E501


        :return: The status of this GetExecutionResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetExecutionResponse.


        :param status: The status of this GetExecutionResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetExecutionResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetExecutionResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetExecutionResponse):
            return True

        return self.to_dict() != other.to_dict()
