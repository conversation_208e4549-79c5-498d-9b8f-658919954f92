# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConvertAudioExtractForGetExecutionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'background': 'BackgroundForGetExecutionOutput',
        'duration': 'float',
        'voice': 'VoiceForGetExecutionOutput'
    }

    attribute_map = {
        'background': 'Background',
        'duration': 'Duration',
        'voice': 'Voice'
    }

    def __init__(self, background=None, duration=None, voice=None, _configuration=None):  # noqa: E501
        """ConvertAudioExtractForGetExecutionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._background = None
        self._duration = None
        self._voice = None
        self.discriminator = None

        if background is not None:
            self.background = background
        if duration is not None:
            self.duration = duration
        if voice is not None:
            self.voice = voice

    @property
    def background(self):
        """Gets the background of this ConvertAudioExtractForGetExecutionOutput.  # noqa: E501


        :return: The background of this ConvertAudioExtractForGetExecutionOutput.  # noqa: E501
        :rtype: BackgroundForGetExecutionOutput
        """
        return self._background

    @background.setter
    def background(self, background):
        """Sets the background of this ConvertAudioExtractForGetExecutionOutput.


        :param background: The background of this ConvertAudioExtractForGetExecutionOutput.  # noqa: E501
        :type: BackgroundForGetExecutionOutput
        """

        self._background = background

    @property
    def duration(self):
        """Gets the duration of this ConvertAudioExtractForGetExecutionOutput.  # noqa: E501


        :return: The duration of this ConvertAudioExtractForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this ConvertAudioExtractForGetExecutionOutput.


        :param duration: The duration of this ConvertAudioExtractForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._duration = duration

    @property
    def voice(self):
        """Gets the voice of this ConvertAudioExtractForGetExecutionOutput.  # noqa: E501


        :return: The voice of this ConvertAudioExtractForGetExecutionOutput.  # noqa: E501
        :rtype: VoiceForGetExecutionOutput
        """
        return self._voice

    @voice.setter
    def voice(self, voice):
        """Sets the voice of this ConvertAudioExtractForGetExecutionOutput.


        :param voice: The voice of this ConvertAudioExtractForGetExecutionOutput.  # noqa: E501
        :type: VoiceForGetExecutionOutput
        """

        self._voice = voice

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConvertAudioExtractForGetExecutionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConvertAudioExtractForGetExecutionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConvertAudioExtractForGetExecutionOutput):
            return True

        return self.to_dict() != other.to_dict()
