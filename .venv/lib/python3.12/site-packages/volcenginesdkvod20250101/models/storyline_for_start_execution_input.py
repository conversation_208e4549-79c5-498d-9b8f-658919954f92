# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StorylineForStartExecutionInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'clip_duration': 'float',
        'with_snapshot': 'bool'
    }

    attribute_map = {
        'clip_duration': 'ClipDuration',
        'with_snapshot': 'WithSnapshot'
    }

    def __init__(self, clip_duration=None, with_snapshot=None, _configuration=None):  # noqa: E501
        """StorylineForStartExecutionInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._clip_duration = None
        self._with_snapshot = None
        self.discriminator = None

        if clip_duration is not None:
            self.clip_duration = clip_duration
        if with_snapshot is not None:
            self.with_snapshot = with_snapshot

    @property
    def clip_duration(self):
        """Gets the clip_duration of this StorylineForStartExecutionInput.  # noqa: E501


        :return: The clip_duration of this StorylineForStartExecutionInput.  # noqa: E501
        :rtype: float
        """
        return self._clip_duration

    @clip_duration.setter
    def clip_duration(self, clip_duration):
        """Sets the clip_duration of this StorylineForStartExecutionInput.


        :param clip_duration: The clip_duration of this StorylineForStartExecutionInput.  # noqa: E501
        :type: float
        """

        self._clip_duration = clip_duration

    @property
    def with_snapshot(self):
        """Gets the with_snapshot of this StorylineForStartExecutionInput.  # noqa: E501


        :return: The with_snapshot of this StorylineForStartExecutionInput.  # noqa: E501
        :rtype: bool
        """
        return self._with_snapshot

    @with_snapshot.setter
    def with_snapshot(self, with_snapshot):
        """Sets the with_snapshot of this StorylineForStartExecutionInput.


        :param with_snapshot: The with_snapshot of this StorylineForStartExecutionInput.  # noqa: E501
        :type: bool
        """

        self._with_snapshot = with_snapshot

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StorylineForStartExecutionInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StorylineForStartExecutionInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StorylineForStartExecutionInput):
            return True

        return self.to_dict() != other.to_dict()
