# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AsrForGetExecutionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'language': 'str',
        'mode': 'str',
        'type': 'str',
        'with_confidence': 'bool',
        'with_speaker_info': 'bool'
    }

    attribute_map = {
        'language': 'Language',
        'mode': 'Mode',
        'type': 'Type',
        'with_confidence': 'WithConfidence',
        'with_speaker_info': 'WithSpeakerInfo'
    }

    def __init__(self, language=None, mode=None, type=None, with_confidence=None, with_speaker_info=None, _configuration=None):  # noqa: E501
        """AsrForGetExecutionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._language = None
        self._mode = None
        self._type = None
        self._with_confidence = None
        self._with_speaker_info = None
        self.discriminator = None

        if language is not None:
            self.language = language
        if mode is not None:
            self.mode = mode
        if type is not None:
            self.type = type
        if with_confidence is not None:
            self.with_confidence = with_confidence
        if with_speaker_info is not None:
            self.with_speaker_info = with_speaker_info

    @property
    def language(self):
        """Gets the language of this AsrForGetExecutionOutput.  # noqa: E501


        :return: The language of this AsrForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._language

    @language.setter
    def language(self, language):
        """Sets the language of this AsrForGetExecutionOutput.


        :param language: The language of this AsrForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._language = language

    @property
    def mode(self):
        """Gets the mode of this AsrForGetExecutionOutput.  # noqa: E501


        :return: The mode of this AsrForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._mode

    @mode.setter
    def mode(self, mode):
        """Sets the mode of this AsrForGetExecutionOutput.


        :param mode: The mode of this AsrForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._mode = mode

    @property
    def type(self):
        """Gets the type of this AsrForGetExecutionOutput.  # noqa: E501


        :return: The type of this AsrForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this AsrForGetExecutionOutput.


        :param type: The type of this AsrForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def with_confidence(self):
        """Gets the with_confidence of this AsrForGetExecutionOutput.  # noqa: E501


        :return: The with_confidence of this AsrForGetExecutionOutput.  # noqa: E501
        :rtype: bool
        """
        return self._with_confidence

    @with_confidence.setter
    def with_confidence(self, with_confidence):
        """Sets the with_confidence of this AsrForGetExecutionOutput.


        :param with_confidence: The with_confidence of this AsrForGetExecutionOutput.  # noqa: E501
        :type: bool
        """

        self._with_confidence = with_confidence

    @property
    def with_speaker_info(self):
        """Gets the with_speaker_info of this AsrForGetExecutionOutput.  # noqa: E501


        :return: The with_speaker_info of this AsrForGetExecutionOutput.  # noqa: E501
        :rtype: bool
        """
        return self._with_speaker_info

    @with_speaker_info.setter
    def with_speaker_info(self, with_speaker_info):
        """Sets the with_speaker_info of this AsrForGetExecutionOutput.


        :param with_speaker_info: The with_speaker_info of this AsrForGetExecutionOutput.  # noqa: E501
        :type: bool
        """

        self._with_speaker_info = with_speaker_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AsrForGetExecutionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AsrForGetExecutionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AsrForGetExecutionOutput):
            return True

        return self.to_dict() != other.to_dict()
