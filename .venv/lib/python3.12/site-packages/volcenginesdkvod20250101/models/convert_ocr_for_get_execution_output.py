# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConvertOcrForGetExecutionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'duration': 'float',
        'image_set': 'list[ImageSetForGetExecutionOutput]',
        'mode': 'str',
        'texts': 'list[TextForGetExecutionOutput]'
    }

    attribute_map = {
        'duration': 'Duration',
        'image_set': 'ImageSet',
        'mode': 'Mode',
        'texts': 'Texts'
    }

    def __init__(self, duration=None, image_set=None, mode=None, texts=None, _configuration=None):  # noqa: E501
        """ConvertOcrForGetExecutionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._duration = None
        self._image_set = None
        self._mode = None
        self._texts = None
        self.discriminator = None

        if duration is not None:
            self.duration = duration
        if image_set is not None:
            self.image_set = image_set
        if mode is not None:
            self.mode = mode
        if texts is not None:
            self.texts = texts

    @property
    def duration(self):
        """Gets the duration of this ConvertOcrForGetExecutionOutput.  # noqa: E501


        :return: The duration of this ConvertOcrForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this ConvertOcrForGetExecutionOutput.


        :param duration: The duration of this ConvertOcrForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._duration = duration

    @property
    def image_set(self):
        """Gets the image_set of this ConvertOcrForGetExecutionOutput.  # noqa: E501


        :return: The image_set of this ConvertOcrForGetExecutionOutput.  # noqa: E501
        :rtype: list[ImageSetForGetExecutionOutput]
        """
        return self._image_set

    @image_set.setter
    def image_set(self, image_set):
        """Sets the image_set of this ConvertOcrForGetExecutionOutput.


        :param image_set: The image_set of this ConvertOcrForGetExecutionOutput.  # noqa: E501
        :type: list[ImageSetForGetExecutionOutput]
        """

        self._image_set = image_set

    @property
    def mode(self):
        """Gets the mode of this ConvertOcrForGetExecutionOutput.  # noqa: E501


        :return: The mode of this ConvertOcrForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._mode

    @mode.setter
    def mode(self, mode):
        """Sets the mode of this ConvertOcrForGetExecutionOutput.


        :param mode: The mode of this ConvertOcrForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._mode = mode

    @property
    def texts(self):
        """Gets the texts of this ConvertOcrForGetExecutionOutput.  # noqa: E501


        :return: The texts of this ConvertOcrForGetExecutionOutput.  # noqa: E501
        :rtype: list[TextForGetExecutionOutput]
        """
        return self._texts

    @texts.setter
    def texts(self, texts):
        """Sets the texts of this ConvertOcrForGetExecutionOutput.


        :param texts: The texts of this ConvertOcrForGetExecutionOutput.  # noqa: E501
        :type: list[TextForGetExecutionOutput]
        """

        self._texts = texts

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConvertOcrForGetExecutionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConvertOcrForGetExecutionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConvertOcrForGetExecutionOutput):
            return True

        return self.to_dict() != other.to_dict()
