# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VideoStreamMetaForGetExecutionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bitrate': 'int',
        'codec': 'str',
        'definition': 'str',
        'duration': 'float',
        'fps': 'float',
        'height': 'int',
        'width': 'int'
    }

    attribute_map = {
        'bitrate': 'Bitrate',
        'codec': 'Codec',
        'definition': 'Definition',
        'duration': 'Duration',
        'fps': 'Fps',
        'height': 'Height',
        'width': 'Width'
    }

    def __init__(self, bitrate=None, codec=None, definition=None, duration=None, fps=None, height=None, width=None, _configuration=None):  # noqa: E501
        """VideoStreamMetaForGetExecutionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bitrate = None
        self._codec = None
        self._definition = None
        self._duration = None
        self._fps = None
        self._height = None
        self._width = None
        self.discriminator = None

        if bitrate is not None:
            self.bitrate = bitrate
        if codec is not None:
            self.codec = codec
        if definition is not None:
            self.definition = definition
        if duration is not None:
            self.duration = duration
        if fps is not None:
            self.fps = fps
        if height is not None:
            self.height = height
        if width is not None:
            self.width = width

    @property
    def bitrate(self):
        """Gets the bitrate of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501


        :return: The bitrate of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501
        :rtype: int
        """
        return self._bitrate

    @bitrate.setter
    def bitrate(self, bitrate):
        """Sets the bitrate of this VideoStreamMetaForGetExecutionOutput.


        :param bitrate: The bitrate of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501
        :type: int
        """

        self._bitrate = bitrate

    @property
    def codec(self):
        """Gets the codec of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501


        :return: The codec of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._codec

    @codec.setter
    def codec(self, codec):
        """Sets the codec of this VideoStreamMetaForGetExecutionOutput.


        :param codec: The codec of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._codec = codec

    @property
    def definition(self):
        """Gets the definition of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501


        :return: The definition of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._definition

    @definition.setter
    def definition(self, definition):
        """Sets the definition of this VideoStreamMetaForGetExecutionOutput.


        :param definition: The definition of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._definition = definition

    @property
    def duration(self):
        """Gets the duration of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501


        :return: The duration of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this VideoStreamMetaForGetExecutionOutput.


        :param duration: The duration of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._duration = duration

    @property
    def fps(self):
        """Gets the fps of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501


        :return: The fps of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._fps

    @fps.setter
    def fps(self, fps):
        """Sets the fps of this VideoStreamMetaForGetExecutionOutput.


        :param fps: The fps of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._fps = fps

    @property
    def height(self):
        """Gets the height of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501


        :return: The height of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501
        :rtype: int
        """
        return self._height

    @height.setter
    def height(self, height):
        """Sets the height of this VideoStreamMetaForGetExecutionOutput.


        :param height: The height of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501
        :type: int
        """

        self._height = height

    @property
    def width(self):
        """Gets the width of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501


        :return: The width of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501
        :rtype: int
        """
        return self._width

    @width.setter
    def width(self, width):
        """Sets the width of this VideoStreamMetaForGetExecutionOutput.


        :param width: The width of this VideoStreamMetaForGetExecutionOutput.  # noqa: E501
        :type: int
        """

        self._width = width

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VideoStreamMetaForGetExecutionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VideoStreamMetaForGetExecutionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VideoStreamMetaForGetExecutionOutput):
            return True

        return self.to_dict() != other.to_dict()
