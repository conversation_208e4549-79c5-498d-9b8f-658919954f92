# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetAITranslationProjectRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'additions': 'str',
        'project_id': 'str',
        'space_name': 'str'
    }

    attribute_map = {
        'additions': 'Additions',
        'project_id': 'ProjectId',
        'space_name': 'SpaceName'
    }

    def __init__(self, additions=None, project_id=None, space_name=None, _configuration=None):  # noqa: E501
        """GetAITranslationProjectRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._additions = None
        self._project_id = None
        self._space_name = None
        self.discriminator = None

        if additions is not None:
            self.additions = additions
        self.project_id = project_id
        self.space_name = space_name

    @property
    def additions(self):
        """Gets the additions of this GetAITranslationProjectRequest.  # noqa: E501


        :return: The additions of this GetAITranslationProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._additions

    @additions.setter
    def additions(self, additions):
        """Sets the additions of this GetAITranslationProjectRequest.


        :param additions: The additions of this GetAITranslationProjectRequest.  # noqa: E501
        :type: str
        """

        self._additions = additions

    @property
    def project_id(self):
        """Gets the project_id of this GetAITranslationProjectRequest.  # noqa: E501


        :return: The project_id of this GetAITranslationProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this GetAITranslationProjectRequest.


        :param project_id: The project_id of this GetAITranslationProjectRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and project_id is None:
            raise ValueError("Invalid value for `project_id`, must not be `None`")  # noqa: E501

        self._project_id = project_id

    @property
    def space_name(self):
        """Gets the space_name of this GetAITranslationProjectRequest.  # noqa: E501


        :return: The space_name of this GetAITranslationProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._space_name

    @space_name.setter
    def space_name(self, space_name):
        """Sets the space_name of this GetAITranslationProjectRequest.


        :param space_name: The space_name of this GetAITranslationProjectRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and space_name is None:
            raise ValueError("Invalid value for `space_name`, must not be `None`")  # noqa: E501

        self._space_name = space_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetAITranslationProjectRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetAITranslationProjectRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetAITranslationProjectRequest):
            return True

        return self.to_dict() != other.to_dict()
