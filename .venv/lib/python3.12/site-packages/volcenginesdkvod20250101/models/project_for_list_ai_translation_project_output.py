# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProjectForListAITranslationProjectOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'str',
        'error_code': 'int',
        'error_msg': 'str',
        'input_video': 'InputVideoForListAITranslationProjectOutput',
        'input_video_title': 'str',
        'operator_config': 'OperatorConfigForListAITranslationProjectOutput',
        'output_video': 'OutputVideoForListAITranslationProjectOutput',
        'project_id': 'str',
        'project_version': 'str',
        'source_blueprints': 'list[SourceBlueprintForListAITranslationProjectOutput]',
        'status': 'str',
        'target_blueprints': 'list[TargetBlueprintForListAITranslationProjectOutput]',
        'translation_type_list': 'list[str]',
        'updated_at': 'str',
        'workflow_id': 'str'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'error_code': 'ErrorCode',
        'error_msg': 'ErrorMsg',
        'input_video': 'InputVideo',
        'input_video_title': 'InputVideoTitle',
        'operator_config': 'OperatorConfig',
        'output_video': 'OutputVideo',
        'project_id': 'ProjectId',
        'project_version': 'ProjectVersion',
        'source_blueprints': 'SourceBlueprints',
        'status': 'Status',
        'target_blueprints': 'TargetBlueprints',
        'translation_type_list': 'TranslationTypeList',
        'updated_at': 'UpdatedAt',
        'workflow_id': 'WorkflowId'
    }

    def __init__(self, created_at=None, error_code=None, error_msg=None, input_video=None, input_video_title=None, operator_config=None, output_video=None, project_id=None, project_version=None, source_blueprints=None, status=None, target_blueprints=None, translation_type_list=None, updated_at=None, workflow_id=None, _configuration=None):  # noqa: E501
        """ProjectForListAITranslationProjectOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._error_code = None
        self._error_msg = None
        self._input_video = None
        self._input_video_title = None
        self._operator_config = None
        self._output_video = None
        self._project_id = None
        self._project_version = None
        self._source_blueprints = None
        self._status = None
        self._target_blueprints = None
        self._translation_type_list = None
        self._updated_at = None
        self._workflow_id = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if error_code is not None:
            self.error_code = error_code
        if error_msg is not None:
            self.error_msg = error_msg
        if input_video is not None:
            self.input_video = input_video
        if input_video_title is not None:
            self.input_video_title = input_video_title
        if operator_config is not None:
            self.operator_config = operator_config
        if output_video is not None:
            self.output_video = output_video
        if project_id is not None:
            self.project_id = project_id
        if project_version is not None:
            self.project_version = project_version
        if source_blueprints is not None:
            self.source_blueprints = source_blueprints
        if status is not None:
            self.status = status
        if target_blueprints is not None:
            self.target_blueprints = target_blueprints
        if translation_type_list is not None:
            self.translation_type_list = translation_type_list
        if updated_at is not None:
            self.updated_at = updated_at
        if workflow_id is not None:
            self.workflow_id = workflow_id

    @property
    def created_at(self):
        """Gets the created_at of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The created_at of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this ProjectForListAITranslationProjectOutput.


        :param created_at: The created_at of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def error_code(self):
        """Gets the error_code of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The error_code of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: int
        """
        return self._error_code

    @error_code.setter
    def error_code(self, error_code):
        """Sets the error_code of this ProjectForListAITranslationProjectOutput.


        :param error_code: The error_code of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: int
        """

        self._error_code = error_code

    @property
    def error_msg(self):
        """Gets the error_msg of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The error_msg of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._error_msg

    @error_msg.setter
    def error_msg(self, error_msg):
        """Sets the error_msg of this ProjectForListAITranslationProjectOutput.


        :param error_msg: The error_msg of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._error_msg = error_msg

    @property
    def input_video(self):
        """Gets the input_video of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The input_video of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: InputVideoForListAITranslationProjectOutput
        """
        return self._input_video

    @input_video.setter
    def input_video(self, input_video):
        """Sets the input_video of this ProjectForListAITranslationProjectOutput.


        :param input_video: The input_video of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: InputVideoForListAITranslationProjectOutput
        """

        self._input_video = input_video

    @property
    def input_video_title(self):
        """Gets the input_video_title of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The input_video_title of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._input_video_title

    @input_video_title.setter
    def input_video_title(self, input_video_title):
        """Sets the input_video_title of this ProjectForListAITranslationProjectOutput.


        :param input_video_title: The input_video_title of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._input_video_title = input_video_title

    @property
    def operator_config(self):
        """Gets the operator_config of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The operator_config of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: OperatorConfigForListAITranslationProjectOutput
        """
        return self._operator_config

    @operator_config.setter
    def operator_config(self, operator_config):
        """Sets the operator_config of this ProjectForListAITranslationProjectOutput.


        :param operator_config: The operator_config of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: OperatorConfigForListAITranslationProjectOutput
        """

        self._operator_config = operator_config

    @property
    def output_video(self):
        """Gets the output_video of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The output_video of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: OutputVideoForListAITranslationProjectOutput
        """
        return self._output_video

    @output_video.setter
    def output_video(self, output_video):
        """Sets the output_video of this ProjectForListAITranslationProjectOutput.


        :param output_video: The output_video of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: OutputVideoForListAITranslationProjectOutput
        """

        self._output_video = output_video

    @property
    def project_id(self):
        """Gets the project_id of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The project_id of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this ProjectForListAITranslationProjectOutput.


        :param project_id: The project_id of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._project_id = project_id

    @property
    def project_version(self):
        """Gets the project_version of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The project_version of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_version

    @project_version.setter
    def project_version(self, project_version):
        """Sets the project_version of this ProjectForListAITranslationProjectOutput.


        :param project_version: The project_version of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._project_version = project_version

    @property
    def source_blueprints(self):
        """Gets the source_blueprints of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The source_blueprints of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: list[SourceBlueprintForListAITranslationProjectOutput]
        """
        return self._source_blueprints

    @source_blueprints.setter
    def source_blueprints(self, source_blueprints):
        """Sets the source_blueprints of this ProjectForListAITranslationProjectOutput.


        :param source_blueprints: The source_blueprints of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: list[SourceBlueprintForListAITranslationProjectOutput]
        """

        self._source_blueprints = source_blueprints

    @property
    def status(self):
        """Gets the status of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The status of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ProjectForListAITranslationProjectOutput.


        :param status: The status of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def target_blueprints(self):
        """Gets the target_blueprints of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The target_blueprints of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: list[TargetBlueprintForListAITranslationProjectOutput]
        """
        return self._target_blueprints

    @target_blueprints.setter
    def target_blueprints(self, target_blueprints):
        """Sets the target_blueprints of this ProjectForListAITranslationProjectOutput.


        :param target_blueprints: The target_blueprints of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: list[TargetBlueprintForListAITranslationProjectOutput]
        """

        self._target_blueprints = target_blueprints

    @property
    def translation_type_list(self):
        """Gets the translation_type_list of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The translation_type_list of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._translation_type_list

    @translation_type_list.setter
    def translation_type_list(self, translation_type_list):
        """Sets the translation_type_list of this ProjectForListAITranslationProjectOutput.


        :param translation_type_list: The translation_type_list of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: list[str]
        """

        self._translation_type_list = translation_type_list

    @property
    def updated_at(self):
        """Gets the updated_at of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The updated_at of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this ProjectForListAITranslationProjectOutput.


        :param updated_at: The updated_at of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    @property
    def workflow_id(self):
        """Gets the workflow_id of this ProjectForListAITranslationProjectOutput.  # noqa: E501


        :return: The workflow_id of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._workflow_id

    @workflow_id.setter
    def workflow_id(self, workflow_id):
        """Sets the workflow_id of this ProjectForListAITranslationProjectOutput.


        :param workflow_id: The workflow_id of this ProjectForListAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._workflow_id = workflow_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProjectForListAITranslationProjectOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProjectForListAITranslationProjectOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProjectForListAITranslationProjectOutput):
            return True

        return self.to_dict() != other.to_dict()
