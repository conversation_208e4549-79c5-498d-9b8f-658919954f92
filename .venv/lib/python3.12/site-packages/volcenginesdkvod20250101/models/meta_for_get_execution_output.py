# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MetaForGetExecutionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'end_time': 'str',
        'space_name': 'str',
        'start_time': 'str',
        'trigger': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'end_time': 'EndTime',
        'space_name': 'SpaceName',
        'start_time': 'StartTime',
        'trigger': 'Trigger'
    }

    def __init__(self, create_time=None, end_time=None, space_name=None, start_time=None, trigger=None, _configuration=None):  # noqa: E501
        """MetaForGetExecutionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._end_time = None
        self._space_name = None
        self._start_time = None
        self._trigger = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if end_time is not None:
            self.end_time = end_time
        if space_name is not None:
            self.space_name = space_name
        if start_time is not None:
            self.start_time = start_time
        if trigger is not None:
            self.trigger = trigger

    @property
    def create_time(self):
        """Gets the create_time of this MetaForGetExecutionOutput.  # noqa: E501


        :return: The create_time of this MetaForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this MetaForGetExecutionOutput.


        :param create_time: The create_time of this MetaForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def end_time(self):
        """Gets the end_time of this MetaForGetExecutionOutput.  # noqa: E501


        :return: The end_time of this MetaForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this MetaForGetExecutionOutput.


        :param end_time: The end_time of this MetaForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._end_time = end_time

    @property
    def space_name(self):
        """Gets the space_name of this MetaForGetExecutionOutput.  # noqa: E501


        :return: The space_name of this MetaForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._space_name

    @space_name.setter
    def space_name(self, space_name):
        """Sets the space_name of this MetaForGetExecutionOutput.


        :param space_name: The space_name of this MetaForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._space_name = space_name

    @property
    def start_time(self):
        """Gets the start_time of this MetaForGetExecutionOutput.  # noqa: E501


        :return: The start_time of this MetaForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this MetaForGetExecutionOutput.


        :param start_time: The start_time of this MetaForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def trigger(self):
        """Gets the trigger of this MetaForGetExecutionOutput.  # noqa: E501


        :return: The trigger of this MetaForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._trigger

    @trigger.setter
    def trigger(self, trigger):
        """Sets the trigger of this MetaForGetExecutionOutput.


        :param trigger: The trigger of this MetaForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._trigger = trigger

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MetaForGetExecutionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MetaForGetExecutionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MetaForGetExecutionOutput):
            return True

        return self.to_dict() != other.to_dict()
