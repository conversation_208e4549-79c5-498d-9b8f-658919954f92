# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConvertConvertSegmentForGetExecutionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'duration': 'float',
        'segments': 'list[ConvertSegmentForGetExecutionOutput]'
    }

    attribute_map = {
        'duration': 'Duration',
        'segments': 'Segments'
    }

    def __init__(self, duration=None, segments=None, _configuration=None):  # noqa: E501
        """ConvertConvertSegmentForGetExecutionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._duration = None
        self._segments = None
        self.discriminator = None

        if duration is not None:
            self.duration = duration
        if segments is not None:
            self.segments = segments

    @property
    def duration(self):
        """Gets the duration of this ConvertConvertSegmentForGetExecutionOutput.  # noqa: E501


        :return: The duration of this ConvertConvertSegmentForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this ConvertConvertSegmentForGetExecutionOutput.


        :param duration: The duration of this ConvertConvertSegmentForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._duration = duration

    @property
    def segments(self):
        """Gets the segments of this ConvertConvertSegmentForGetExecutionOutput.  # noqa: E501


        :return: The segments of this ConvertConvertSegmentForGetExecutionOutput.  # noqa: E501
        :rtype: list[ConvertSegmentForGetExecutionOutput]
        """
        return self._segments

    @segments.setter
    def segments(self, segments):
        """Sets the segments of this ConvertConvertSegmentForGetExecutionOutput.


        :param segments: The segments of this ConvertConvertSegmentForGetExecutionOutput.  # noqa: E501
        :type: list[ConvertSegmentForGetExecutionOutput]
        """

        self._segments = segments

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConvertConvertSegmentForGetExecutionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConvertConvertSegmentForGetExecutionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConvertConvertSegmentForGetExecutionOutput):
            return True

        return self.to_dict() != other.to_dict()
