# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AttributeForGetExecutionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'confidence': 'float',
        'event': 'str',
        'speaker': 'str'
    }

    attribute_map = {
        'confidence': 'Confidence',
        'event': 'Event',
        'speaker': 'Speaker'
    }

    def __init__(self, confidence=None, event=None, speaker=None, _configuration=None):  # noqa: E501
        """AttributeForGetExecutionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._confidence = None
        self._event = None
        self._speaker = None
        self.discriminator = None

        if confidence is not None:
            self.confidence = confidence
        if event is not None:
            self.event = event
        if speaker is not None:
            self.speaker = speaker

    @property
    def confidence(self):
        """Gets the confidence of this AttributeForGetExecutionOutput.  # noqa: E501


        :return: The confidence of this AttributeForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._confidence

    @confidence.setter
    def confidence(self, confidence):
        """Sets the confidence of this AttributeForGetExecutionOutput.


        :param confidence: The confidence of this AttributeForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._confidence = confidence

    @property
    def event(self):
        """Gets the event of this AttributeForGetExecutionOutput.  # noqa: E501


        :return: The event of this AttributeForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._event

    @event.setter
    def event(self, event):
        """Sets the event of this AttributeForGetExecutionOutput.


        :param event: The event of this AttributeForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._event = event

    @property
    def speaker(self):
        """Gets the speaker of this AttributeForGetExecutionOutput.  # noqa: E501


        :return: The speaker of this AttributeForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._speaker

    @speaker.setter
    def speaker(self, speaker):
        """Sets the speaker of this AttributeForGetExecutionOutput.


        :param speaker: The speaker of this AttributeForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._speaker = speaker

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AttributeForGetExecutionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AttributeForGetExecutionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AttributeForGetExecutionOutput):
            return True

        return self.to_dict() != other.to_dict()
