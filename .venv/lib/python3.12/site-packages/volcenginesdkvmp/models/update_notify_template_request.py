# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateNotifyTemplateRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'active': 'ActiveForUpdateNotifyTemplateInput',
        'description': 'str',
        'id': 'str',
        'name': 'str',
        'resolved': 'ResolvedForUpdateNotifyTemplateInput'
    }

    attribute_map = {
        'active': 'Active',
        'description': 'Description',
        'id': 'Id',
        'name': 'Name',
        'resolved': 'Resolved'
    }

    def __init__(self, active=None, description=None, id=None, name=None, resolved=None, _configuration=None):  # noqa: E501
        """UpdateNotifyTemplateRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._active = None
        self._description = None
        self._id = None
        self._name = None
        self._resolved = None
        self.discriminator = None

        if active is not None:
            self.active = active
        if description is not None:
            self.description = description
        self.id = id
        if name is not None:
            self.name = name
        if resolved is not None:
            self.resolved = resolved

    @property
    def active(self):
        """Gets the active of this UpdateNotifyTemplateRequest.  # noqa: E501


        :return: The active of this UpdateNotifyTemplateRequest.  # noqa: E501
        :rtype: ActiveForUpdateNotifyTemplateInput
        """
        return self._active

    @active.setter
    def active(self, active):
        """Sets the active of this UpdateNotifyTemplateRequest.


        :param active: The active of this UpdateNotifyTemplateRequest.  # noqa: E501
        :type: ActiveForUpdateNotifyTemplateInput
        """

        self._active = active

    @property
    def description(self):
        """Gets the description of this UpdateNotifyTemplateRequest.  # noqa: E501


        :return: The description of this UpdateNotifyTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateNotifyTemplateRequest.


        :param description: The description of this UpdateNotifyTemplateRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this UpdateNotifyTemplateRequest.  # noqa: E501


        :return: The id of this UpdateNotifyTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateNotifyTemplateRequest.


        :param id: The id of this UpdateNotifyTemplateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this UpdateNotifyTemplateRequest.  # noqa: E501


        :return: The name of this UpdateNotifyTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateNotifyTemplateRequest.


        :param name: The name of this UpdateNotifyTemplateRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def resolved(self):
        """Gets the resolved of this UpdateNotifyTemplateRequest.  # noqa: E501


        :return: The resolved of this UpdateNotifyTemplateRequest.  # noqa: E501
        :rtype: ResolvedForUpdateNotifyTemplateInput
        """
        return self._resolved

    @resolved.setter
    def resolved(self, resolved):
        """Sets the resolved of this UpdateNotifyTemplateRequest.


        :param resolved: The resolved of this UpdateNotifyTemplateRequest.  # noqa: E501
        :type: ResolvedForUpdateNotifyTemplateInput
        """

        self._resolved = resolved

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateNotifyTemplateRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateNotifyTemplateRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateNotifyTemplateRequest):
            return True

        return self.to_dict() != other.to_dict()
