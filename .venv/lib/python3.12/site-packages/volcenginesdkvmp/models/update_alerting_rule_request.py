# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateAlertingRuleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'annotations': 'list[AnnotationForUpdateAlertingRuleInput]',
        'description': 'str',
        'id': 'str',
        'labels': 'list[LabelForUpdateAlertingRuleInput]',
        'levels': 'list[LevelForUpdateAlertingRuleInput]',
        'name': 'str',
        'notify_group_policy_id': 'str',
        'notify_policy_id': 'str',
        'query': 'QueryForUpdateAlertingRuleInput',
        'type': 'str'
    }

    attribute_map = {
        'annotations': 'Annotations',
        'description': 'Description',
        'id': 'Id',
        'labels': 'Labels',
        'levels': 'Levels',
        'name': 'Name',
        'notify_group_policy_id': 'NotifyGroupPolicyId',
        'notify_policy_id': 'NotifyPolicyId',
        'query': 'Query',
        'type': 'Type'
    }

    def __init__(self, annotations=None, description=None, id=None, labels=None, levels=None, name=None, notify_group_policy_id=None, notify_policy_id=None, query=None, type=None, _configuration=None):  # noqa: E501
        """UpdateAlertingRuleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._annotations = None
        self._description = None
        self._id = None
        self._labels = None
        self._levels = None
        self._name = None
        self._notify_group_policy_id = None
        self._notify_policy_id = None
        self._query = None
        self._type = None
        self.discriminator = None

        if annotations is not None:
            self.annotations = annotations
        if description is not None:
            self.description = description
        self.id = id
        if labels is not None:
            self.labels = labels
        if levels is not None:
            self.levels = levels
        if name is not None:
            self.name = name
        if notify_group_policy_id is not None:
            self.notify_group_policy_id = notify_group_policy_id
        if notify_policy_id is not None:
            self.notify_policy_id = notify_policy_id
        if query is not None:
            self.query = query
        if type is not None:
            self.type = type

    @property
    def annotations(self):
        """Gets the annotations of this UpdateAlertingRuleRequest.  # noqa: E501


        :return: The annotations of this UpdateAlertingRuleRequest.  # noqa: E501
        :rtype: list[AnnotationForUpdateAlertingRuleInput]
        """
        return self._annotations

    @annotations.setter
    def annotations(self, annotations):
        """Sets the annotations of this UpdateAlertingRuleRequest.


        :param annotations: The annotations of this UpdateAlertingRuleRequest.  # noqa: E501
        :type: list[AnnotationForUpdateAlertingRuleInput]
        """

        self._annotations = annotations

    @property
    def description(self):
        """Gets the description of this UpdateAlertingRuleRequest.  # noqa: E501


        :return: The description of this UpdateAlertingRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateAlertingRuleRequest.


        :param description: The description of this UpdateAlertingRuleRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this UpdateAlertingRuleRequest.  # noqa: E501


        :return: The id of this UpdateAlertingRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateAlertingRuleRequest.


        :param id: The id of this UpdateAlertingRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def labels(self):
        """Gets the labels of this UpdateAlertingRuleRequest.  # noqa: E501


        :return: The labels of this UpdateAlertingRuleRequest.  # noqa: E501
        :rtype: list[LabelForUpdateAlertingRuleInput]
        """
        return self._labels

    @labels.setter
    def labels(self, labels):
        """Sets the labels of this UpdateAlertingRuleRequest.


        :param labels: The labels of this UpdateAlertingRuleRequest.  # noqa: E501
        :type: list[LabelForUpdateAlertingRuleInput]
        """

        self._labels = labels

    @property
    def levels(self):
        """Gets the levels of this UpdateAlertingRuleRequest.  # noqa: E501


        :return: The levels of this UpdateAlertingRuleRequest.  # noqa: E501
        :rtype: list[LevelForUpdateAlertingRuleInput]
        """
        return self._levels

    @levels.setter
    def levels(self, levels):
        """Sets the levels of this UpdateAlertingRuleRequest.


        :param levels: The levels of this UpdateAlertingRuleRequest.  # noqa: E501
        :type: list[LevelForUpdateAlertingRuleInput]
        """

        self._levels = levels

    @property
    def name(self):
        """Gets the name of this UpdateAlertingRuleRequest.  # noqa: E501


        :return: The name of this UpdateAlertingRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateAlertingRuleRequest.


        :param name: The name of this UpdateAlertingRuleRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def notify_group_policy_id(self):
        """Gets the notify_group_policy_id of this UpdateAlertingRuleRequest.  # noqa: E501


        :return: The notify_group_policy_id of this UpdateAlertingRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._notify_group_policy_id

    @notify_group_policy_id.setter
    def notify_group_policy_id(self, notify_group_policy_id):
        """Sets the notify_group_policy_id of this UpdateAlertingRuleRequest.


        :param notify_group_policy_id: The notify_group_policy_id of this UpdateAlertingRuleRequest.  # noqa: E501
        :type: str
        """

        self._notify_group_policy_id = notify_group_policy_id

    @property
    def notify_policy_id(self):
        """Gets the notify_policy_id of this UpdateAlertingRuleRequest.  # noqa: E501


        :return: The notify_policy_id of this UpdateAlertingRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._notify_policy_id

    @notify_policy_id.setter
    def notify_policy_id(self, notify_policy_id):
        """Sets the notify_policy_id of this UpdateAlertingRuleRequest.


        :param notify_policy_id: The notify_policy_id of this UpdateAlertingRuleRequest.  # noqa: E501
        :type: str
        """

        self._notify_policy_id = notify_policy_id

    @property
    def query(self):
        """Gets the query of this UpdateAlertingRuleRequest.  # noqa: E501


        :return: The query of this UpdateAlertingRuleRequest.  # noqa: E501
        :rtype: QueryForUpdateAlertingRuleInput
        """
        return self._query

    @query.setter
    def query(self, query):
        """Sets the query of this UpdateAlertingRuleRequest.


        :param query: The query of this UpdateAlertingRuleRequest.  # noqa: E501
        :type: QueryForUpdateAlertingRuleInput
        """

        self._query = query

    @property
    def type(self):
        """Gets the type of this UpdateAlertingRuleRequest.  # noqa: E501


        :return: The type of this UpdateAlertingRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this UpdateAlertingRuleRequest.


        :param type: The type of this UpdateAlertingRuleRequest.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateAlertingRuleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateAlertingRuleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateAlertingRuleRequest):
            return True

        return self.to_dict() != other.to_dict()
