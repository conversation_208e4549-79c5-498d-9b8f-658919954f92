# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SendValidationMessageResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'message': 'str',
        'send_status': 'str'
    }

    attribute_map = {
        'message': 'Message',
        'send_status': 'SendStatus'
    }

    def __init__(self, message=None, send_status=None, _configuration=None):  # noqa: E501
        """SendValidationMessageResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._message = None
        self._send_status = None
        self.discriminator = None

        if message is not None:
            self.message = message
        if send_status is not None:
            self.send_status = send_status

    @property
    def message(self):
        """Gets the message of this SendValidationMessageResponse.  # noqa: E501


        :return: The message of this SendValidationMessageResponse.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this SendValidationMessageResponse.


        :param message: The message of this SendValidationMessageResponse.  # noqa: E501
        :type: str
        """

        self._message = message

    @property
    def send_status(self):
        """Gets the send_status of this SendValidationMessageResponse.  # noqa: E501


        :return: The send_status of this SendValidationMessageResponse.  # noqa: E501
        :rtype: str
        """
        return self._send_status

    @send_status.setter
    def send_status(self, send_status):
        """Sets the send_status of this SendValidationMessageResponse.


        :param send_status: The send_status of this SendValidationMessageResponse.  # noqa: E501
        :type: str
        """

        self._send_status = send_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SendValidationMessageResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SendValidationMessageResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SendValidationMessageResponse):
            return True

        return self.to_dict() != other.to_dict()
