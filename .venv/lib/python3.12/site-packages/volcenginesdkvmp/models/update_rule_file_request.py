# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateRuleFileRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'content': 'str',
        'description': 'str',
        'id': 'str',
        'name': 'str',
        'workspace_id': 'str'
    }

    attribute_map = {
        'content': 'Content',
        'description': 'Description',
        'id': 'Id',
        'name': 'Name',
        'workspace_id': 'WorkspaceId'
    }

    def __init__(self, content=None, description=None, id=None, name=None, workspace_id=None, _configuration=None):  # noqa: E501
        """UpdateRuleFileRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._content = None
        self._description = None
        self._id = None
        self._name = None
        self._workspace_id = None
        self.discriminator = None

        if content is not None:
            self.content = content
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if workspace_id is not None:
            self.workspace_id = workspace_id

    @property
    def content(self):
        """Gets the content of this UpdateRuleFileRequest.  # noqa: E501


        :return: The content of this UpdateRuleFileRequest.  # noqa: E501
        :rtype: str
        """
        return self._content

    @content.setter
    def content(self, content):
        """Sets the content of this UpdateRuleFileRequest.


        :param content: The content of this UpdateRuleFileRequest.  # noqa: E501
        :type: str
        """

        self._content = content

    @property
    def description(self):
        """Gets the description of this UpdateRuleFileRequest.  # noqa: E501


        :return: The description of this UpdateRuleFileRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateRuleFileRequest.


        :param description: The description of this UpdateRuleFileRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this UpdateRuleFileRequest.  # noqa: E501


        :return: The id of this UpdateRuleFileRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateRuleFileRequest.


        :param id: The id of this UpdateRuleFileRequest.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this UpdateRuleFileRequest.  # noqa: E501


        :return: The name of this UpdateRuleFileRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateRuleFileRequest.


        :param name: The name of this UpdateRuleFileRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def workspace_id(self):
        """Gets the workspace_id of this UpdateRuleFileRequest.  # noqa: E501


        :return: The workspace_id of this UpdateRuleFileRequest.  # noqa: E501
        :rtype: str
        """
        return self._workspace_id

    @workspace_id.setter
    def workspace_id(self, workspace_id):
        """Sets the workspace_id of this UpdateRuleFileRequest.


        :param workspace_id: The workspace_id of this UpdateRuleFileRequest.  # noqa: E501
        :type: str
        """

        self._workspace_id = workspace_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateRuleFileRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateRuleFileRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateRuleFileRequest):
            return True

        return self.to_dict() != other.to_dict()
