# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TestDingTalkBotWebhookResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ding_talk_code': 'int',
        'ding_talk_message': 'str',
        'error_message': 'str',
        'status_code': 'int'
    }

    attribute_map = {
        'ding_talk_code': 'DingTalkCode',
        'ding_talk_message': 'DingTalkMessage',
        'error_message': 'ErrorMessage',
        'status_code': 'StatusCode'
    }

    def __init__(self, ding_talk_code=None, ding_talk_message=None, error_message=None, status_code=None, _configuration=None):  # noqa: E501
        """TestDingTalkBotWebhookResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ding_talk_code = None
        self._ding_talk_message = None
        self._error_message = None
        self._status_code = None
        self.discriminator = None

        if ding_talk_code is not None:
            self.ding_talk_code = ding_talk_code
        if ding_talk_message is not None:
            self.ding_talk_message = ding_talk_message
        if error_message is not None:
            self.error_message = error_message
        if status_code is not None:
            self.status_code = status_code

    @property
    def ding_talk_code(self):
        """Gets the ding_talk_code of this TestDingTalkBotWebhookResponse.  # noqa: E501


        :return: The ding_talk_code of this TestDingTalkBotWebhookResponse.  # noqa: E501
        :rtype: int
        """
        return self._ding_talk_code

    @ding_talk_code.setter
    def ding_talk_code(self, ding_talk_code):
        """Sets the ding_talk_code of this TestDingTalkBotWebhookResponse.


        :param ding_talk_code: The ding_talk_code of this TestDingTalkBotWebhookResponse.  # noqa: E501
        :type: int
        """

        self._ding_talk_code = ding_talk_code

    @property
    def ding_talk_message(self):
        """Gets the ding_talk_message of this TestDingTalkBotWebhookResponse.  # noqa: E501


        :return: The ding_talk_message of this TestDingTalkBotWebhookResponse.  # noqa: E501
        :rtype: str
        """
        return self._ding_talk_message

    @ding_talk_message.setter
    def ding_talk_message(self, ding_talk_message):
        """Sets the ding_talk_message of this TestDingTalkBotWebhookResponse.


        :param ding_talk_message: The ding_talk_message of this TestDingTalkBotWebhookResponse.  # noqa: E501
        :type: str
        """

        self._ding_talk_message = ding_talk_message

    @property
    def error_message(self):
        """Gets the error_message of this TestDingTalkBotWebhookResponse.  # noqa: E501


        :return: The error_message of this TestDingTalkBotWebhookResponse.  # noqa: E501
        :rtype: str
        """
        return self._error_message

    @error_message.setter
    def error_message(self, error_message):
        """Sets the error_message of this TestDingTalkBotWebhookResponse.


        :param error_message: The error_message of this TestDingTalkBotWebhookResponse.  # noqa: E501
        :type: str
        """

        self._error_message = error_message

    @property
    def status_code(self):
        """Gets the status_code of this TestDingTalkBotWebhookResponse.  # noqa: E501


        :return: The status_code of this TestDingTalkBotWebhookResponse.  # noqa: E501
        :rtype: int
        """
        return self._status_code

    @status_code.setter
    def status_code(self, status_code):
        """Sets the status_code of this TestDingTalkBotWebhookResponse.


        :param status_code: The status_code of this TestDingTalkBotWebhookResponse.  # noqa: E501
        :type: int
        """

        self._status_code = status_code

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TestDingTalkBotWebhookResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TestDingTalkBotWebhookResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TestDingTalkBotWebhookResponse):
            return True

        return self.to_dict() != other.to_dict()
