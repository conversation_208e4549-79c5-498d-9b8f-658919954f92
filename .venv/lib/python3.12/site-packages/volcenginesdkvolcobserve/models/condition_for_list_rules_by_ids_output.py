# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConditionForListRulesByIdsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'comparison_operator': 'str',
        'display_name': 'str',
        'metric_name': 'str',
        'metric_unit': 'str',
        'period': 'str',
        'statistics': 'str',
        'threshold': 'str'
    }

    attribute_map = {
        'comparison_operator': 'ComparisonOperator',
        'display_name': 'DisplayName',
        'metric_name': 'MetricName',
        'metric_unit': 'MetricUnit',
        'period': 'Period',
        'statistics': 'Statistics',
        'threshold': 'Threshold'
    }

    def __init__(self, comparison_operator=None, display_name=None, metric_name=None, metric_unit=None, period=None, statistics=None, threshold=None, _configuration=None):  # noqa: E501
        """ConditionForListRulesByIdsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._comparison_operator = None
        self._display_name = None
        self._metric_name = None
        self._metric_unit = None
        self._period = None
        self._statistics = None
        self._threshold = None
        self.discriminator = None

        if comparison_operator is not None:
            self.comparison_operator = comparison_operator
        if display_name is not None:
            self.display_name = display_name
        if metric_name is not None:
            self.metric_name = metric_name
        if metric_unit is not None:
            self.metric_unit = metric_unit
        if period is not None:
            self.period = period
        if statistics is not None:
            self.statistics = statistics
        if threshold is not None:
            self.threshold = threshold

    @property
    def comparison_operator(self):
        """Gets the comparison_operator of this ConditionForListRulesByIdsOutput.  # noqa: E501


        :return: The comparison_operator of this ConditionForListRulesByIdsOutput.  # noqa: E501
        :rtype: str
        """
        return self._comparison_operator

    @comparison_operator.setter
    def comparison_operator(self, comparison_operator):
        """Sets the comparison_operator of this ConditionForListRulesByIdsOutput.


        :param comparison_operator: The comparison_operator of this ConditionForListRulesByIdsOutput.  # noqa: E501
        :type: str
        """

        self._comparison_operator = comparison_operator

    @property
    def display_name(self):
        """Gets the display_name of this ConditionForListRulesByIdsOutput.  # noqa: E501


        :return: The display_name of this ConditionForListRulesByIdsOutput.  # noqa: E501
        :rtype: str
        """
        return self._display_name

    @display_name.setter
    def display_name(self, display_name):
        """Sets the display_name of this ConditionForListRulesByIdsOutput.


        :param display_name: The display_name of this ConditionForListRulesByIdsOutput.  # noqa: E501
        :type: str
        """

        self._display_name = display_name

    @property
    def metric_name(self):
        """Gets the metric_name of this ConditionForListRulesByIdsOutput.  # noqa: E501


        :return: The metric_name of this ConditionForListRulesByIdsOutput.  # noqa: E501
        :rtype: str
        """
        return self._metric_name

    @metric_name.setter
    def metric_name(self, metric_name):
        """Sets the metric_name of this ConditionForListRulesByIdsOutput.


        :param metric_name: The metric_name of this ConditionForListRulesByIdsOutput.  # noqa: E501
        :type: str
        """

        self._metric_name = metric_name

    @property
    def metric_unit(self):
        """Gets the metric_unit of this ConditionForListRulesByIdsOutput.  # noqa: E501


        :return: The metric_unit of this ConditionForListRulesByIdsOutput.  # noqa: E501
        :rtype: str
        """
        return self._metric_unit

    @metric_unit.setter
    def metric_unit(self, metric_unit):
        """Sets the metric_unit of this ConditionForListRulesByIdsOutput.


        :param metric_unit: The metric_unit of this ConditionForListRulesByIdsOutput.  # noqa: E501
        :type: str
        """

        self._metric_unit = metric_unit

    @property
    def period(self):
        """Gets the period of this ConditionForListRulesByIdsOutput.  # noqa: E501


        :return: The period of this ConditionForListRulesByIdsOutput.  # noqa: E501
        :rtype: str
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this ConditionForListRulesByIdsOutput.


        :param period: The period of this ConditionForListRulesByIdsOutput.  # noqa: E501
        :type: str
        """

        self._period = period

    @property
    def statistics(self):
        """Gets the statistics of this ConditionForListRulesByIdsOutput.  # noqa: E501


        :return: The statistics of this ConditionForListRulesByIdsOutput.  # noqa: E501
        :rtype: str
        """
        return self._statistics

    @statistics.setter
    def statistics(self, statistics):
        """Sets the statistics of this ConditionForListRulesByIdsOutput.


        :param statistics: The statistics of this ConditionForListRulesByIdsOutput.  # noqa: E501
        :type: str
        """

        self._statistics = statistics

    @property
    def threshold(self):
        """Gets the threshold of this ConditionForListRulesByIdsOutput.  # noqa: E501


        :return: The threshold of this ConditionForListRulesByIdsOutput.  # noqa: E501
        :rtype: str
        """
        return self._threshold

    @threshold.setter
    def threshold(self, threshold):
        """Sets the threshold of this ConditionForListRulesByIdsOutput.


        :param threshold: The threshold of this ConditionForListRulesByIdsOutput.  # noqa: E501
        :type: str
        """

        self._threshold = threshold

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConditionForListRulesByIdsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConditionForListRulesByIdsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConditionForListRulesByIdsOutput):
            return True

        return self.to_dict() != other.to_dict()
