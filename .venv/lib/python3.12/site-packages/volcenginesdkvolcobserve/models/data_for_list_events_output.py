# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListEventsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'detail': 'dict(str, object)',
        'event_bus_name': 'str',
        'event_type': 'str',
        'happen_time': 'int',
        'id': 'str',
        'region': 'str',
        'source': 'str'
    }

    attribute_map = {
        'detail': 'Detail',
        'event_bus_name': 'EventBusName',
        'event_type': 'EventType',
        'happen_time': 'HappenTime',
        'id': 'ID',
        'region': 'Region',
        'source': 'Source'
    }

    def __init__(self, detail=None, event_bus_name=None, event_type=None, happen_time=None, id=None, region=None, source=None, _configuration=None):  # noqa: E501
        """DataForListEventsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._detail = None
        self._event_bus_name = None
        self._event_type = None
        self._happen_time = None
        self._id = None
        self._region = None
        self._source = None
        self.discriminator = None

        if detail is not None:
            self.detail = detail
        if event_bus_name is not None:
            self.event_bus_name = event_bus_name
        if event_type is not None:
            self.event_type = event_type
        if happen_time is not None:
            self.happen_time = happen_time
        if id is not None:
            self.id = id
        if region is not None:
            self.region = region
        if source is not None:
            self.source = source

    @property
    def detail(self):
        """Gets the detail of this DataForListEventsOutput.  # noqa: E501


        :return: The detail of this DataForListEventsOutput.  # noqa: E501
        :rtype: dict(str, object)
        """
        return self._detail

    @detail.setter
    def detail(self, detail):
        """Sets the detail of this DataForListEventsOutput.


        :param detail: The detail of this DataForListEventsOutput.  # noqa: E501
        :type: dict(str, object)
        """

        self._detail = detail

    @property
    def event_bus_name(self):
        """Gets the event_bus_name of this DataForListEventsOutput.  # noqa: E501


        :return: The event_bus_name of this DataForListEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._event_bus_name

    @event_bus_name.setter
    def event_bus_name(self, event_bus_name):
        """Sets the event_bus_name of this DataForListEventsOutput.


        :param event_bus_name: The event_bus_name of this DataForListEventsOutput.  # noqa: E501
        :type: str
        """

        self._event_bus_name = event_bus_name

    @property
    def event_type(self):
        """Gets the event_type of this DataForListEventsOutput.  # noqa: E501


        :return: The event_type of this DataForListEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._event_type

    @event_type.setter
    def event_type(self, event_type):
        """Sets the event_type of this DataForListEventsOutput.


        :param event_type: The event_type of this DataForListEventsOutput.  # noqa: E501
        :type: str
        """

        self._event_type = event_type

    @property
    def happen_time(self):
        """Gets the happen_time of this DataForListEventsOutput.  # noqa: E501


        :return: The happen_time of this DataForListEventsOutput.  # noqa: E501
        :rtype: int
        """
        return self._happen_time

    @happen_time.setter
    def happen_time(self, happen_time):
        """Sets the happen_time of this DataForListEventsOutput.


        :param happen_time: The happen_time of this DataForListEventsOutput.  # noqa: E501
        :type: int
        """

        self._happen_time = happen_time

    @property
    def id(self):
        """Gets the id of this DataForListEventsOutput.  # noqa: E501


        :return: The id of this DataForListEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListEventsOutput.


        :param id: The id of this DataForListEventsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def region(self):
        """Gets the region of this DataForListEventsOutput.  # noqa: E501


        :return: The region of this DataForListEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DataForListEventsOutput.


        :param region: The region of this DataForListEventsOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def source(self):
        """Gets the source of this DataForListEventsOutput.  # noqa: E501


        :return: The source of this DataForListEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this DataForListEventsOutput.


        :param source: The source of this DataForListEventsOutput.  # noqa: E501
        :type: str
        """

        self._source = source

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListEventsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListEventsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListEventsOutput):
            return True

        return self.to_dict() != other.to_dict()
