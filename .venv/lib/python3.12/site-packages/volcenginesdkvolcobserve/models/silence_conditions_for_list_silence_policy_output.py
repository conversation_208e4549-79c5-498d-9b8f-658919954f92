# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SilenceConditionsForListSilencePolicyOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'event_type': 'list[str]',
        'meta_condition': 'MetaConditionForListSilencePolicyOutput',
        'rule_id': 'str',
        'rule_name': 'str'
    }

    attribute_map = {
        'event_type': 'EventType',
        'meta_condition': 'MetaCondition',
        'rule_id': 'RuleId',
        'rule_name': 'RuleName'
    }

    def __init__(self, event_type=None, meta_condition=None, rule_id=None, rule_name=None, _configuration=None):  # noqa: E501
        """SilenceConditionsForListSilencePolicyOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._event_type = None
        self._meta_condition = None
        self._rule_id = None
        self._rule_name = None
        self.discriminator = None

        if event_type is not None:
            self.event_type = event_type
        if meta_condition is not None:
            self.meta_condition = meta_condition
        if rule_id is not None:
            self.rule_id = rule_id
        if rule_name is not None:
            self.rule_name = rule_name

    @property
    def event_type(self):
        """Gets the event_type of this SilenceConditionsForListSilencePolicyOutput.  # noqa: E501


        :return: The event_type of this SilenceConditionsForListSilencePolicyOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._event_type

    @event_type.setter
    def event_type(self, event_type):
        """Sets the event_type of this SilenceConditionsForListSilencePolicyOutput.


        :param event_type: The event_type of this SilenceConditionsForListSilencePolicyOutput.  # noqa: E501
        :type: list[str]
        """

        self._event_type = event_type

    @property
    def meta_condition(self):
        """Gets the meta_condition of this SilenceConditionsForListSilencePolicyOutput.  # noqa: E501


        :return: The meta_condition of this SilenceConditionsForListSilencePolicyOutput.  # noqa: E501
        :rtype: MetaConditionForListSilencePolicyOutput
        """
        return self._meta_condition

    @meta_condition.setter
    def meta_condition(self, meta_condition):
        """Sets the meta_condition of this SilenceConditionsForListSilencePolicyOutput.


        :param meta_condition: The meta_condition of this SilenceConditionsForListSilencePolicyOutput.  # noqa: E501
        :type: MetaConditionForListSilencePolicyOutput
        """

        self._meta_condition = meta_condition

    @property
    def rule_id(self):
        """Gets the rule_id of this SilenceConditionsForListSilencePolicyOutput.  # noqa: E501


        :return: The rule_id of this SilenceConditionsForListSilencePolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this SilenceConditionsForListSilencePolicyOutput.


        :param rule_id: The rule_id of this SilenceConditionsForListSilencePolicyOutput.  # noqa: E501
        :type: str
        """

        self._rule_id = rule_id

    @property
    def rule_name(self):
        """Gets the rule_name of this SilenceConditionsForListSilencePolicyOutput.  # noqa: E501


        :return: The rule_name of this SilenceConditionsForListSilencePolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_name

    @rule_name.setter
    def rule_name(self, rule_name):
        """Sets the rule_name of this SilenceConditionsForListSilencePolicyOutput.


        :param rule_name: The rule_name of this SilenceConditionsForListSilencePolicyOutput.  # noqa: E501
        :type: str
        """

        self._rule_name = rule_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SilenceConditionsForListSilencePolicyOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SilenceConditionsForListSilencePolicyOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SilenceConditionsForListSilencePolicyOutput):
            return True

        return self.to_dict() != other.to_dict()
