# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EnablePresetAlertTemplateRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_methods': 'list[str]',
        'contact_group_ids': 'list[str]',
        'effect_end_at': 'str',
        'effect_start_at': 'str',
        'notification_id': 'str',
        'project_name': 'str',
        'template_id': 'str',
        'upgrade_preset_alert_template': 'bool',
        'webhook': 'str',
        'webhook_ids': 'list[str]'
    }

    attribute_map = {
        'alert_methods': 'AlertMethods',
        'contact_group_ids': 'ContactGroupIds',
        'effect_end_at': 'EffectEndAt',
        'effect_start_at': 'EffectStartAt',
        'notification_id': 'NotificationId',
        'project_name': 'ProjectName',
        'template_id': 'TemplateId',
        'upgrade_preset_alert_template': 'UpgradePresetAlertTemplate',
        'webhook': 'Webhook',
        'webhook_ids': 'WebhookIds'
    }

    def __init__(self, alert_methods=None, contact_group_ids=None, effect_end_at=None, effect_start_at=None, notification_id=None, project_name=None, template_id=None, upgrade_preset_alert_template=None, webhook=None, webhook_ids=None, _configuration=None):  # noqa: E501
        """EnablePresetAlertTemplateRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_methods = None
        self._contact_group_ids = None
        self._effect_end_at = None
        self._effect_start_at = None
        self._notification_id = None
        self._project_name = None
        self._template_id = None
        self._upgrade_preset_alert_template = None
        self._webhook = None
        self._webhook_ids = None
        self.discriminator = None

        if alert_methods is not None:
            self.alert_methods = alert_methods
        if contact_group_ids is not None:
            self.contact_group_ids = contact_group_ids
        self.effect_end_at = effect_end_at
        self.effect_start_at = effect_start_at
        if notification_id is not None:
            self.notification_id = notification_id
        self.project_name = project_name
        self.template_id = template_id
        if upgrade_preset_alert_template is not None:
            self.upgrade_preset_alert_template = upgrade_preset_alert_template
        if webhook is not None:
            self.webhook = webhook
        if webhook_ids is not None:
            self.webhook_ids = webhook_ids

    @property
    def alert_methods(self):
        """Gets the alert_methods of this EnablePresetAlertTemplateRequest.  # noqa: E501


        :return: The alert_methods of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._alert_methods

    @alert_methods.setter
    def alert_methods(self, alert_methods):
        """Sets the alert_methods of this EnablePresetAlertTemplateRequest.


        :param alert_methods: The alert_methods of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :type: list[str]
        """

        self._alert_methods = alert_methods

    @property
    def contact_group_ids(self):
        """Gets the contact_group_ids of this EnablePresetAlertTemplateRequest.  # noqa: E501


        :return: The contact_group_ids of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._contact_group_ids

    @contact_group_ids.setter
    def contact_group_ids(self, contact_group_ids):
        """Sets the contact_group_ids of this EnablePresetAlertTemplateRequest.


        :param contact_group_ids: The contact_group_ids of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :type: list[str]
        """

        self._contact_group_ids = contact_group_ids

    @property
    def effect_end_at(self):
        """Gets the effect_end_at of this EnablePresetAlertTemplateRequest.  # noqa: E501


        :return: The effect_end_at of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._effect_end_at

    @effect_end_at.setter
    def effect_end_at(self, effect_end_at):
        """Sets the effect_end_at of this EnablePresetAlertTemplateRequest.


        :param effect_end_at: The effect_end_at of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and effect_end_at is None:
            raise ValueError("Invalid value for `effect_end_at`, must not be `None`")  # noqa: E501

        self._effect_end_at = effect_end_at

    @property
    def effect_start_at(self):
        """Gets the effect_start_at of this EnablePresetAlertTemplateRequest.  # noqa: E501


        :return: The effect_start_at of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._effect_start_at

    @effect_start_at.setter
    def effect_start_at(self, effect_start_at):
        """Sets the effect_start_at of this EnablePresetAlertTemplateRequest.


        :param effect_start_at: The effect_start_at of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and effect_start_at is None:
            raise ValueError("Invalid value for `effect_start_at`, must not be `None`")  # noqa: E501

        self._effect_start_at = effect_start_at

    @property
    def notification_id(self):
        """Gets the notification_id of this EnablePresetAlertTemplateRequest.  # noqa: E501


        :return: The notification_id of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._notification_id

    @notification_id.setter
    def notification_id(self, notification_id):
        """Sets the notification_id of this EnablePresetAlertTemplateRequest.


        :param notification_id: The notification_id of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :type: str
        """

        self._notification_id = notification_id

    @property
    def project_name(self):
        """Gets the project_name of this EnablePresetAlertTemplateRequest.  # noqa: E501


        :return: The project_name of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this EnablePresetAlertTemplateRequest.


        :param project_name: The project_name of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and project_name is None:
            raise ValueError("Invalid value for `project_name`, must not be `None`")  # noqa: E501

        self._project_name = project_name

    @property
    def template_id(self):
        """Gets the template_id of this EnablePresetAlertTemplateRequest.  # noqa: E501


        :return: The template_id of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._template_id

    @template_id.setter
    def template_id(self, template_id):
        """Sets the template_id of this EnablePresetAlertTemplateRequest.


        :param template_id: The template_id of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and template_id is None:
            raise ValueError("Invalid value for `template_id`, must not be `None`")  # noqa: E501

        self._template_id = template_id

    @property
    def upgrade_preset_alert_template(self):
        """Gets the upgrade_preset_alert_template of this EnablePresetAlertTemplateRequest.  # noqa: E501


        :return: The upgrade_preset_alert_template of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :rtype: bool
        """
        return self._upgrade_preset_alert_template

    @upgrade_preset_alert_template.setter
    def upgrade_preset_alert_template(self, upgrade_preset_alert_template):
        """Sets the upgrade_preset_alert_template of this EnablePresetAlertTemplateRequest.


        :param upgrade_preset_alert_template: The upgrade_preset_alert_template of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :type: bool
        """

        self._upgrade_preset_alert_template = upgrade_preset_alert_template

    @property
    def webhook(self):
        """Gets the webhook of this EnablePresetAlertTemplateRequest.  # noqa: E501


        :return: The webhook of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._webhook

    @webhook.setter
    def webhook(self, webhook):
        """Sets the webhook of this EnablePresetAlertTemplateRequest.


        :param webhook: The webhook of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :type: str
        """

        self._webhook = webhook

    @property
    def webhook_ids(self):
        """Gets the webhook_ids of this EnablePresetAlertTemplateRequest.  # noqa: E501


        :return: The webhook_ids of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._webhook_ids

    @webhook_ids.setter
    def webhook_ids(self, webhook_ids):
        """Sets the webhook_ids of this EnablePresetAlertTemplateRequest.


        :param webhook_ids: The webhook_ids of this EnablePresetAlertTemplateRequest.  # noqa: E501
        :type: list[str]
        """

        self._webhook_ids = webhook_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EnablePresetAlertTemplateRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EnablePresetAlertTemplateRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EnablePresetAlertTemplateRequest):
            return True

        return self.to_dict() != other.to_dict()
