# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MetaConditionForUpdateObjectGroupInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'all_dimensions': 'bool',
        'condition': 'str',
        'metas': 'list[MetaForUpdateObjectGroupInput]'
    }

    attribute_map = {
        'all_dimensions': 'AllDimensions',
        'condition': 'Condition',
        'metas': 'Metas'
    }

    def __init__(self, all_dimensions=None, condition=None, metas=None, _configuration=None):  # noqa: E501
        """MetaConditionForUpdateObjectGroupInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._all_dimensions = None
        self._condition = None
        self._metas = None
        self.discriminator = None

        if all_dimensions is not None:
            self.all_dimensions = all_dimensions
        if condition is not None:
            self.condition = condition
        if metas is not None:
            self.metas = metas

    @property
    def all_dimensions(self):
        """Gets the all_dimensions of this MetaConditionForUpdateObjectGroupInput.  # noqa: E501


        :return: The all_dimensions of this MetaConditionForUpdateObjectGroupInput.  # noqa: E501
        :rtype: bool
        """
        return self._all_dimensions

    @all_dimensions.setter
    def all_dimensions(self, all_dimensions):
        """Sets the all_dimensions of this MetaConditionForUpdateObjectGroupInput.


        :param all_dimensions: The all_dimensions of this MetaConditionForUpdateObjectGroupInput.  # noqa: E501
        :type: bool
        """

        self._all_dimensions = all_dimensions

    @property
    def condition(self):
        """Gets the condition of this MetaConditionForUpdateObjectGroupInput.  # noqa: E501


        :return: The condition of this MetaConditionForUpdateObjectGroupInput.  # noqa: E501
        :rtype: str
        """
        return self._condition

    @condition.setter
    def condition(self, condition):
        """Sets the condition of this MetaConditionForUpdateObjectGroupInput.


        :param condition: The condition of this MetaConditionForUpdateObjectGroupInput.  # noqa: E501
        :type: str
        """

        self._condition = condition

    @property
    def metas(self):
        """Gets the metas of this MetaConditionForUpdateObjectGroupInput.  # noqa: E501


        :return: The metas of this MetaConditionForUpdateObjectGroupInput.  # noqa: E501
        :rtype: list[MetaForUpdateObjectGroupInput]
        """
        return self._metas

    @metas.setter
    def metas(self, metas):
        """Sets the metas of this MetaConditionForUpdateObjectGroupInput.


        :param metas: The metas of this MetaConditionForUpdateObjectGroupInput.  # noqa: E501
        :type: list[MetaForUpdateObjectGroupInput]
        """

        self._metas = metas

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MetaConditionForUpdateObjectGroupInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MetaConditionForUpdateObjectGroupInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MetaConditionForUpdateObjectGroupInput):
            return True

        return self.to_dict() != other.to_dict()
