# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetTopDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asc': 'bool',
        'order_by_metric_name': 'str',
        'top_data_results': 'list[TopDataResultForGetTopDataOutput]'
    }

    attribute_map = {
        'asc': 'Asc',
        'order_by_metric_name': 'OrderByMetricName',
        'top_data_results': 'TopDataResults'
    }

    def __init__(self, asc=None, order_by_metric_name=None, top_data_results=None, _configuration=None):  # noqa: E501
        """DataForGetTopDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asc = None
        self._order_by_metric_name = None
        self._top_data_results = None
        self.discriminator = None

        if asc is not None:
            self.asc = asc
        if order_by_metric_name is not None:
            self.order_by_metric_name = order_by_metric_name
        if top_data_results is not None:
            self.top_data_results = top_data_results

    @property
    def asc(self):
        """Gets the asc of this DataForGetTopDataOutput.  # noqa: E501


        :return: The asc of this DataForGetTopDataOutput.  # noqa: E501
        :rtype: bool
        """
        return self._asc

    @asc.setter
    def asc(self, asc):
        """Sets the asc of this DataForGetTopDataOutput.


        :param asc: The asc of this DataForGetTopDataOutput.  # noqa: E501
        :type: bool
        """

        self._asc = asc

    @property
    def order_by_metric_name(self):
        """Gets the order_by_metric_name of this DataForGetTopDataOutput.  # noqa: E501


        :return: The order_by_metric_name of this DataForGetTopDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._order_by_metric_name

    @order_by_metric_name.setter
    def order_by_metric_name(self, order_by_metric_name):
        """Sets the order_by_metric_name of this DataForGetTopDataOutput.


        :param order_by_metric_name: The order_by_metric_name of this DataForGetTopDataOutput.  # noqa: E501
        :type: str
        """

        self._order_by_metric_name = order_by_metric_name

    @property
    def top_data_results(self):
        """Gets the top_data_results of this DataForGetTopDataOutput.  # noqa: E501


        :return: The top_data_results of this DataForGetTopDataOutput.  # noqa: E501
        :rtype: list[TopDataResultForGetTopDataOutput]
        """
        return self._top_data_results

    @top_data_results.setter
    def top_data_results(self, top_data_results):
        """Sets the top_data_results of this DataForGetTopDataOutput.


        :param top_data_results: The top_data_results of this DataForGetTopDataOutput.  # noqa: E501
        :type: list[TopDataResultForGetTopDataOutput]
        """

        self._top_data_results = top_data_results

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetTopDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetTopDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetTopDataOutput):
            return True

        return self.to_dict() != other.to_dict()
