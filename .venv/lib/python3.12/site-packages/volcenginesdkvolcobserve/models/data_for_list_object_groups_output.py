# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListObjectGroupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_template_id': 'str',
        'alert_template_name': 'str',
        'created_at': 'str',
        'id': 'str',
        'name': 'str',
        'objects': 'list[ObjectForListObjectGroupsOutput]',
        'updated_at': 'str'
    }

    attribute_map = {
        'alert_template_id': 'AlertTemplateId',
        'alert_template_name': 'AlertTemplateName',
        'created_at': 'CreatedAt',
        'id': 'Id',
        'name': 'Name',
        'objects': 'Objects',
        'updated_at': 'UpdatedAt'
    }

    def __init__(self, alert_template_id=None, alert_template_name=None, created_at=None, id=None, name=None, objects=None, updated_at=None, _configuration=None):  # noqa: E501
        """DataForListObjectGroupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_template_id = None
        self._alert_template_name = None
        self._created_at = None
        self._id = None
        self._name = None
        self._objects = None
        self._updated_at = None
        self.discriminator = None

        if alert_template_id is not None:
            self.alert_template_id = alert_template_id
        if alert_template_name is not None:
            self.alert_template_name = alert_template_name
        if created_at is not None:
            self.created_at = created_at
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if objects is not None:
            self.objects = objects
        if updated_at is not None:
            self.updated_at = updated_at

    @property
    def alert_template_id(self):
        """Gets the alert_template_id of this DataForListObjectGroupsOutput.  # noqa: E501


        :return: The alert_template_id of this DataForListObjectGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._alert_template_id

    @alert_template_id.setter
    def alert_template_id(self, alert_template_id):
        """Sets the alert_template_id of this DataForListObjectGroupsOutput.


        :param alert_template_id: The alert_template_id of this DataForListObjectGroupsOutput.  # noqa: E501
        :type: str
        """

        self._alert_template_id = alert_template_id

    @property
    def alert_template_name(self):
        """Gets the alert_template_name of this DataForListObjectGroupsOutput.  # noqa: E501


        :return: The alert_template_name of this DataForListObjectGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._alert_template_name

    @alert_template_name.setter
    def alert_template_name(self, alert_template_name):
        """Sets the alert_template_name of this DataForListObjectGroupsOutput.


        :param alert_template_name: The alert_template_name of this DataForListObjectGroupsOutput.  # noqa: E501
        :type: str
        """

        self._alert_template_name = alert_template_name

    @property
    def created_at(self):
        """Gets the created_at of this DataForListObjectGroupsOutput.  # noqa: E501


        :return: The created_at of this DataForListObjectGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this DataForListObjectGroupsOutput.


        :param created_at: The created_at of this DataForListObjectGroupsOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def id(self):
        """Gets the id of this DataForListObjectGroupsOutput.  # noqa: E501


        :return: The id of this DataForListObjectGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListObjectGroupsOutput.


        :param id: The id of this DataForListObjectGroupsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this DataForListObjectGroupsOutput.  # noqa: E501


        :return: The name of this DataForListObjectGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForListObjectGroupsOutput.


        :param name: The name of this DataForListObjectGroupsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def objects(self):
        """Gets the objects of this DataForListObjectGroupsOutput.  # noqa: E501


        :return: The objects of this DataForListObjectGroupsOutput.  # noqa: E501
        :rtype: list[ObjectForListObjectGroupsOutput]
        """
        return self._objects

    @objects.setter
    def objects(self, objects):
        """Sets the objects of this DataForListObjectGroupsOutput.


        :param objects: The objects of this DataForListObjectGroupsOutput.  # noqa: E501
        :type: list[ObjectForListObjectGroupsOutput]
        """

        self._objects = objects

    @property
    def updated_at(self):
        """Gets the updated_at of this DataForListObjectGroupsOutput.  # noqa: E501


        :return: The updated_at of this DataForListObjectGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this DataForListObjectGroupsOutput.


        :param updated_at: The updated_at of this DataForListObjectGroupsOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListObjectGroupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListObjectGroupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListObjectGroupsOutput):
            return True

        return self.to_dict() != other.to_dict()
