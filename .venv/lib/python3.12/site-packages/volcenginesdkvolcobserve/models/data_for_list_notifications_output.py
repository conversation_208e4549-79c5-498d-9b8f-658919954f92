# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListNotificationsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'str',
        'effect_end_at': 'str',
        'effect_start_at': 'str',
        'id': 'str',
        'levels': 'list[str]',
        'name': 'str',
        'notifications': 'list[NotificationForListNotificationsOutput]',
        'updated_at': 'str'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'effect_end_at': 'EffectEndAt',
        'effect_start_at': 'EffectStartAt',
        'id': 'Id',
        'levels': 'Levels',
        'name': 'Name',
        'notifications': 'Notifications',
        'updated_at': 'UpdatedAt'
    }

    def __init__(self, created_at=None, effect_end_at=None, effect_start_at=None, id=None, levels=None, name=None, notifications=None, updated_at=None, _configuration=None):  # noqa: E501
        """DataForListNotificationsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._effect_end_at = None
        self._effect_start_at = None
        self._id = None
        self._levels = None
        self._name = None
        self._notifications = None
        self._updated_at = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if effect_end_at is not None:
            self.effect_end_at = effect_end_at
        if effect_start_at is not None:
            self.effect_start_at = effect_start_at
        if id is not None:
            self.id = id
        if levels is not None:
            self.levels = levels
        if name is not None:
            self.name = name
        if notifications is not None:
            self.notifications = notifications
        if updated_at is not None:
            self.updated_at = updated_at

    @property
    def created_at(self):
        """Gets the created_at of this DataForListNotificationsOutput.  # noqa: E501


        :return: The created_at of this DataForListNotificationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this DataForListNotificationsOutput.


        :param created_at: The created_at of this DataForListNotificationsOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def effect_end_at(self):
        """Gets the effect_end_at of this DataForListNotificationsOutput.  # noqa: E501


        :return: The effect_end_at of this DataForListNotificationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._effect_end_at

    @effect_end_at.setter
    def effect_end_at(self, effect_end_at):
        """Sets the effect_end_at of this DataForListNotificationsOutput.


        :param effect_end_at: The effect_end_at of this DataForListNotificationsOutput.  # noqa: E501
        :type: str
        """

        self._effect_end_at = effect_end_at

    @property
    def effect_start_at(self):
        """Gets the effect_start_at of this DataForListNotificationsOutput.  # noqa: E501


        :return: The effect_start_at of this DataForListNotificationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._effect_start_at

    @effect_start_at.setter
    def effect_start_at(self, effect_start_at):
        """Sets the effect_start_at of this DataForListNotificationsOutput.


        :param effect_start_at: The effect_start_at of this DataForListNotificationsOutput.  # noqa: E501
        :type: str
        """

        self._effect_start_at = effect_start_at

    @property
    def id(self):
        """Gets the id of this DataForListNotificationsOutput.  # noqa: E501


        :return: The id of this DataForListNotificationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListNotificationsOutput.


        :param id: The id of this DataForListNotificationsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def levels(self):
        """Gets the levels of this DataForListNotificationsOutput.  # noqa: E501


        :return: The levels of this DataForListNotificationsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._levels

    @levels.setter
    def levels(self, levels):
        """Sets the levels of this DataForListNotificationsOutput.


        :param levels: The levels of this DataForListNotificationsOutput.  # noqa: E501
        :type: list[str]
        """

        self._levels = levels

    @property
    def name(self):
        """Gets the name of this DataForListNotificationsOutput.  # noqa: E501


        :return: The name of this DataForListNotificationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForListNotificationsOutput.


        :param name: The name of this DataForListNotificationsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def notifications(self):
        """Gets the notifications of this DataForListNotificationsOutput.  # noqa: E501


        :return: The notifications of this DataForListNotificationsOutput.  # noqa: E501
        :rtype: list[NotificationForListNotificationsOutput]
        """
        return self._notifications

    @notifications.setter
    def notifications(self, notifications):
        """Sets the notifications of this DataForListNotificationsOutput.


        :param notifications: The notifications of this DataForListNotificationsOutput.  # noqa: E501
        :type: list[NotificationForListNotificationsOutput]
        """

        self._notifications = notifications

    @property
    def updated_at(self):
        """Gets the updated_at of this DataForListNotificationsOutput.  # noqa: E501


        :return: The updated_at of this DataForListNotificationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this DataForListNotificationsOutput.


        :param updated_at: The updated_at of this DataForListNotificationsOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListNotificationsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListNotificationsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListNotificationsOutput):
            return True

        return self.to_dict() != other.to_dict()
