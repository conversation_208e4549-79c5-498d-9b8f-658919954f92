# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyStateOfSilencePolicyByIdsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ids': 'list[str]',
        'state': 'str'
    }

    attribute_map = {
        'ids': 'Ids',
        'state': 'State'
    }

    def __init__(self, ids=None, state=None, _configuration=None):  # noqa: E501
        """ModifyStateOfSilencePolicyByIdsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ids = None
        self._state = None
        self.discriminator = None

        if ids is not None:
            self.ids = ids
        self.state = state

    @property
    def ids(self):
        """Gets the ids of this ModifyStateOfSilencePolicyByIdsRequest.  # noqa: E501


        :return: The ids of this ModifyStateOfSilencePolicyByIdsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this ModifyStateOfSilencePolicyByIdsRequest.


        :param ids: The ids of this ModifyStateOfSilencePolicyByIdsRequest.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def state(self):
        """Gets the state of this ModifyStateOfSilencePolicyByIdsRequest.  # noqa: E501


        :return: The state of this ModifyStateOfSilencePolicyByIdsRequest.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this ModifyStateOfSilencePolicyByIdsRequest.


        :param state: The state of this ModifyStateOfSilencePolicyByIdsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and state is None:
            raise ValueError("Invalid value for `state`, must not be `None`")  # noqa: E501
        allowed_values = ["disable", "enable"]  # noqa: E501
        if (self._configuration.client_side_validation and
                state not in allowed_values):
            raise ValueError(
                "Invalid value for `state` ({0}), must be one of {1}"  # noqa: E501
                .format(state, allowed_values)
            )

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyStateOfSilencePolicyByIdsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyStateOfSilencePolicyByIdsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyStateOfSilencePolicyByIdsRequest):
            return True

        return self.to_dict() != other.to_dict()
