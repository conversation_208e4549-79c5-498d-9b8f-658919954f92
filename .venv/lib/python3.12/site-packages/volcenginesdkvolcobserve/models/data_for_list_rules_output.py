# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListRulesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_methods': 'list[str]',
        'alert_state': 'str',
        'condition_operator': 'str',
        'conditions': 'list[ConditionForListRulesOutput]',
        'contact_group_ids': 'list[str]',
        'created_at': 'str',
        'description': 'str',
        'dimension_conditions': 'DimensionConditionsForListRulesOutput',
        'effect_end_at': 'str',
        'effect_start_at': 'str',
        'enable_state': 'str',
        'evaluation_count': 'int',
        'id': 'str',
        'level': 'str',
        'level_conditions': 'list[LevelConditionForListRulesOutput]',
        'multiple_conditions': 'bool',
        'namespace': 'str',
        'notification_id': 'str',
        'original_dimensions': 'dict(str, list[str])',
        'project_name': 'str',
        'recovery_notify': 'RecoveryNotifyForListRulesOutput',
        'regions': 'list[str]',
        'rule_name': 'str',
        'rule_type': 'str',
        'silence_time': 'int',
        'sub_namespace': 'str',
        'tags': 'list[ConvertTagForListRulesOutput]',
        'updated_at': 'str',
        'web_hook': 'str',
        'webhook_ids': 'list[str]'
    }

    attribute_map = {
        'alert_methods': 'AlertMethods',
        'alert_state': 'AlertState',
        'condition_operator': 'ConditionOperator',
        'conditions': 'Conditions',
        'contact_group_ids': 'ContactGroupIds',
        'created_at': 'CreatedAt',
        'description': 'Description',
        'dimension_conditions': 'DimensionConditions',
        'effect_end_at': 'EffectEndAt',
        'effect_start_at': 'EffectStartAt',
        'enable_state': 'EnableState',
        'evaluation_count': 'EvaluationCount',
        'id': 'Id',
        'level': 'Level',
        'level_conditions': 'LevelConditions',
        'multiple_conditions': 'MultipleConditions',
        'namespace': 'Namespace',
        'notification_id': 'NotificationId',
        'original_dimensions': 'OriginalDimensions',
        'project_name': 'ProjectName',
        'recovery_notify': 'RecoveryNotify',
        'regions': 'Regions',
        'rule_name': 'RuleName',
        'rule_type': 'RuleType',
        'silence_time': 'SilenceTime',
        'sub_namespace': 'SubNamespace',
        'tags': 'Tags',
        'updated_at': 'UpdatedAt',
        'web_hook': 'WebHook',
        'webhook_ids': 'WebhookIds'
    }

    def __init__(self, alert_methods=None, alert_state=None, condition_operator=None, conditions=None, contact_group_ids=None, created_at=None, description=None, dimension_conditions=None, effect_end_at=None, effect_start_at=None, enable_state=None, evaluation_count=None, id=None, level=None, level_conditions=None, multiple_conditions=None, namespace=None, notification_id=None, original_dimensions=None, project_name=None, recovery_notify=None, regions=None, rule_name=None, rule_type=None, silence_time=None, sub_namespace=None, tags=None, updated_at=None, web_hook=None, webhook_ids=None, _configuration=None):  # noqa: E501
        """DataForListRulesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_methods = None
        self._alert_state = None
        self._condition_operator = None
        self._conditions = None
        self._contact_group_ids = None
        self._created_at = None
        self._description = None
        self._dimension_conditions = None
        self._effect_end_at = None
        self._effect_start_at = None
        self._enable_state = None
        self._evaluation_count = None
        self._id = None
        self._level = None
        self._level_conditions = None
        self._multiple_conditions = None
        self._namespace = None
        self._notification_id = None
        self._original_dimensions = None
        self._project_name = None
        self._recovery_notify = None
        self._regions = None
        self._rule_name = None
        self._rule_type = None
        self._silence_time = None
        self._sub_namespace = None
        self._tags = None
        self._updated_at = None
        self._web_hook = None
        self._webhook_ids = None
        self.discriminator = None

        if alert_methods is not None:
            self.alert_methods = alert_methods
        if alert_state is not None:
            self.alert_state = alert_state
        if condition_operator is not None:
            self.condition_operator = condition_operator
        if conditions is not None:
            self.conditions = conditions
        if contact_group_ids is not None:
            self.contact_group_ids = contact_group_ids
        if created_at is not None:
            self.created_at = created_at
        if description is not None:
            self.description = description
        if dimension_conditions is not None:
            self.dimension_conditions = dimension_conditions
        if effect_end_at is not None:
            self.effect_end_at = effect_end_at
        if effect_start_at is not None:
            self.effect_start_at = effect_start_at
        if enable_state is not None:
            self.enable_state = enable_state
        if evaluation_count is not None:
            self.evaluation_count = evaluation_count
        if id is not None:
            self.id = id
        if level is not None:
            self.level = level
        if level_conditions is not None:
            self.level_conditions = level_conditions
        if multiple_conditions is not None:
            self.multiple_conditions = multiple_conditions
        if namespace is not None:
            self.namespace = namespace
        if notification_id is not None:
            self.notification_id = notification_id
        if original_dimensions is not None:
            self.original_dimensions = original_dimensions
        if project_name is not None:
            self.project_name = project_name
        if recovery_notify is not None:
            self.recovery_notify = recovery_notify
        if regions is not None:
            self.regions = regions
        if rule_name is not None:
            self.rule_name = rule_name
        if rule_type is not None:
            self.rule_type = rule_type
        if silence_time is not None:
            self.silence_time = silence_time
        if sub_namespace is not None:
            self.sub_namespace = sub_namespace
        if tags is not None:
            self.tags = tags
        if updated_at is not None:
            self.updated_at = updated_at
        if web_hook is not None:
            self.web_hook = web_hook
        if webhook_ids is not None:
            self.webhook_ids = webhook_ids

    @property
    def alert_methods(self):
        """Gets the alert_methods of this DataForListRulesOutput.  # noqa: E501


        :return: The alert_methods of this DataForListRulesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._alert_methods

    @alert_methods.setter
    def alert_methods(self, alert_methods):
        """Sets the alert_methods of this DataForListRulesOutput.


        :param alert_methods: The alert_methods of this DataForListRulesOutput.  # noqa: E501
        :type: list[str]
        """

        self._alert_methods = alert_methods

    @property
    def alert_state(self):
        """Gets the alert_state of this DataForListRulesOutput.  # noqa: E501


        :return: The alert_state of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._alert_state

    @alert_state.setter
    def alert_state(self, alert_state):
        """Sets the alert_state of this DataForListRulesOutput.


        :param alert_state: The alert_state of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._alert_state = alert_state

    @property
    def condition_operator(self):
        """Gets the condition_operator of this DataForListRulesOutput.  # noqa: E501


        :return: The condition_operator of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._condition_operator

    @condition_operator.setter
    def condition_operator(self, condition_operator):
        """Sets the condition_operator of this DataForListRulesOutput.


        :param condition_operator: The condition_operator of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._condition_operator = condition_operator

    @property
    def conditions(self):
        """Gets the conditions of this DataForListRulesOutput.  # noqa: E501


        :return: The conditions of this DataForListRulesOutput.  # noqa: E501
        :rtype: list[ConditionForListRulesOutput]
        """
        return self._conditions

    @conditions.setter
    def conditions(self, conditions):
        """Sets the conditions of this DataForListRulesOutput.


        :param conditions: The conditions of this DataForListRulesOutput.  # noqa: E501
        :type: list[ConditionForListRulesOutput]
        """

        self._conditions = conditions

    @property
    def contact_group_ids(self):
        """Gets the contact_group_ids of this DataForListRulesOutput.  # noqa: E501


        :return: The contact_group_ids of this DataForListRulesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._contact_group_ids

    @contact_group_ids.setter
    def contact_group_ids(self, contact_group_ids):
        """Sets the contact_group_ids of this DataForListRulesOutput.


        :param contact_group_ids: The contact_group_ids of this DataForListRulesOutput.  # noqa: E501
        :type: list[str]
        """

        self._contact_group_ids = contact_group_ids

    @property
    def created_at(self):
        """Gets the created_at of this DataForListRulesOutput.  # noqa: E501


        :return: The created_at of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this DataForListRulesOutput.


        :param created_at: The created_at of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def description(self):
        """Gets the description of this DataForListRulesOutput.  # noqa: E501


        :return: The description of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DataForListRulesOutput.


        :param description: The description of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dimension_conditions(self):
        """Gets the dimension_conditions of this DataForListRulesOutput.  # noqa: E501


        :return: The dimension_conditions of this DataForListRulesOutput.  # noqa: E501
        :rtype: DimensionConditionsForListRulesOutput
        """
        return self._dimension_conditions

    @dimension_conditions.setter
    def dimension_conditions(self, dimension_conditions):
        """Sets the dimension_conditions of this DataForListRulesOutput.


        :param dimension_conditions: The dimension_conditions of this DataForListRulesOutput.  # noqa: E501
        :type: DimensionConditionsForListRulesOutput
        """

        self._dimension_conditions = dimension_conditions

    @property
    def effect_end_at(self):
        """Gets the effect_end_at of this DataForListRulesOutput.  # noqa: E501


        :return: The effect_end_at of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._effect_end_at

    @effect_end_at.setter
    def effect_end_at(self, effect_end_at):
        """Sets the effect_end_at of this DataForListRulesOutput.


        :param effect_end_at: The effect_end_at of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._effect_end_at = effect_end_at

    @property
    def effect_start_at(self):
        """Gets the effect_start_at of this DataForListRulesOutput.  # noqa: E501


        :return: The effect_start_at of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._effect_start_at

    @effect_start_at.setter
    def effect_start_at(self, effect_start_at):
        """Sets the effect_start_at of this DataForListRulesOutput.


        :param effect_start_at: The effect_start_at of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._effect_start_at = effect_start_at

    @property
    def enable_state(self):
        """Gets the enable_state of this DataForListRulesOutput.  # noqa: E501


        :return: The enable_state of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._enable_state

    @enable_state.setter
    def enable_state(self, enable_state):
        """Sets the enable_state of this DataForListRulesOutput.


        :param enable_state: The enable_state of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._enable_state = enable_state

    @property
    def evaluation_count(self):
        """Gets the evaluation_count of this DataForListRulesOutput.  # noqa: E501


        :return: The evaluation_count of this DataForListRulesOutput.  # noqa: E501
        :rtype: int
        """
        return self._evaluation_count

    @evaluation_count.setter
    def evaluation_count(self, evaluation_count):
        """Sets the evaluation_count of this DataForListRulesOutput.


        :param evaluation_count: The evaluation_count of this DataForListRulesOutput.  # noqa: E501
        :type: int
        """

        self._evaluation_count = evaluation_count

    @property
    def id(self):
        """Gets the id of this DataForListRulesOutput.  # noqa: E501


        :return: The id of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListRulesOutput.


        :param id: The id of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def level(self):
        """Gets the level of this DataForListRulesOutput.  # noqa: E501


        :return: The level of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this DataForListRulesOutput.


        :param level: The level of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def level_conditions(self):
        """Gets the level_conditions of this DataForListRulesOutput.  # noqa: E501


        :return: The level_conditions of this DataForListRulesOutput.  # noqa: E501
        :rtype: list[LevelConditionForListRulesOutput]
        """
        return self._level_conditions

    @level_conditions.setter
    def level_conditions(self, level_conditions):
        """Sets the level_conditions of this DataForListRulesOutput.


        :param level_conditions: The level_conditions of this DataForListRulesOutput.  # noqa: E501
        :type: list[LevelConditionForListRulesOutput]
        """

        self._level_conditions = level_conditions

    @property
    def multiple_conditions(self):
        """Gets the multiple_conditions of this DataForListRulesOutput.  # noqa: E501


        :return: The multiple_conditions of this DataForListRulesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._multiple_conditions

    @multiple_conditions.setter
    def multiple_conditions(self, multiple_conditions):
        """Sets the multiple_conditions of this DataForListRulesOutput.


        :param multiple_conditions: The multiple_conditions of this DataForListRulesOutput.  # noqa: E501
        :type: bool
        """

        self._multiple_conditions = multiple_conditions

    @property
    def namespace(self):
        """Gets the namespace of this DataForListRulesOutput.  # noqa: E501


        :return: The namespace of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this DataForListRulesOutput.


        :param namespace: The namespace of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    @property
    def notification_id(self):
        """Gets the notification_id of this DataForListRulesOutput.  # noqa: E501


        :return: The notification_id of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._notification_id

    @notification_id.setter
    def notification_id(self, notification_id):
        """Sets the notification_id of this DataForListRulesOutput.


        :param notification_id: The notification_id of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._notification_id = notification_id

    @property
    def original_dimensions(self):
        """Gets the original_dimensions of this DataForListRulesOutput.  # noqa: E501


        :return: The original_dimensions of this DataForListRulesOutput.  # noqa: E501
        :rtype: dict(str, list[str])
        """
        return self._original_dimensions

    @original_dimensions.setter
    def original_dimensions(self, original_dimensions):
        """Sets the original_dimensions of this DataForListRulesOutput.


        :param original_dimensions: The original_dimensions of this DataForListRulesOutput.  # noqa: E501
        :type: dict(str, list[str])
        """

        self._original_dimensions = original_dimensions

    @property
    def project_name(self):
        """Gets the project_name of this DataForListRulesOutput.  # noqa: E501


        :return: The project_name of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DataForListRulesOutput.


        :param project_name: The project_name of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def recovery_notify(self):
        """Gets the recovery_notify of this DataForListRulesOutput.  # noqa: E501


        :return: The recovery_notify of this DataForListRulesOutput.  # noqa: E501
        :rtype: RecoveryNotifyForListRulesOutput
        """
        return self._recovery_notify

    @recovery_notify.setter
    def recovery_notify(self, recovery_notify):
        """Sets the recovery_notify of this DataForListRulesOutput.


        :param recovery_notify: The recovery_notify of this DataForListRulesOutput.  # noqa: E501
        :type: RecoveryNotifyForListRulesOutput
        """

        self._recovery_notify = recovery_notify

    @property
    def regions(self):
        """Gets the regions of this DataForListRulesOutput.  # noqa: E501


        :return: The regions of this DataForListRulesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._regions

    @regions.setter
    def regions(self, regions):
        """Sets the regions of this DataForListRulesOutput.


        :param regions: The regions of this DataForListRulesOutput.  # noqa: E501
        :type: list[str]
        """

        self._regions = regions

    @property
    def rule_name(self):
        """Gets the rule_name of this DataForListRulesOutput.  # noqa: E501


        :return: The rule_name of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_name

    @rule_name.setter
    def rule_name(self, rule_name):
        """Sets the rule_name of this DataForListRulesOutput.


        :param rule_name: The rule_name of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._rule_name = rule_name

    @property
    def rule_type(self):
        """Gets the rule_type of this DataForListRulesOutput.  # noqa: E501


        :return: The rule_type of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_type

    @rule_type.setter
    def rule_type(self, rule_type):
        """Sets the rule_type of this DataForListRulesOutput.


        :param rule_type: The rule_type of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._rule_type = rule_type

    @property
    def silence_time(self):
        """Gets the silence_time of this DataForListRulesOutput.  # noqa: E501


        :return: The silence_time of this DataForListRulesOutput.  # noqa: E501
        :rtype: int
        """
        return self._silence_time

    @silence_time.setter
    def silence_time(self, silence_time):
        """Sets the silence_time of this DataForListRulesOutput.


        :param silence_time: The silence_time of this DataForListRulesOutput.  # noqa: E501
        :type: int
        """

        self._silence_time = silence_time

    @property
    def sub_namespace(self):
        """Gets the sub_namespace of this DataForListRulesOutput.  # noqa: E501


        :return: The sub_namespace of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._sub_namespace

    @sub_namespace.setter
    def sub_namespace(self, sub_namespace):
        """Sets the sub_namespace of this DataForListRulesOutput.


        :param sub_namespace: The sub_namespace of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._sub_namespace = sub_namespace

    @property
    def tags(self):
        """Gets the tags of this DataForListRulesOutput.  # noqa: E501


        :return: The tags of this DataForListRulesOutput.  # noqa: E501
        :rtype: list[ConvertTagForListRulesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DataForListRulesOutput.


        :param tags: The tags of this DataForListRulesOutput.  # noqa: E501
        :type: list[ConvertTagForListRulesOutput]
        """

        self._tags = tags

    @property
    def updated_at(self):
        """Gets the updated_at of this DataForListRulesOutput.  # noqa: E501


        :return: The updated_at of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this DataForListRulesOutput.


        :param updated_at: The updated_at of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    @property
    def web_hook(self):
        """Gets the web_hook of this DataForListRulesOutput.  # noqa: E501


        :return: The web_hook of this DataForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._web_hook

    @web_hook.setter
    def web_hook(self, web_hook):
        """Sets the web_hook of this DataForListRulesOutput.


        :param web_hook: The web_hook of this DataForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._web_hook = web_hook

    @property
    def webhook_ids(self):
        """Gets the webhook_ids of this DataForListRulesOutput.  # noqa: E501


        :return: The webhook_ids of this DataForListRulesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._webhook_ids

    @webhook_ids.setter
    def webhook_ids(self, webhook_ids):
        """Sets the webhook_ids of this DataForListRulesOutput.


        :param webhook_ids: The webhook_ids of this DataForListRulesOutput.  # noqa: E501
        :type: list[str]
        """

        self._webhook_ids = webhook_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListRulesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListRulesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListRulesOutput):
            return True

        return self.to_dict() != other.to_dict()
