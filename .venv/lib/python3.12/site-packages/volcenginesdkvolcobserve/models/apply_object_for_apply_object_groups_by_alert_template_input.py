# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ApplyObjectForApplyObjectGroupsByAlertTemplateInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_methods': 'list[str]',
        'contact_group_ids': 'list[str]',
        'effect_end_at': 'str',
        'effect_start_at': 'str',
        'notification_id': 'str',
        'object_group_id': 'str',
        'webhook': 'str',
        'webhook_ids': 'list[str]'
    }

    attribute_map = {
        'alert_methods': 'AlertMethods',
        'contact_group_ids': 'ContactGroupIds',
        'effect_end_at': 'EffectEndAt',
        'effect_start_at': 'EffectStartAt',
        'notification_id': 'NotificationId',
        'object_group_id': 'ObjectGroupId',
        'webhook': 'Webhook',
        'webhook_ids': 'WebhookIds'
    }

    def __init__(self, alert_methods=None, contact_group_ids=None, effect_end_at=None, effect_start_at=None, notification_id=None, object_group_id=None, webhook=None, webhook_ids=None, _configuration=None):  # noqa: E501
        """ApplyObjectForApplyObjectGroupsByAlertTemplateInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_methods = None
        self._contact_group_ids = None
        self._effect_end_at = None
        self._effect_start_at = None
        self._notification_id = None
        self._object_group_id = None
        self._webhook = None
        self._webhook_ids = None
        self.discriminator = None

        if alert_methods is not None:
            self.alert_methods = alert_methods
        if contact_group_ids is not None:
            self.contact_group_ids = contact_group_ids
        if effect_end_at is not None:
            self.effect_end_at = effect_end_at
        if effect_start_at is not None:
            self.effect_start_at = effect_start_at
        if notification_id is not None:
            self.notification_id = notification_id
        if object_group_id is not None:
            self.object_group_id = object_group_id
        if webhook is not None:
            self.webhook = webhook
        if webhook_ids is not None:
            self.webhook_ids = webhook_ids

    @property
    def alert_methods(self):
        """Gets the alert_methods of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501


        :return: The alert_methods of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._alert_methods

    @alert_methods.setter
    def alert_methods(self, alert_methods):
        """Sets the alert_methods of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.


        :param alert_methods: The alert_methods of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :type: list[str]
        """

        self._alert_methods = alert_methods

    @property
    def contact_group_ids(self):
        """Gets the contact_group_ids of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501


        :return: The contact_group_ids of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._contact_group_ids

    @contact_group_ids.setter
    def contact_group_ids(self, contact_group_ids):
        """Sets the contact_group_ids of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.


        :param contact_group_ids: The contact_group_ids of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :type: list[str]
        """

        self._contact_group_ids = contact_group_ids

    @property
    def effect_end_at(self):
        """Gets the effect_end_at of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501


        :return: The effect_end_at of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :rtype: str
        """
        return self._effect_end_at

    @effect_end_at.setter
    def effect_end_at(self, effect_end_at):
        """Sets the effect_end_at of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.


        :param effect_end_at: The effect_end_at of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :type: str
        """

        self._effect_end_at = effect_end_at

    @property
    def effect_start_at(self):
        """Gets the effect_start_at of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501


        :return: The effect_start_at of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :rtype: str
        """
        return self._effect_start_at

    @effect_start_at.setter
    def effect_start_at(self, effect_start_at):
        """Sets the effect_start_at of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.


        :param effect_start_at: The effect_start_at of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :type: str
        """

        self._effect_start_at = effect_start_at

    @property
    def notification_id(self):
        """Gets the notification_id of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501


        :return: The notification_id of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :rtype: str
        """
        return self._notification_id

    @notification_id.setter
    def notification_id(self, notification_id):
        """Sets the notification_id of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.


        :param notification_id: The notification_id of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :type: str
        """

        self._notification_id = notification_id

    @property
    def object_group_id(self):
        """Gets the object_group_id of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501


        :return: The object_group_id of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :rtype: str
        """
        return self._object_group_id

    @object_group_id.setter
    def object_group_id(self, object_group_id):
        """Sets the object_group_id of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.


        :param object_group_id: The object_group_id of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :type: str
        """

        self._object_group_id = object_group_id

    @property
    def webhook(self):
        """Gets the webhook of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501


        :return: The webhook of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :rtype: str
        """
        return self._webhook

    @webhook.setter
    def webhook(self, webhook):
        """Sets the webhook of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.


        :param webhook: The webhook of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :type: str
        """

        self._webhook = webhook

    @property
    def webhook_ids(self):
        """Gets the webhook_ids of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501


        :return: The webhook_ids of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._webhook_ids

    @webhook_ids.setter
    def webhook_ids(self, webhook_ids):
        """Sets the webhook_ids of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.


        :param webhook_ids: The webhook_ids of this ApplyObjectForApplyObjectGroupsByAlertTemplateInput.  # noqa: E501
        :type: list[str]
        """

        self._webhook_ids = webhook_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ApplyObjectForApplyObjectGroupsByAlertTemplateInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ApplyObjectForApplyObjectGroupsByAlertTemplateInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ApplyObjectForApplyObjectGroupsByAlertTemplateInput):
            return True

        return self.to_dict() != other.to_dict()
