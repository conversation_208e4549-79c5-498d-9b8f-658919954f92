# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListPresetAlertTemplatesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_notification': 'AlertNotificationForListPresetAlertTemplatesOutput',
        'applied_project_name': 'str',
        'created_at': 'str',
        'description': 'str',
        'id': 'str',
        'name': 'str',
        'no_data': 'NoDataForListPresetAlertTemplatesOutput',
        'notify_mode': 'str',
        'recovery_notify': 'RecoveryNotifyForListPresetAlertTemplatesOutput',
        'silence_time': 'int',
        'status': 'bool',
        'template_rules': 'list[TemplateRuleForListPresetAlertTemplatesOutput]',
        'updated_at': 'str'
    }

    attribute_map = {
        'alert_notification': 'AlertNotification',
        'applied_project_name': 'AppliedProjectName',
        'created_at': 'CreatedAt',
        'description': 'Description',
        'id': 'Id',
        'name': 'Name',
        'no_data': 'NoData',
        'notify_mode': 'NotifyMode',
        'recovery_notify': 'RecoveryNotify',
        'silence_time': 'SilenceTime',
        'status': 'Status',
        'template_rules': 'TemplateRules',
        'updated_at': 'UpdatedAt'
    }

    def __init__(self, alert_notification=None, applied_project_name=None, created_at=None, description=None, id=None, name=None, no_data=None, notify_mode=None, recovery_notify=None, silence_time=None, status=None, template_rules=None, updated_at=None, _configuration=None):  # noqa: E501
        """DataForListPresetAlertTemplatesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_notification = None
        self._applied_project_name = None
        self._created_at = None
        self._description = None
        self._id = None
        self._name = None
        self._no_data = None
        self._notify_mode = None
        self._recovery_notify = None
        self._silence_time = None
        self._status = None
        self._template_rules = None
        self._updated_at = None
        self.discriminator = None

        if alert_notification is not None:
            self.alert_notification = alert_notification
        if applied_project_name is not None:
            self.applied_project_name = applied_project_name
        if created_at is not None:
            self.created_at = created_at
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if no_data is not None:
            self.no_data = no_data
        if notify_mode is not None:
            self.notify_mode = notify_mode
        if recovery_notify is not None:
            self.recovery_notify = recovery_notify
        if silence_time is not None:
            self.silence_time = silence_time
        if status is not None:
            self.status = status
        if template_rules is not None:
            self.template_rules = template_rules
        if updated_at is not None:
            self.updated_at = updated_at

    @property
    def alert_notification(self):
        """Gets the alert_notification of this DataForListPresetAlertTemplatesOutput.  # noqa: E501


        :return: The alert_notification of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :rtype: AlertNotificationForListPresetAlertTemplatesOutput
        """
        return self._alert_notification

    @alert_notification.setter
    def alert_notification(self, alert_notification):
        """Sets the alert_notification of this DataForListPresetAlertTemplatesOutput.


        :param alert_notification: The alert_notification of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :type: AlertNotificationForListPresetAlertTemplatesOutput
        """

        self._alert_notification = alert_notification

    @property
    def applied_project_name(self):
        """Gets the applied_project_name of this DataForListPresetAlertTemplatesOutput.  # noqa: E501


        :return: The applied_project_name of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._applied_project_name

    @applied_project_name.setter
    def applied_project_name(self, applied_project_name):
        """Sets the applied_project_name of this DataForListPresetAlertTemplatesOutput.


        :param applied_project_name: The applied_project_name of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._applied_project_name = applied_project_name

    @property
    def created_at(self):
        """Gets the created_at of this DataForListPresetAlertTemplatesOutput.  # noqa: E501


        :return: The created_at of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this DataForListPresetAlertTemplatesOutput.


        :param created_at: The created_at of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def description(self):
        """Gets the description of this DataForListPresetAlertTemplatesOutput.  # noqa: E501


        :return: The description of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DataForListPresetAlertTemplatesOutput.


        :param description: The description of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this DataForListPresetAlertTemplatesOutput.  # noqa: E501


        :return: The id of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListPresetAlertTemplatesOutput.


        :param id: The id of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this DataForListPresetAlertTemplatesOutput.  # noqa: E501


        :return: The name of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForListPresetAlertTemplatesOutput.


        :param name: The name of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def no_data(self):
        """Gets the no_data of this DataForListPresetAlertTemplatesOutput.  # noqa: E501


        :return: The no_data of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :rtype: NoDataForListPresetAlertTemplatesOutput
        """
        return self._no_data

    @no_data.setter
    def no_data(self, no_data):
        """Sets the no_data of this DataForListPresetAlertTemplatesOutput.


        :param no_data: The no_data of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :type: NoDataForListPresetAlertTemplatesOutput
        """

        self._no_data = no_data

    @property
    def notify_mode(self):
        """Gets the notify_mode of this DataForListPresetAlertTemplatesOutput.  # noqa: E501


        :return: The notify_mode of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._notify_mode

    @notify_mode.setter
    def notify_mode(self, notify_mode):
        """Sets the notify_mode of this DataForListPresetAlertTemplatesOutput.


        :param notify_mode: The notify_mode of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._notify_mode = notify_mode

    @property
    def recovery_notify(self):
        """Gets the recovery_notify of this DataForListPresetAlertTemplatesOutput.  # noqa: E501


        :return: The recovery_notify of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :rtype: RecoveryNotifyForListPresetAlertTemplatesOutput
        """
        return self._recovery_notify

    @recovery_notify.setter
    def recovery_notify(self, recovery_notify):
        """Sets the recovery_notify of this DataForListPresetAlertTemplatesOutput.


        :param recovery_notify: The recovery_notify of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :type: RecoveryNotifyForListPresetAlertTemplatesOutput
        """

        self._recovery_notify = recovery_notify

    @property
    def silence_time(self):
        """Gets the silence_time of this DataForListPresetAlertTemplatesOutput.  # noqa: E501


        :return: The silence_time of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :rtype: int
        """
        return self._silence_time

    @silence_time.setter
    def silence_time(self, silence_time):
        """Sets the silence_time of this DataForListPresetAlertTemplatesOutput.


        :param silence_time: The silence_time of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :type: int
        """

        self._silence_time = silence_time

    @property
    def status(self):
        """Gets the status of this DataForListPresetAlertTemplatesOutput.  # noqa: E501


        :return: The status of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListPresetAlertTemplatesOutput.


        :param status: The status of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :type: bool
        """

        self._status = status

    @property
    def template_rules(self):
        """Gets the template_rules of this DataForListPresetAlertTemplatesOutput.  # noqa: E501


        :return: The template_rules of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :rtype: list[TemplateRuleForListPresetAlertTemplatesOutput]
        """
        return self._template_rules

    @template_rules.setter
    def template_rules(self, template_rules):
        """Sets the template_rules of this DataForListPresetAlertTemplatesOutput.


        :param template_rules: The template_rules of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :type: list[TemplateRuleForListPresetAlertTemplatesOutput]
        """

        self._template_rules = template_rules

    @property
    def updated_at(self):
        """Gets the updated_at of this DataForListPresetAlertTemplatesOutput.  # noqa: E501


        :return: The updated_at of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this DataForListPresetAlertTemplatesOutput.


        :param updated_at: The updated_at of this DataForListPresetAlertTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListPresetAlertTemplatesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListPresetAlertTemplatesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListPresetAlertTemplatesOutput):
            return True

        return self.to_dict() != other.to_dict()
