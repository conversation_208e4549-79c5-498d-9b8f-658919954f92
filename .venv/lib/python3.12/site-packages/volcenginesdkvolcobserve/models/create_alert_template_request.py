# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateAlertTemplateRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'name': 'str',
        'no_data': 'NoDataForCreateAlertTemplateInput',
        'notify_mode': 'str',
        'recovery_notify': 'RecoveryNotifyForCreateAlertTemplateInput',
        'silence_time': 'int',
        'template_rules': 'list[TemplateRuleForCreateAlertTemplateInput]'
    }

    attribute_map = {
        'description': 'Description',
        'name': 'Name',
        'no_data': 'NoData',
        'notify_mode': 'NotifyMode',
        'recovery_notify': 'RecoveryNotify',
        'silence_time': 'SilenceTime',
        'template_rules': 'TemplateRules'
    }

    def __init__(self, description=None, name=None, no_data=None, notify_mode=None, recovery_notify=None, silence_time=None, template_rules=None, _configuration=None):  # noqa: E501
        """CreateAlertTemplateRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._name = None
        self._no_data = None
        self._notify_mode = None
        self._recovery_notify = None
        self._silence_time = None
        self._template_rules = None
        self.discriminator = None

        if description is not None:
            self.description = description
        self.name = name
        if no_data is not None:
            self.no_data = no_data
        self.notify_mode = notify_mode
        if recovery_notify is not None:
            self.recovery_notify = recovery_notify
        self.silence_time = silence_time
        if template_rules is not None:
            self.template_rules = template_rules

    @property
    def description(self):
        """Gets the description of this CreateAlertTemplateRequest.  # noqa: E501


        :return: The description of this CreateAlertTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateAlertTemplateRequest.


        :param description: The description of this CreateAlertTemplateRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def name(self):
        """Gets the name of this CreateAlertTemplateRequest.  # noqa: E501


        :return: The name of this CreateAlertTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateAlertTemplateRequest.


        :param name: The name of this CreateAlertTemplateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def no_data(self):
        """Gets the no_data of this CreateAlertTemplateRequest.  # noqa: E501


        :return: The no_data of this CreateAlertTemplateRequest.  # noqa: E501
        :rtype: NoDataForCreateAlertTemplateInput
        """
        return self._no_data

    @no_data.setter
    def no_data(self, no_data):
        """Sets the no_data of this CreateAlertTemplateRequest.


        :param no_data: The no_data of this CreateAlertTemplateRequest.  # noqa: E501
        :type: NoDataForCreateAlertTemplateInput
        """

        self._no_data = no_data

    @property
    def notify_mode(self):
        """Gets the notify_mode of this CreateAlertTemplateRequest.  # noqa: E501


        :return: The notify_mode of this CreateAlertTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._notify_mode

    @notify_mode.setter
    def notify_mode(self, notify_mode):
        """Sets the notify_mode of this CreateAlertTemplateRequest.


        :param notify_mode: The notify_mode of this CreateAlertTemplateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and notify_mode is None:
            raise ValueError("Invalid value for `notify_mode`, must not be `None`")  # noqa: E501

        self._notify_mode = notify_mode

    @property
    def recovery_notify(self):
        """Gets the recovery_notify of this CreateAlertTemplateRequest.  # noqa: E501


        :return: The recovery_notify of this CreateAlertTemplateRequest.  # noqa: E501
        :rtype: RecoveryNotifyForCreateAlertTemplateInput
        """
        return self._recovery_notify

    @recovery_notify.setter
    def recovery_notify(self, recovery_notify):
        """Sets the recovery_notify of this CreateAlertTemplateRequest.


        :param recovery_notify: The recovery_notify of this CreateAlertTemplateRequest.  # noqa: E501
        :type: RecoveryNotifyForCreateAlertTemplateInput
        """

        self._recovery_notify = recovery_notify

    @property
    def silence_time(self):
        """Gets the silence_time of this CreateAlertTemplateRequest.  # noqa: E501


        :return: The silence_time of this CreateAlertTemplateRequest.  # noqa: E501
        :rtype: int
        """
        return self._silence_time

    @silence_time.setter
    def silence_time(self, silence_time):
        """Sets the silence_time of this CreateAlertTemplateRequest.


        :param silence_time: The silence_time of this CreateAlertTemplateRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and silence_time is None:
            raise ValueError("Invalid value for `silence_time`, must not be `None`")  # noqa: E501

        self._silence_time = silence_time

    @property
    def template_rules(self):
        """Gets the template_rules of this CreateAlertTemplateRequest.  # noqa: E501


        :return: The template_rules of this CreateAlertTemplateRequest.  # noqa: E501
        :rtype: list[TemplateRuleForCreateAlertTemplateInput]
        """
        return self._template_rules

    @template_rules.setter
    def template_rules(self, template_rules):
        """Sets the template_rules of this CreateAlertTemplateRequest.


        :param template_rules: The template_rules of this CreateAlertTemplateRequest.  # noqa: E501
        :type: list[TemplateRuleForCreateAlertTemplateInput]
        """

        self._template_rules = template_rules

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateAlertTemplateRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateAlertTemplateRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateAlertTemplateRequest):
            return True

        return self.to_dict() != other.to_dict()
