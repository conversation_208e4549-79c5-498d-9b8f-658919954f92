# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateNotificationRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'effect_end_at': 'str',
        'effect_start_at': 'str',
        'id': 'str',
        'name': 'str',
        'notifications': 'list[NotificationForUpdateNotificationInput]'
    }

    attribute_map = {
        'effect_end_at': 'EffectEndAt',
        'effect_start_at': 'EffectStartAt',
        'id': 'Id',
        'name': 'Name',
        'notifications': 'Notifications'
    }

    def __init__(self, effect_end_at=None, effect_start_at=None, id=None, name=None, notifications=None, _configuration=None):  # noqa: E501
        """UpdateNotificationRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._effect_end_at = None
        self._effect_start_at = None
        self._id = None
        self._name = None
        self._notifications = None
        self.discriminator = None

        self.effect_end_at = effect_end_at
        self.effect_start_at = effect_start_at
        self.id = id
        self.name = name
        if notifications is not None:
            self.notifications = notifications

    @property
    def effect_end_at(self):
        """Gets the effect_end_at of this UpdateNotificationRequest.  # noqa: E501


        :return: The effect_end_at of this UpdateNotificationRequest.  # noqa: E501
        :rtype: str
        """
        return self._effect_end_at

    @effect_end_at.setter
    def effect_end_at(self, effect_end_at):
        """Sets the effect_end_at of this UpdateNotificationRequest.


        :param effect_end_at: The effect_end_at of this UpdateNotificationRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and effect_end_at is None:
            raise ValueError("Invalid value for `effect_end_at`, must not be `None`")  # noqa: E501

        self._effect_end_at = effect_end_at

    @property
    def effect_start_at(self):
        """Gets the effect_start_at of this UpdateNotificationRequest.  # noqa: E501


        :return: The effect_start_at of this UpdateNotificationRequest.  # noqa: E501
        :rtype: str
        """
        return self._effect_start_at

    @effect_start_at.setter
    def effect_start_at(self, effect_start_at):
        """Sets the effect_start_at of this UpdateNotificationRequest.


        :param effect_start_at: The effect_start_at of this UpdateNotificationRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and effect_start_at is None:
            raise ValueError("Invalid value for `effect_start_at`, must not be `None`")  # noqa: E501

        self._effect_start_at = effect_start_at

    @property
    def id(self):
        """Gets the id of this UpdateNotificationRequest.  # noqa: E501


        :return: The id of this UpdateNotificationRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateNotificationRequest.


        :param id: The id of this UpdateNotificationRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this UpdateNotificationRequest.  # noqa: E501


        :return: The name of this UpdateNotificationRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateNotificationRequest.


        :param name: The name of this UpdateNotificationRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def notifications(self):
        """Gets the notifications of this UpdateNotificationRequest.  # noqa: E501


        :return: The notifications of this UpdateNotificationRequest.  # noqa: E501
        :rtype: list[NotificationForUpdateNotificationInput]
        """
        return self._notifications

    @notifications.setter
    def notifications(self, notifications):
        """Sets the notifications of this UpdateNotificationRequest.


        :param notifications: The notifications of this UpdateNotificationRequest.  # noqa: E501
        :type: list[NotificationForUpdateNotificationInput]
        """

        self._notifications = notifications

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateNotificationRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateNotificationRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateNotificationRequest):
            return True

        return self.to_dict() != other.to_dict()
