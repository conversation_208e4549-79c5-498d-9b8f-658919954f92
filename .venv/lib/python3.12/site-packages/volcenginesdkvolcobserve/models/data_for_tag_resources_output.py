# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForTagResourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'failed_resources': 'list[str]',
        'succeed_resources': 'list[str]'
    }

    attribute_map = {
        'failed_resources': 'FailedResources',
        'succeed_resources': 'SucceedResources'
    }

    def __init__(self, failed_resources=None, succeed_resources=None, _configuration=None):  # noqa: E501
        """DataForTagResourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._failed_resources = None
        self._succeed_resources = None
        self.discriminator = None

        if failed_resources is not None:
            self.failed_resources = failed_resources
        if succeed_resources is not None:
            self.succeed_resources = succeed_resources

    @property
    def failed_resources(self):
        """Gets the failed_resources of this DataForTagResourcesOutput.  # noqa: E501


        :return: The failed_resources of this DataForTagResourcesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._failed_resources

    @failed_resources.setter
    def failed_resources(self, failed_resources):
        """Sets the failed_resources of this DataForTagResourcesOutput.


        :param failed_resources: The failed_resources of this DataForTagResourcesOutput.  # noqa: E501
        :type: list[str]
        """

        self._failed_resources = failed_resources

    @property
    def succeed_resources(self):
        """Gets the succeed_resources of this DataForTagResourcesOutput.  # noqa: E501


        :return: The succeed_resources of this DataForTagResourcesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._succeed_resources

    @succeed_resources.setter
    def succeed_resources(self, succeed_resources):
        """Sets the succeed_resources of this DataForTagResourcesOutput.


        :param succeed_resources: The succeed_resources of this DataForTagResourcesOutput.  # noqa: E501
        :type: list[str]
        """

        self._succeed_resources = succeed_resources

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForTagResourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForTagResourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForTagResourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
