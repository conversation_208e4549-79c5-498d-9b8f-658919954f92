# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateSilencePolicyRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'effect_time': 'EffectTimeForUpdateSilencePolicyInput',
        'id': 'str',
        'name': 'str',
        'namespace': 'str',
        'silence_conditions': 'SilenceConditionsForUpdateSilencePolicyInput',
        'silence_type': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'effect_time': 'EffectTime',
        'id': 'Id',
        'name': 'Name',
        'namespace': 'Namespace',
        'silence_conditions': 'SilenceConditions',
        'silence_type': 'SilenceType'
    }

    def __init__(self, description=None, effect_time=None, id=None, name=None, namespace=None, silence_conditions=None, silence_type=None, _configuration=None):  # noqa: E501
        """UpdateSilencePolicyRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._effect_time = None
        self._id = None
        self._name = None
        self._namespace = None
        self._silence_conditions = None
        self._silence_type = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if effect_time is not None:
            self.effect_time = effect_time
        self.id = id
        self.name = name
        self.namespace = namespace
        if silence_conditions is not None:
            self.silence_conditions = silence_conditions
        self.silence_type = silence_type

    @property
    def description(self):
        """Gets the description of this UpdateSilencePolicyRequest.  # noqa: E501


        :return: The description of this UpdateSilencePolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateSilencePolicyRequest.


        :param description: The description of this UpdateSilencePolicyRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def effect_time(self):
        """Gets the effect_time of this UpdateSilencePolicyRequest.  # noqa: E501


        :return: The effect_time of this UpdateSilencePolicyRequest.  # noqa: E501
        :rtype: EffectTimeForUpdateSilencePolicyInput
        """
        return self._effect_time

    @effect_time.setter
    def effect_time(self, effect_time):
        """Sets the effect_time of this UpdateSilencePolicyRequest.


        :param effect_time: The effect_time of this UpdateSilencePolicyRequest.  # noqa: E501
        :type: EffectTimeForUpdateSilencePolicyInput
        """

        self._effect_time = effect_time

    @property
    def id(self):
        """Gets the id of this UpdateSilencePolicyRequest.  # noqa: E501


        :return: The id of this UpdateSilencePolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateSilencePolicyRequest.


        :param id: The id of this UpdateSilencePolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this UpdateSilencePolicyRequest.  # noqa: E501


        :return: The name of this UpdateSilencePolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateSilencePolicyRequest.


        :param name: The name of this UpdateSilencePolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def namespace(self):
        """Gets the namespace of this UpdateSilencePolicyRequest.  # noqa: E501


        :return: The namespace of this UpdateSilencePolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this UpdateSilencePolicyRequest.


        :param namespace: The namespace of this UpdateSilencePolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and namespace is None:
            raise ValueError("Invalid value for `namespace`, must not be `None`")  # noqa: E501

        self._namespace = namespace

    @property
    def silence_conditions(self):
        """Gets the silence_conditions of this UpdateSilencePolicyRequest.  # noqa: E501


        :return: The silence_conditions of this UpdateSilencePolicyRequest.  # noqa: E501
        :rtype: SilenceConditionsForUpdateSilencePolicyInput
        """
        return self._silence_conditions

    @silence_conditions.setter
    def silence_conditions(self, silence_conditions):
        """Sets the silence_conditions of this UpdateSilencePolicyRequest.


        :param silence_conditions: The silence_conditions of this UpdateSilencePolicyRequest.  # noqa: E501
        :type: SilenceConditionsForUpdateSilencePolicyInput
        """

        self._silence_conditions = silence_conditions

    @property
    def silence_type(self):
        """Gets the silence_type of this UpdateSilencePolicyRequest.  # noqa: E501


        :return: The silence_type of this UpdateSilencePolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._silence_type

    @silence_type.setter
    def silence_type(self, silence_type):
        """Sets the silence_type of this UpdateSilencePolicyRequest.


        :param silence_type: The silence_type of this UpdateSilencePolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and silence_type is None:
            raise ValueError("Invalid value for `silence_type`, must not be `None`")  # noqa: E501

        self._silence_type = silence_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateSilencePolicyRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateSilencePolicyRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateSilencePolicyRequest):
            return True

        return self.to_dict() != other.to_dict()
