# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListRulesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_state': 'list[str]',
        'enable_state': 'list[str]',
        'level': 'list[str]',
        'namespace': 'list[str]',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'rule_name': 'str'
    }

    attribute_map = {
        'alert_state': 'AlertState',
        'enable_state': 'EnableState',
        'level': 'Level',
        'namespace': 'Namespace',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'rule_name': 'RuleName'
    }

    def __init__(self, alert_state=None, enable_state=None, level=None, namespace=None, page_number=None, page_size=None, project_name=None, rule_name=None, _configuration=None):  # noqa: E501
        """ListRulesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_state = None
        self._enable_state = None
        self._level = None
        self._namespace = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._rule_name = None
        self.discriminator = None

        if alert_state is not None:
            self.alert_state = alert_state
        if enable_state is not None:
            self.enable_state = enable_state
        if level is not None:
            self.level = level
        if namespace is not None:
            self.namespace = namespace
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if rule_name is not None:
            self.rule_name = rule_name

    @property
    def alert_state(self):
        """Gets the alert_state of this ListRulesRequest.  # noqa: E501


        :return: The alert_state of this ListRulesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._alert_state

    @alert_state.setter
    def alert_state(self, alert_state):
        """Sets the alert_state of this ListRulesRequest.


        :param alert_state: The alert_state of this ListRulesRequest.  # noqa: E501
        :type: list[str]
        """

        self._alert_state = alert_state

    @property
    def enable_state(self):
        """Gets the enable_state of this ListRulesRequest.  # noqa: E501


        :return: The enable_state of this ListRulesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._enable_state

    @enable_state.setter
    def enable_state(self, enable_state):
        """Sets the enable_state of this ListRulesRequest.


        :param enable_state: The enable_state of this ListRulesRequest.  # noqa: E501
        :type: list[str]
        """

        self._enable_state = enable_state

    @property
    def level(self):
        """Gets the level of this ListRulesRequest.  # noqa: E501


        :return: The level of this ListRulesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this ListRulesRequest.


        :param level: The level of this ListRulesRequest.  # noqa: E501
        :type: list[str]
        """

        self._level = level

    @property
    def namespace(self):
        """Gets the namespace of this ListRulesRequest.  # noqa: E501


        :return: The namespace of this ListRulesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this ListRulesRequest.


        :param namespace: The namespace of this ListRulesRequest.  # noqa: E501
        :type: list[str]
        """

        self._namespace = namespace

    @property
    def page_number(self):
        """Gets the page_number of this ListRulesRequest.  # noqa: E501


        :return: The page_number of this ListRulesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListRulesRequest.


        :param page_number: The page_number of this ListRulesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListRulesRequest.  # noqa: E501


        :return: The page_size of this ListRulesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListRulesRequest.


        :param page_size: The page_size of this ListRulesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this ListRulesRequest.  # noqa: E501


        :return: The project_name of this ListRulesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListRulesRequest.


        :param project_name: The project_name of this ListRulesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def rule_name(self):
        """Gets the rule_name of this ListRulesRequest.  # noqa: E501


        :return: The rule_name of this ListRulesRequest.  # noqa: E501
        :rtype: str
        """
        return self._rule_name

    @rule_name.setter
    def rule_name(self, rule_name):
        """Sets the rule_name of this ListRulesRequest.


        :param rule_name: The rule_name of this ListRulesRequest.  # noqa: E501
        :type: str
        """

        self._rule_name = rule_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListRulesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListRulesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListRulesRequest):
            return True

        return self.to_dict() != other.to_dict()
