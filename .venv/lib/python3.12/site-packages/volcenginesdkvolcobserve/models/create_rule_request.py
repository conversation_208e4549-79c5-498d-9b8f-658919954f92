# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateRuleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_methods': 'list[str]',
        'condition_operator': 'str',
        'conditions': 'list[ConditionForCreateRuleInput]',
        'contact_group_ids': 'list[str]',
        'description': 'str',
        'dimension_conditions': 'DimensionConditionsForCreateRuleInput',
        'effect_end_at': 'str',
        'effect_start_at': 'str',
        'enable_state': 'str',
        'evaluation_count': 'int',
        'level': 'str',
        'level_conditions': 'list[LevelConditionForCreateRuleInput]',
        'multiple_conditions': 'bool',
        'namespace': 'str',
        'no_data': 'NoDataForCreateRuleInput',
        'notification_id': 'str',
        'original_dimensions': 'dict(str, list[str])',
        'project_name': 'str',
        'recovery_notify': 'RecoveryNotifyForCreateRuleInput',
        'regions': 'list[str]',
        'rule_name': 'str',
        'rule_type': 'str',
        'silence_time': 'int',
        'sub_namespace': 'str',
        'tags': 'list[ConvertTagForCreateRuleInput]',
        'webhook': 'str',
        'webhook_ids': 'list[str]'
    }

    attribute_map = {
        'alert_methods': 'AlertMethods',
        'condition_operator': 'ConditionOperator',
        'conditions': 'Conditions',
        'contact_group_ids': 'ContactGroupIds',
        'description': 'Description',
        'dimension_conditions': 'DimensionConditions',
        'effect_end_at': 'EffectEndAt',
        'effect_start_at': 'EffectStartAt',
        'enable_state': 'EnableState',
        'evaluation_count': 'EvaluationCount',
        'level': 'Level',
        'level_conditions': 'LevelConditions',
        'multiple_conditions': 'MultipleConditions',
        'namespace': 'Namespace',
        'no_data': 'NoData',
        'notification_id': 'NotificationId',
        'original_dimensions': 'OriginalDimensions',
        'project_name': 'ProjectName',
        'recovery_notify': 'RecoveryNotify',
        'regions': 'Regions',
        'rule_name': 'RuleName',
        'rule_type': 'RuleType',
        'silence_time': 'SilenceTime',
        'sub_namespace': 'SubNamespace',
        'tags': 'Tags',
        'webhook': 'Webhook',
        'webhook_ids': 'WebhookIds'
    }

    def __init__(self, alert_methods=None, condition_operator=None, conditions=None, contact_group_ids=None, description=None, dimension_conditions=None, effect_end_at=None, effect_start_at=None, enable_state=None, evaluation_count=None, level=None, level_conditions=None, multiple_conditions=None, namespace=None, no_data=None, notification_id=None, original_dimensions=None, project_name=None, recovery_notify=None, regions=None, rule_name=None, rule_type=None, silence_time=None, sub_namespace=None, tags=None, webhook=None, webhook_ids=None, _configuration=None):  # noqa: E501
        """CreateRuleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_methods = None
        self._condition_operator = None
        self._conditions = None
        self._contact_group_ids = None
        self._description = None
        self._dimension_conditions = None
        self._effect_end_at = None
        self._effect_start_at = None
        self._enable_state = None
        self._evaluation_count = None
        self._level = None
        self._level_conditions = None
        self._multiple_conditions = None
        self._namespace = None
        self._no_data = None
        self._notification_id = None
        self._original_dimensions = None
        self._project_name = None
        self._recovery_notify = None
        self._regions = None
        self._rule_name = None
        self._rule_type = None
        self._silence_time = None
        self._sub_namespace = None
        self._tags = None
        self._webhook = None
        self._webhook_ids = None
        self.discriminator = None

        if alert_methods is not None:
            self.alert_methods = alert_methods
        if condition_operator is not None:
            self.condition_operator = condition_operator
        if conditions is not None:
            self.conditions = conditions
        if contact_group_ids is not None:
            self.contact_group_ids = contact_group_ids
        if description is not None:
            self.description = description
        if dimension_conditions is not None:
            self.dimension_conditions = dimension_conditions
        self.effect_end_at = effect_end_at
        self.effect_start_at = effect_start_at
        self.enable_state = enable_state
        self.evaluation_count = evaluation_count
        self.level = level
        if level_conditions is not None:
            self.level_conditions = level_conditions
        if multiple_conditions is not None:
            self.multiple_conditions = multiple_conditions
        self.namespace = namespace
        if no_data is not None:
            self.no_data = no_data
        if notification_id is not None:
            self.notification_id = notification_id
        if original_dimensions is not None:
            self.original_dimensions = original_dimensions
        if project_name is not None:
            self.project_name = project_name
        if recovery_notify is not None:
            self.recovery_notify = recovery_notify
        if regions is not None:
            self.regions = regions
        self.rule_name = rule_name
        self.rule_type = rule_type
        self.silence_time = silence_time
        self.sub_namespace = sub_namespace
        if tags is not None:
            self.tags = tags
        if webhook is not None:
            self.webhook = webhook
        if webhook_ids is not None:
            self.webhook_ids = webhook_ids

    @property
    def alert_methods(self):
        """Gets the alert_methods of this CreateRuleRequest.  # noqa: E501


        :return: The alert_methods of this CreateRuleRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._alert_methods

    @alert_methods.setter
    def alert_methods(self, alert_methods):
        """Sets the alert_methods of this CreateRuleRequest.


        :param alert_methods: The alert_methods of this CreateRuleRequest.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["Email", "Phone", "SMS", "Webhook"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(alert_methods).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `alert_methods` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(alert_methods) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._alert_methods = alert_methods

    @property
    def condition_operator(self):
        """Gets the condition_operator of this CreateRuleRequest.  # noqa: E501


        :return: The condition_operator of this CreateRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._condition_operator

    @condition_operator.setter
    def condition_operator(self, condition_operator):
        """Sets the condition_operator of this CreateRuleRequest.


        :param condition_operator: The condition_operator of this CreateRuleRequest.  # noqa: E501
        :type: str
        """

        self._condition_operator = condition_operator

    @property
    def conditions(self):
        """Gets the conditions of this CreateRuleRequest.  # noqa: E501


        :return: The conditions of this CreateRuleRequest.  # noqa: E501
        :rtype: list[ConditionForCreateRuleInput]
        """
        return self._conditions

    @conditions.setter
    def conditions(self, conditions):
        """Sets the conditions of this CreateRuleRequest.


        :param conditions: The conditions of this CreateRuleRequest.  # noqa: E501
        :type: list[ConditionForCreateRuleInput]
        """

        self._conditions = conditions

    @property
    def contact_group_ids(self):
        """Gets the contact_group_ids of this CreateRuleRequest.  # noqa: E501


        :return: The contact_group_ids of this CreateRuleRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._contact_group_ids

    @contact_group_ids.setter
    def contact_group_ids(self, contact_group_ids):
        """Sets the contact_group_ids of this CreateRuleRequest.


        :param contact_group_ids: The contact_group_ids of this CreateRuleRequest.  # noqa: E501
        :type: list[str]
        """

        self._contact_group_ids = contact_group_ids

    @property
    def description(self):
        """Gets the description of this CreateRuleRequest.  # noqa: E501


        :return: The description of this CreateRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateRuleRequest.


        :param description: The description of this CreateRuleRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dimension_conditions(self):
        """Gets the dimension_conditions of this CreateRuleRequest.  # noqa: E501


        :return: The dimension_conditions of this CreateRuleRequest.  # noqa: E501
        :rtype: DimensionConditionsForCreateRuleInput
        """
        return self._dimension_conditions

    @dimension_conditions.setter
    def dimension_conditions(self, dimension_conditions):
        """Sets the dimension_conditions of this CreateRuleRequest.


        :param dimension_conditions: The dimension_conditions of this CreateRuleRequest.  # noqa: E501
        :type: DimensionConditionsForCreateRuleInput
        """

        self._dimension_conditions = dimension_conditions

    @property
    def effect_end_at(self):
        """Gets the effect_end_at of this CreateRuleRequest.  # noqa: E501


        :return: The effect_end_at of this CreateRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._effect_end_at

    @effect_end_at.setter
    def effect_end_at(self, effect_end_at):
        """Sets the effect_end_at of this CreateRuleRequest.


        :param effect_end_at: The effect_end_at of this CreateRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and effect_end_at is None:
            raise ValueError("Invalid value for `effect_end_at`, must not be `None`")  # noqa: E501

        self._effect_end_at = effect_end_at

    @property
    def effect_start_at(self):
        """Gets the effect_start_at of this CreateRuleRequest.  # noqa: E501


        :return: The effect_start_at of this CreateRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._effect_start_at

    @effect_start_at.setter
    def effect_start_at(self, effect_start_at):
        """Sets the effect_start_at of this CreateRuleRequest.


        :param effect_start_at: The effect_start_at of this CreateRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and effect_start_at is None:
            raise ValueError("Invalid value for `effect_start_at`, must not be `None`")  # noqa: E501

        self._effect_start_at = effect_start_at

    @property
    def enable_state(self):
        """Gets the enable_state of this CreateRuleRequest.  # noqa: E501


        :return: The enable_state of this CreateRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._enable_state

    @enable_state.setter
    def enable_state(self, enable_state):
        """Sets the enable_state of this CreateRuleRequest.


        :param enable_state: The enable_state of this CreateRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and enable_state is None:
            raise ValueError("Invalid value for `enable_state`, must not be `None`")  # noqa: E501

        self._enable_state = enable_state

    @property
    def evaluation_count(self):
        """Gets the evaluation_count of this CreateRuleRequest.  # noqa: E501


        :return: The evaluation_count of this CreateRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._evaluation_count

    @evaluation_count.setter
    def evaluation_count(self, evaluation_count):
        """Sets the evaluation_count of this CreateRuleRequest.


        :param evaluation_count: The evaluation_count of this CreateRuleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and evaluation_count is None:
            raise ValueError("Invalid value for `evaluation_count`, must not be `None`")  # noqa: E501

        self._evaluation_count = evaluation_count

    @property
    def level(self):
        """Gets the level of this CreateRuleRequest.  # noqa: E501


        :return: The level of this CreateRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this CreateRuleRequest.


        :param level: The level of this CreateRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and level is None:
            raise ValueError("Invalid value for `level`, must not be `None`")  # noqa: E501

        self._level = level

    @property
    def level_conditions(self):
        """Gets the level_conditions of this CreateRuleRequest.  # noqa: E501


        :return: The level_conditions of this CreateRuleRequest.  # noqa: E501
        :rtype: list[LevelConditionForCreateRuleInput]
        """
        return self._level_conditions

    @level_conditions.setter
    def level_conditions(self, level_conditions):
        """Sets the level_conditions of this CreateRuleRequest.


        :param level_conditions: The level_conditions of this CreateRuleRequest.  # noqa: E501
        :type: list[LevelConditionForCreateRuleInput]
        """

        self._level_conditions = level_conditions

    @property
    def multiple_conditions(self):
        """Gets the multiple_conditions of this CreateRuleRequest.  # noqa: E501


        :return: The multiple_conditions of this CreateRuleRequest.  # noqa: E501
        :rtype: bool
        """
        return self._multiple_conditions

    @multiple_conditions.setter
    def multiple_conditions(self, multiple_conditions):
        """Sets the multiple_conditions of this CreateRuleRequest.


        :param multiple_conditions: The multiple_conditions of this CreateRuleRequest.  # noqa: E501
        :type: bool
        """

        self._multiple_conditions = multiple_conditions

    @property
    def namespace(self):
        """Gets the namespace of this CreateRuleRequest.  # noqa: E501


        :return: The namespace of this CreateRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this CreateRuleRequest.


        :param namespace: The namespace of this CreateRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and namespace is None:
            raise ValueError("Invalid value for `namespace`, must not be `None`")  # noqa: E501

        self._namespace = namespace

    @property
    def no_data(self):
        """Gets the no_data of this CreateRuleRequest.  # noqa: E501


        :return: The no_data of this CreateRuleRequest.  # noqa: E501
        :rtype: NoDataForCreateRuleInput
        """
        return self._no_data

    @no_data.setter
    def no_data(self, no_data):
        """Sets the no_data of this CreateRuleRequest.


        :param no_data: The no_data of this CreateRuleRequest.  # noqa: E501
        :type: NoDataForCreateRuleInput
        """

        self._no_data = no_data

    @property
    def notification_id(self):
        """Gets the notification_id of this CreateRuleRequest.  # noqa: E501


        :return: The notification_id of this CreateRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._notification_id

    @notification_id.setter
    def notification_id(self, notification_id):
        """Sets the notification_id of this CreateRuleRequest.


        :param notification_id: The notification_id of this CreateRuleRequest.  # noqa: E501
        :type: str
        """

        self._notification_id = notification_id

    @property
    def original_dimensions(self):
        """Gets the original_dimensions of this CreateRuleRequest.  # noqa: E501


        :return: The original_dimensions of this CreateRuleRequest.  # noqa: E501
        :rtype: dict(str, list[str])
        """
        return self._original_dimensions

    @original_dimensions.setter
    def original_dimensions(self, original_dimensions):
        """Sets the original_dimensions of this CreateRuleRequest.


        :param original_dimensions: The original_dimensions of this CreateRuleRequest.  # noqa: E501
        :type: dict(str, list[str])
        """

        self._original_dimensions = original_dimensions

    @property
    def project_name(self):
        """Gets the project_name of this CreateRuleRequest.  # noqa: E501


        :return: The project_name of this CreateRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateRuleRequest.


        :param project_name: The project_name of this CreateRuleRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def recovery_notify(self):
        """Gets the recovery_notify of this CreateRuleRequest.  # noqa: E501


        :return: The recovery_notify of this CreateRuleRequest.  # noqa: E501
        :rtype: RecoveryNotifyForCreateRuleInput
        """
        return self._recovery_notify

    @recovery_notify.setter
    def recovery_notify(self, recovery_notify):
        """Sets the recovery_notify of this CreateRuleRequest.


        :param recovery_notify: The recovery_notify of this CreateRuleRequest.  # noqa: E501
        :type: RecoveryNotifyForCreateRuleInput
        """

        self._recovery_notify = recovery_notify

    @property
    def regions(self):
        """Gets the regions of this CreateRuleRequest.  # noqa: E501


        :return: The regions of this CreateRuleRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._regions

    @regions.setter
    def regions(self, regions):
        """Sets the regions of this CreateRuleRequest.


        :param regions: The regions of this CreateRuleRequest.  # noqa: E501
        :type: list[str]
        """

        self._regions = regions

    @property
    def rule_name(self):
        """Gets the rule_name of this CreateRuleRequest.  # noqa: E501


        :return: The rule_name of this CreateRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._rule_name

    @rule_name.setter
    def rule_name(self, rule_name):
        """Sets the rule_name of this CreateRuleRequest.


        :param rule_name: The rule_name of this CreateRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and rule_name is None:
            raise ValueError("Invalid value for `rule_name`, must not be `None`")  # noqa: E501

        self._rule_name = rule_name

    @property
    def rule_type(self):
        """Gets the rule_type of this CreateRuleRequest.  # noqa: E501


        :return: The rule_type of this CreateRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._rule_type

    @rule_type.setter
    def rule_type(self, rule_type):
        """Sets the rule_type of this CreateRuleRequest.


        :param rule_type: The rule_type of this CreateRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and rule_type is None:
            raise ValueError("Invalid value for `rule_type`, must not be `None`")  # noqa: E501
        allowed_values = ["static", "dynamic"]  # noqa: E501
        if (self._configuration.client_side_validation and
                rule_type not in allowed_values):
            raise ValueError(
                "Invalid value for `rule_type` ({0}), must be one of {1}"  # noqa: E501
                .format(rule_type, allowed_values)
            )

        self._rule_type = rule_type

    @property
    def silence_time(self):
        """Gets the silence_time of this CreateRuleRequest.  # noqa: E501


        :return: The silence_time of this CreateRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._silence_time

    @silence_time.setter
    def silence_time(self, silence_time):
        """Sets the silence_time of this CreateRuleRequest.


        :param silence_time: The silence_time of this CreateRuleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and silence_time is None:
            raise ValueError("Invalid value for `silence_time`, must not be `None`")  # noqa: E501

        self._silence_time = silence_time

    @property
    def sub_namespace(self):
        """Gets the sub_namespace of this CreateRuleRequest.  # noqa: E501


        :return: The sub_namespace of this CreateRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._sub_namespace

    @sub_namespace.setter
    def sub_namespace(self, sub_namespace):
        """Sets the sub_namespace of this CreateRuleRequest.


        :param sub_namespace: The sub_namespace of this CreateRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and sub_namespace is None:
            raise ValueError("Invalid value for `sub_namespace`, must not be `None`")  # noqa: E501

        self._sub_namespace = sub_namespace

    @property
    def tags(self):
        """Gets the tags of this CreateRuleRequest.  # noqa: E501


        :return: The tags of this CreateRuleRequest.  # noqa: E501
        :rtype: list[ConvertTagForCreateRuleInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateRuleRequest.


        :param tags: The tags of this CreateRuleRequest.  # noqa: E501
        :type: list[ConvertTagForCreateRuleInput]
        """

        self._tags = tags

    @property
    def webhook(self):
        """Gets the webhook of this CreateRuleRequest.  # noqa: E501


        :return: The webhook of this CreateRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._webhook

    @webhook.setter
    def webhook(self, webhook):
        """Sets the webhook of this CreateRuleRequest.


        :param webhook: The webhook of this CreateRuleRequest.  # noqa: E501
        :type: str
        """

        self._webhook = webhook

    @property
    def webhook_ids(self):
        """Gets the webhook_ids of this CreateRuleRequest.  # noqa: E501


        :return: The webhook_ids of this CreateRuleRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._webhook_ids

    @webhook_ids.setter
    def webhook_ids(self, webhook_ids):
        """Sets the webhook_ids of this CreateRuleRequest.


        :param webhook_ids: The webhook_ids of this CreateRuleRequest.  # noqa: E501
        :type: list[str]
        """

        self._webhook_ids = webhook_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateRuleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateRuleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateRuleRequest):
            return True

        return self.to_dict() != other.to_dict()
