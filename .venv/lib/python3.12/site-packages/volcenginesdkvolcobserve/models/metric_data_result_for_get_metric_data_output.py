# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MetricDataResultForGetMetricDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'data_points': 'list[DataPointForGetMetricDataOutput]',
        'dimensions': 'list[DimensionForGetMetricDataOutput]',
        'legend': 'str'
    }

    attribute_map = {
        'data_points': 'DataPoints',
        'dimensions': 'Dimensions',
        'legend': 'Legend'
    }

    def __init__(self, data_points=None, dimensions=None, legend=None, _configuration=None):  # noqa: E501
        """MetricDataResultForGetMetricDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._data_points = None
        self._dimensions = None
        self._legend = None
        self.discriminator = None

        if data_points is not None:
            self.data_points = data_points
        if dimensions is not None:
            self.dimensions = dimensions
        if legend is not None:
            self.legend = legend

    @property
    def data_points(self):
        """Gets the data_points of this MetricDataResultForGetMetricDataOutput.  # noqa: E501


        :return: The data_points of this MetricDataResultForGetMetricDataOutput.  # noqa: E501
        :rtype: list[DataPointForGetMetricDataOutput]
        """
        return self._data_points

    @data_points.setter
    def data_points(self, data_points):
        """Sets the data_points of this MetricDataResultForGetMetricDataOutput.


        :param data_points: The data_points of this MetricDataResultForGetMetricDataOutput.  # noqa: E501
        :type: list[DataPointForGetMetricDataOutput]
        """

        self._data_points = data_points

    @property
    def dimensions(self):
        """Gets the dimensions of this MetricDataResultForGetMetricDataOutput.  # noqa: E501


        :return: The dimensions of this MetricDataResultForGetMetricDataOutput.  # noqa: E501
        :rtype: list[DimensionForGetMetricDataOutput]
        """
        return self._dimensions

    @dimensions.setter
    def dimensions(self, dimensions):
        """Sets the dimensions of this MetricDataResultForGetMetricDataOutput.


        :param dimensions: The dimensions of this MetricDataResultForGetMetricDataOutput.  # noqa: E501
        :type: list[DimensionForGetMetricDataOutput]
        """

        self._dimensions = dimensions

    @property
    def legend(self):
        """Gets the legend of this MetricDataResultForGetMetricDataOutput.  # noqa: E501


        :return: The legend of this MetricDataResultForGetMetricDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._legend

    @legend.setter
    def legend(self, legend):
        """Sets the legend of this MetricDataResultForGetMetricDataOutput.


        :param legend: The legend of this MetricDataResultForGetMetricDataOutput.  # noqa: E501
        :type: str
        """

        self._legend = legend

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MetricDataResultForGetMetricDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MetricDataResultForGetMetricDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MetricDataResultForGetMetricDataOutput):
            return True

        return self.to_dict() != other.to_dict()
