# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetMetricDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description_cn': 'str',
        'description_en': 'str',
        'end_time': 'int',
        'metric_data_results': 'list[MetricDataResultForGetMetricDataOutput]',
        'metric_name': 'str',
        'namespace': 'str',
        'period': 'str',
        'start_time': 'int',
        'unit': 'str'
    }

    attribute_map = {
        'description_cn': 'DescriptionCN',
        'description_en': 'DescriptionEN',
        'end_time': 'EndTime',
        'metric_data_results': 'MetricDataResults',
        'metric_name': 'MetricName',
        'namespace': 'Namespace',
        'period': 'Period',
        'start_time': 'StartTime',
        'unit': 'Unit'
    }

    def __init__(self, description_cn=None, description_en=None, end_time=None, metric_data_results=None, metric_name=None, namespace=None, period=None, start_time=None, unit=None, _configuration=None):  # noqa: E501
        """DataForGetMetricDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description_cn = None
        self._description_en = None
        self._end_time = None
        self._metric_data_results = None
        self._metric_name = None
        self._namespace = None
        self._period = None
        self._start_time = None
        self._unit = None
        self.discriminator = None

        if description_cn is not None:
            self.description_cn = description_cn
        if description_en is not None:
            self.description_en = description_en
        if end_time is not None:
            self.end_time = end_time
        if metric_data_results is not None:
            self.metric_data_results = metric_data_results
        if metric_name is not None:
            self.metric_name = metric_name
        if namespace is not None:
            self.namespace = namespace
        if period is not None:
            self.period = period
        if start_time is not None:
            self.start_time = start_time
        if unit is not None:
            self.unit = unit

    @property
    def description_cn(self):
        """Gets the description_cn of this DataForGetMetricDataOutput.  # noqa: E501


        :return: The description_cn of this DataForGetMetricDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._description_cn

    @description_cn.setter
    def description_cn(self, description_cn):
        """Sets the description_cn of this DataForGetMetricDataOutput.


        :param description_cn: The description_cn of this DataForGetMetricDataOutput.  # noqa: E501
        :type: str
        """

        self._description_cn = description_cn

    @property
    def description_en(self):
        """Gets the description_en of this DataForGetMetricDataOutput.  # noqa: E501


        :return: The description_en of this DataForGetMetricDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._description_en

    @description_en.setter
    def description_en(self, description_en):
        """Sets the description_en of this DataForGetMetricDataOutput.


        :param description_en: The description_en of this DataForGetMetricDataOutput.  # noqa: E501
        :type: str
        """

        self._description_en = description_en

    @property
    def end_time(self):
        """Gets the end_time of this DataForGetMetricDataOutput.  # noqa: E501


        :return: The end_time of this DataForGetMetricDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DataForGetMetricDataOutput.


        :param end_time: The end_time of this DataForGetMetricDataOutput.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def metric_data_results(self):
        """Gets the metric_data_results of this DataForGetMetricDataOutput.  # noqa: E501


        :return: The metric_data_results of this DataForGetMetricDataOutput.  # noqa: E501
        :rtype: list[MetricDataResultForGetMetricDataOutput]
        """
        return self._metric_data_results

    @metric_data_results.setter
    def metric_data_results(self, metric_data_results):
        """Sets the metric_data_results of this DataForGetMetricDataOutput.


        :param metric_data_results: The metric_data_results of this DataForGetMetricDataOutput.  # noqa: E501
        :type: list[MetricDataResultForGetMetricDataOutput]
        """

        self._metric_data_results = metric_data_results

    @property
    def metric_name(self):
        """Gets the metric_name of this DataForGetMetricDataOutput.  # noqa: E501


        :return: The metric_name of this DataForGetMetricDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._metric_name

    @metric_name.setter
    def metric_name(self, metric_name):
        """Sets the metric_name of this DataForGetMetricDataOutput.


        :param metric_name: The metric_name of this DataForGetMetricDataOutput.  # noqa: E501
        :type: str
        """

        self._metric_name = metric_name

    @property
    def namespace(self):
        """Gets the namespace of this DataForGetMetricDataOutput.  # noqa: E501


        :return: The namespace of this DataForGetMetricDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this DataForGetMetricDataOutput.


        :param namespace: The namespace of this DataForGetMetricDataOutput.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    @property
    def period(self):
        """Gets the period of this DataForGetMetricDataOutput.  # noqa: E501


        :return: The period of this DataForGetMetricDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this DataForGetMetricDataOutput.


        :param period: The period of this DataForGetMetricDataOutput.  # noqa: E501
        :type: str
        """

        self._period = period

    @property
    def start_time(self):
        """Gets the start_time of this DataForGetMetricDataOutput.  # noqa: E501


        :return: The start_time of this DataForGetMetricDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DataForGetMetricDataOutput.


        :param start_time: The start_time of this DataForGetMetricDataOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def unit(self):
        """Gets the unit of this DataForGetMetricDataOutput.  # noqa: E501


        :return: The unit of this DataForGetMetricDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._unit

    @unit.setter
    def unit(self, unit):
        """Sets the unit of this DataForGetMetricDataOutput.


        :param unit: The unit of this DataForGetMetricDataOutput.  # noqa: E501
        :type: str
        """

        self._unit = unit

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetMetricDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetMetricDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetMetricDataOutput):
            return True

        return self.to_dict() != other.to_dict()
