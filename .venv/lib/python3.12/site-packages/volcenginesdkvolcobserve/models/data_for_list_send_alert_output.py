# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListSendAlertOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_method': 'str',
        'bath_id': 'str',
        'content': 'str',
        'id': 'str',
        'send_at': 'str',
        'send_result': 'list[SendResultForListSendAlertOutput]'
    }

    attribute_map = {
        'alert_method': 'AlertMethod',
        'bath_id': 'BathId',
        'content': 'Content',
        'id': 'Id',
        'send_at': 'SendAt',
        'send_result': 'SendResult'
    }

    def __init__(self, alert_method=None, bath_id=None, content=None, id=None, send_at=None, send_result=None, _configuration=None):  # noqa: E501
        """DataForListSendAlertOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_method = None
        self._bath_id = None
        self._content = None
        self._id = None
        self._send_at = None
        self._send_result = None
        self.discriminator = None

        if alert_method is not None:
            self.alert_method = alert_method
        if bath_id is not None:
            self.bath_id = bath_id
        if content is not None:
            self.content = content
        if id is not None:
            self.id = id
        if send_at is not None:
            self.send_at = send_at
        if send_result is not None:
            self.send_result = send_result

    @property
    def alert_method(self):
        """Gets the alert_method of this DataForListSendAlertOutput.  # noqa: E501


        :return: The alert_method of this DataForListSendAlertOutput.  # noqa: E501
        :rtype: str
        """
        return self._alert_method

    @alert_method.setter
    def alert_method(self, alert_method):
        """Sets the alert_method of this DataForListSendAlertOutput.


        :param alert_method: The alert_method of this DataForListSendAlertOutput.  # noqa: E501
        :type: str
        """

        self._alert_method = alert_method

    @property
    def bath_id(self):
        """Gets the bath_id of this DataForListSendAlertOutput.  # noqa: E501


        :return: The bath_id of this DataForListSendAlertOutput.  # noqa: E501
        :rtype: str
        """
        return self._bath_id

    @bath_id.setter
    def bath_id(self, bath_id):
        """Sets the bath_id of this DataForListSendAlertOutput.


        :param bath_id: The bath_id of this DataForListSendAlertOutput.  # noqa: E501
        :type: str
        """

        self._bath_id = bath_id

    @property
    def content(self):
        """Gets the content of this DataForListSendAlertOutput.  # noqa: E501


        :return: The content of this DataForListSendAlertOutput.  # noqa: E501
        :rtype: str
        """
        return self._content

    @content.setter
    def content(self, content):
        """Sets the content of this DataForListSendAlertOutput.


        :param content: The content of this DataForListSendAlertOutput.  # noqa: E501
        :type: str
        """

        self._content = content

    @property
    def id(self):
        """Gets the id of this DataForListSendAlertOutput.  # noqa: E501


        :return: The id of this DataForListSendAlertOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListSendAlertOutput.


        :param id: The id of this DataForListSendAlertOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def send_at(self):
        """Gets the send_at of this DataForListSendAlertOutput.  # noqa: E501


        :return: The send_at of this DataForListSendAlertOutput.  # noqa: E501
        :rtype: str
        """
        return self._send_at

    @send_at.setter
    def send_at(self, send_at):
        """Sets the send_at of this DataForListSendAlertOutput.


        :param send_at: The send_at of this DataForListSendAlertOutput.  # noqa: E501
        :type: str
        """

        self._send_at = send_at

    @property
    def send_result(self):
        """Gets the send_result of this DataForListSendAlertOutput.  # noqa: E501


        :return: The send_result of this DataForListSendAlertOutput.  # noqa: E501
        :rtype: list[SendResultForListSendAlertOutput]
        """
        return self._send_result

    @send_result.setter
    def send_result(self, send_result):
        """Sets the send_result of this DataForListSendAlertOutput.


        :param send_result: The send_result of this DataForListSendAlertOutput.  # noqa: E501
        :type: list[SendResultForListSendAlertOutput]
        """

        self._send_result = send_result

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListSendAlertOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListSendAlertOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListSendAlertOutput):
            return True

        return self.to_dict() != other.to_dict()
