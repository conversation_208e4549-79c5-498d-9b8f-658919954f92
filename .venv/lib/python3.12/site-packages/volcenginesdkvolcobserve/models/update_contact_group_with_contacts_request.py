# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateContactGroupWithContactsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'contacts_id_list': 'list[str]',
        'id': 'str'
    }

    attribute_map = {
        'contacts_id_list': 'ContactsIdList',
        'id': 'Id'
    }

    def __init__(self, contacts_id_list=None, id=None, _configuration=None):  # noqa: E501
        """UpdateContactGroupWithContactsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._contacts_id_list = None
        self._id = None
        self.discriminator = None

        if contacts_id_list is not None:
            self.contacts_id_list = contacts_id_list
        self.id = id

    @property
    def contacts_id_list(self):
        """Gets the contacts_id_list of this UpdateContactGroupWithContactsRequest.  # noqa: E501


        :return: The contacts_id_list of this UpdateContactGroupWithContactsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._contacts_id_list

    @contacts_id_list.setter
    def contacts_id_list(self, contacts_id_list):
        """Sets the contacts_id_list of this UpdateContactGroupWithContactsRequest.


        :param contacts_id_list: The contacts_id_list of this UpdateContactGroupWithContactsRequest.  # noqa: E501
        :type: list[str]
        """

        self._contacts_id_list = contacts_id_list

    @property
    def id(self):
        """Gets the id of this UpdateContactGroupWithContactsRequest.  # noqa: E501


        :return: The id of this UpdateContactGroupWithContactsRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateContactGroupWithContactsRequest.


        :param id: The id of this UpdateContactGroupWithContactsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateContactGroupWithContactsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateContactGroupWithContactsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateContactGroupWithContactsRequest):
            return True

        return self.to_dict() != other.to_dict()
