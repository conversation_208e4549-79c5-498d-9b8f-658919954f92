# coding: utf-8

"""
    vedbm

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceForDescribeCrossRegionBackupDBInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cross_region_backup': 'bool',
        'db_engine_version': 'str',
        'db_revision_version': 'str',
        'instance_id': 'str',
        'lower_case_table_names': 'str',
        'project_name': 'str',
        'region_id': 'str',
        'retention': 'int',
        'target_region': 'str',
        'time_zone': 'str'
    }

    attribute_map = {
        'cross_region_backup': 'CrossRegionBackup',
        'db_engine_version': 'DBEngineVersion',
        'db_revision_version': 'DBRevisionVersion',
        'instance_id': 'InstanceId',
        'lower_case_table_names': 'LowerCaseTableNames',
        'project_name': 'ProjectName',
        'region_id': 'RegionId',
        'retention': 'Retention',
        'target_region': 'TargetRegion',
        'time_zone': 'TimeZone'
    }

    def __init__(self, cross_region_backup=None, db_engine_version=None, db_revision_version=None, instance_id=None, lower_case_table_names=None, project_name=None, region_id=None, retention=None, target_region=None, time_zone=None, _configuration=None):  # noqa: E501
        """InstanceForDescribeCrossRegionBackupDBInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cross_region_backup = None
        self._db_engine_version = None
        self._db_revision_version = None
        self._instance_id = None
        self._lower_case_table_names = None
        self._project_name = None
        self._region_id = None
        self._retention = None
        self._target_region = None
        self._time_zone = None
        self.discriminator = None

        if cross_region_backup is not None:
            self.cross_region_backup = cross_region_backup
        if db_engine_version is not None:
            self.db_engine_version = db_engine_version
        if db_revision_version is not None:
            self.db_revision_version = db_revision_version
        if instance_id is not None:
            self.instance_id = instance_id
        if lower_case_table_names is not None:
            self.lower_case_table_names = lower_case_table_names
        if project_name is not None:
            self.project_name = project_name
        if region_id is not None:
            self.region_id = region_id
        if retention is not None:
            self.retention = retention
        if target_region is not None:
            self.target_region = target_region
        if time_zone is not None:
            self.time_zone = time_zone

    @property
    def cross_region_backup(self):
        """Gets the cross_region_backup of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501


        :return: The cross_region_backup of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._cross_region_backup

    @cross_region_backup.setter
    def cross_region_backup(self, cross_region_backup):
        """Sets the cross_region_backup of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.


        :param cross_region_backup: The cross_region_backup of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._cross_region_backup = cross_region_backup

    @property
    def db_engine_version(self):
        """Gets the db_engine_version of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501


        :return: The db_engine_version of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version

    @db_engine_version.setter
    def db_engine_version(self, db_engine_version):
        """Sets the db_engine_version of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.


        :param db_engine_version: The db_engine_version of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :type: str
        """

        self._db_engine_version = db_engine_version

    @property
    def db_revision_version(self):
        """Gets the db_revision_version of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501


        :return: The db_revision_version of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_revision_version

    @db_revision_version.setter
    def db_revision_version(self, db_revision_version):
        """Sets the db_revision_version of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.


        :param db_revision_version: The db_revision_version of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :type: str
        """

        self._db_revision_version = db_revision_version

    @property
    def instance_id(self):
        """Gets the instance_id of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501


        :return: The instance_id of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.


        :param instance_id: The instance_id of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def lower_case_table_names(self):
        """Gets the lower_case_table_names of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501


        :return: The lower_case_table_names of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._lower_case_table_names

    @lower_case_table_names.setter
    def lower_case_table_names(self, lower_case_table_names):
        """Sets the lower_case_table_names of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.


        :param lower_case_table_names: The lower_case_table_names of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :type: str
        """

        self._lower_case_table_names = lower_case_table_names

    @property
    def project_name(self):
        """Gets the project_name of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501


        :return: The project_name of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.


        :param project_name: The project_name of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def region_id(self):
        """Gets the region_id of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501


        :return: The region_id of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.


        :param region_id: The region_id of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def retention(self):
        """Gets the retention of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501


        :return: The retention of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._retention

    @retention.setter
    def retention(self, retention):
        """Sets the retention of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.


        :param retention: The retention of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :type: int
        """

        self._retention = retention

    @property
    def target_region(self):
        """Gets the target_region of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501


        :return: The target_region of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._target_region

    @target_region.setter
    def target_region(self, target_region):
        """Sets the target_region of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.


        :param target_region: The target_region of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :type: str
        """

        self._target_region = target_region

    @property
    def time_zone(self):
        """Gets the time_zone of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501


        :return: The time_zone of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._time_zone

    @time_zone.setter
    def time_zone(self, time_zone):
        """Sets the time_zone of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.


        :param time_zone: The time_zone of this InstanceForDescribeCrossRegionBackupDBInstancesOutput.  # noqa: E501
        :type: str
        """

        self._time_zone = time_zone

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceForDescribeCrossRegionBackupDBInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceForDescribeCrossRegionBackupDBInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceForDescribeCrossRegionBackupDBInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
