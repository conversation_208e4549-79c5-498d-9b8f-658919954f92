# coding: utf-8

"""
    vedbm

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RestoreTableRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_id': 'str',
        'instance_id': 'str',
        'restore_time': 'str',
        'table_meta': 'list[TableMetaForRestoreTableInput]'
    }

    attribute_map = {
        'backup_id': 'BackupId',
        'instance_id': 'InstanceId',
        'restore_time': 'RestoreTime',
        'table_meta': 'TableMeta'
    }

    def __init__(self, backup_id=None, instance_id=None, restore_time=None, table_meta=None, _configuration=None):  # noqa: E501
        """RestoreTableRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_id = None
        self._instance_id = None
        self._restore_time = None
        self._table_meta = None
        self.discriminator = None

        if backup_id is not None:
            self.backup_id = backup_id
        self.instance_id = instance_id
        if restore_time is not None:
            self.restore_time = restore_time
        if table_meta is not None:
            self.table_meta = table_meta

    @property
    def backup_id(self):
        """Gets the backup_id of this RestoreTableRequest.  # noqa: E501


        :return: The backup_id of this RestoreTableRequest.  # noqa: E501
        :rtype: str
        """
        return self._backup_id

    @backup_id.setter
    def backup_id(self, backup_id):
        """Sets the backup_id of this RestoreTableRequest.


        :param backup_id: The backup_id of this RestoreTableRequest.  # noqa: E501
        :type: str
        """

        self._backup_id = backup_id

    @property
    def instance_id(self):
        """Gets the instance_id of this RestoreTableRequest.  # noqa: E501


        :return: The instance_id of this RestoreTableRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this RestoreTableRequest.


        :param instance_id: The instance_id of this RestoreTableRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def restore_time(self):
        """Gets the restore_time of this RestoreTableRequest.  # noqa: E501


        :return: The restore_time of this RestoreTableRequest.  # noqa: E501
        :rtype: str
        """
        return self._restore_time

    @restore_time.setter
    def restore_time(self, restore_time):
        """Sets the restore_time of this RestoreTableRequest.


        :param restore_time: The restore_time of this RestoreTableRequest.  # noqa: E501
        :type: str
        """

        self._restore_time = restore_time

    @property
    def table_meta(self):
        """Gets the table_meta of this RestoreTableRequest.  # noqa: E501


        :return: The table_meta of this RestoreTableRequest.  # noqa: E501
        :rtype: list[TableMetaForRestoreTableInput]
        """
        return self._table_meta

    @table_meta.setter
    def table_meta(self, table_meta):
        """Sets the table_meta of this RestoreTableRequest.


        :param table_meta: The table_meta of this RestoreTableRequest.  # noqa: E501
        :type: list[TableMetaForRestoreTableInput]
        """

        self._table_meta = table_meta

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RestoreTableRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RestoreTableRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RestoreTableRequest):
            return True

        return self.to_dict() != other.to_dict()
