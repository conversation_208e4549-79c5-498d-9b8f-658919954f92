# coding: utf-8

"""
    vedbm

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyDBInstanceSpecRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'node_number': 'int',
        'node_spec': 'str',
        'pre_paid_storage_in_gb': 'int'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'node_number': 'NodeNumber',
        'node_spec': 'NodeSpec',
        'pre_paid_storage_in_gb': 'PrePaidStorageInGB'
    }

    def __init__(self, instance_id=None, node_number=None, node_spec=None, pre_paid_storage_in_gb=None, _configuration=None):  # noqa: E501
        """ModifyDBInstanceSpecRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._node_number = None
        self._node_spec = None
        self._pre_paid_storage_in_gb = None
        self.discriminator = None

        self.instance_id = instance_id
        self.node_number = node_number
        self.node_spec = node_spec
        if pre_paid_storage_in_gb is not None:
            self.pre_paid_storage_in_gb = pre_paid_storage_in_gb

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyDBInstanceSpecRequest.  # noqa: E501


        :return: The instance_id of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyDBInstanceSpecRequest.


        :param instance_id: The instance_id of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def node_number(self):
        """Gets the node_number of this ModifyDBInstanceSpecRequest.  # noqa: E501


        :return: The node_number of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :rtype: int
        """
        return self._node_number

    @node_number.setter
    def node_number(self, node_number):
        """Sets the node_number of this ModifyDBInstanceSpecRequest.


        :param node_number: The node_number of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and node_number is None:
            raise ValueError("Invalid value for `node_number`, must not be `None`")  # noqa: E501

        self._node_number = node_number

    @property
    def node_spec(self):
        """Gets the node_spec of this ModifyDBInstanceSpecRequest.  # noqa: E501


        :return: The node_spec of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :rtype: str
        """
        return self._node_spec

    @node_spec.setter
    def node_spec(self, node_spec):
        """Sets the node_spec of this ModifyDBInstanceSpecRequest.


        :param node_spec: The node_spec of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and node_spec is None:
            raise ValueError("Invalid value for `node_spec`, must not be `None`")  # noqa: E501
        allowed_values = ["vedb.mysql.g4.2xlarge", "vedb.mysql.g4.4xlarge", "vedb.mysql.g4.large", "vedb.mysql.g4.xlarge", "vedb.mysql.g8.2xlarge", "vedb.mysql.x4.2xlarge", "vedb.mysql.x4.4xlarge", "vedb.mysql.x4.8xlarge", "vedb.mysql.x4.large", "vedb.mysql.x4.xlarge", "vedb.mysql.x8.2xlarge", "vedb.mysql.x8.4xlarge", "vedb.mysql.x8.6xlarge", "vedb.mysql.x8.8xlarge", "vedb.mysql.x8.large", "vedb.mysql.x8.xlarge"]  # noqa: E501
        if (self._configuration.client_side_validation and
                node_spec not in allowed_values):
            raise ValueError(
                "Invalid value for `node_spec` ({0}), must be one of {1}"  # noqa: E501
                .format(node_spec, allowed_values)
            )

        self._node_spec = node_spec

    @property
    def pre_paid_storage_in_gb(self):
        """Gets the pre_paid_storage_in_gb of this ModifyDBInstanceSpecRequest.  # noqa: E501


        :return: The pre_paid_storage_in_gb of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :rtype: int
        """
        return self._pre_paid_storage_in_gb

    @pre_paid_storage_in_gb.setter
    def pre_paid_storage_in_gb(self, pre_paid_storage_in_gb):
        """Sets the pre_paid_storage_in_gb of this ModifyDBInstanceSpecRequest.


        :param pre_paid_storage_in_gb: The pre_paid_storage_in_gb of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :type: int
        """

        self._pre_paid_storage_in_gb = pre_paid_storage_in_gb

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyDBInstanceSpecRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyDBInstanceSpecRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyDBInstanceSpecRequest):
            return True

        return self.to_dict() != other.to_dict()
