# coding: utf-8

"""
    vedbm

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeAllowListsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_lists': 'list[AllowListForDescribeAllowListsOutput]'
    }

    attribute_map = {
        'allow_lists': 'AllowLists'
    }

    def __init__(self, allow_lists=None, _configuration=None):  # noqa: E501
        """DescribeAllowListsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_lists = None
        self.discriminator = None

        if allow_lists is not None:
            self.allow_lists = allow_lists

    @property
    def allow_lists(self):
        """Gets the allow_lists of this DescribeAllowListsResponse.  # noqa: E501


        :return: The allow_lists of this DescribeAllowListsResponse.  # noqa: E501
        :rtype: list[AllowListForDescribeAllowListsOutput]
        """
        return self._allow_lists

    @allow_lists.setter
    def allow_lists(self, allow_lists):
        """Sets the allow_lists of this DescribeAllowListsResponse.


        :param allow_lists: The allow_lists of this DescribeAllowListsResponse.  # noqa: E501
        :type: list[AllowListForDescribeAllowListsOutput]
        """

        self._allow_lists = allow_lists

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeAllowListsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeAllowListsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeAllowListsResponse):
            return True

        return self.to_dict() != other.to_dict()
