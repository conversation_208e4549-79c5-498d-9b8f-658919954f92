# coding: utf-8

"""
    vedbm

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateBackupRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_method': 'str',
        'backup_type': 'str',
        'instance_id': 'str'
    }

    attribute_map = {
        'backup_method': 'BackupMethod',
        'backup_type': 'BackupType',
        'instance_id': 'InstanceId'
    }

    def __init__(self, backup_method=None, backup_type=None, instance_id=None, _configuration=None):  # noqa: E501
        """CreateBackupRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_method = None
        self._backup_type = None
        self._instance_id = None
        self.discriminator = None

        if backup_method is not None:
            self.backup_method = backup_method
        if backup_type is not None:
            self.backup_type = backup_type
        self.instance_id = instance_id

    @property
    def backup_method(self):
        """Gets the backup_method of this CreateBackupRequest.  # noqa: E501


        :return: The backup_method of this CreateBackupRequest.  # noqa: E501
        :rtype: str
        """
        return self._backup_method

    @backup_method.setter
    def backup_method(self, backup_method):
        """Sets the backup_method of this CreateBackupRequest.


        :param backup_method: The backup_method of this CreateBackupRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Physical"]  # noqa: E501
        if (self._configuration.client_side_validation and
                backup_method not in allowed_values):
            raise ValueError(
                "Invalid value for `backup_method` ({0}), must be one of {1}"  # noqa: E501
                .format(backup_method, allowed_values)
            )

        self._backup_method = backup_method

    @property
    def backup_type(self):
        """Gets the backup_type of this CreateBackupRequest.  # noqa: E501


        :return: The backup_type of this CreateBackupRequest.  # noqa: E501
        :rtype: str
        """
        return self._backup_type

    @backup_type.setter
    def backup_type(self, backup_type):
        """Sets the backup_type of this CreateBackupRequest.


        :param backup_type: The backup_type of this CreateBackupRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Full"]  # noqa: E501
        if (self._configuration.client_side_validation and
                backup_type not in allowed_values):
            raise ValueError(
                "Invalid value for `backup_type` ({0}), must be one of {1}"  # noqa: E501
                .format(backup_type, allowed_values)
            )

        self._backup_type = backup_type

    @property
    def instance_id(self):
        """Gets the instance_id of this CreateBackupRequest.  # noqa: E501


        :return: The instance_id of this CreateBackupRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this CreateBackupRequest.


        :param instance_id: The instance_id of this CreateBackupRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateBackupRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateBackupRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateBackupRequest):
            return True

        return self.to_dict() != other.to_dict()
