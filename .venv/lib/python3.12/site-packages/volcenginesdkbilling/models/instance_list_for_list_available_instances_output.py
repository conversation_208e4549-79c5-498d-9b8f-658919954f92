# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceListForListAvailableInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'int',
        'begin_time': 'str',
        'configuration_code': 'str',
        'expired_time': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'payment_method': 'str',
        'product': 'str',
        'remain_renew_times': 'str',
        'renew_type': 'str',
        'renewal_duration_unit': 'str',
        'renewal_times': 'str',
        'status': 'str',
        'sub_status': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'begin_time': 'BeginTime',
        'configuration_code': 'ConfigurationCode',
        'expired_time': 'ExpiredTime',
        'instance_id': 'InstanceID',
        'instance_name': 'InstanceName',
        'payment_method': 'PaymentMethod',
        'product': 'Product',
        'remain_renew_times': 'RemainRenewTimes',
        'renew_type': 'RenewType',
        'renewal_duration_unit': 'RenewalDurationUnit',
        'renewal_times': 'RenewalTimes',
        'status': 'Status',
        'sub_status': 'SubStatus'
    }

    def __init__(self, account_id=None, begin_time=None, configuration_code=None, expired_time=None, instance_id=None, instance_name=None, payment_method=None, product=None, remain_renew_times=None, renew_type=None, renewal_duration_unit=None, renewal_times=None, status=None, sub_status=None, _configuration=None):  # noqa: E501
        """InstanceListForListAvailableInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._begin_time = None
        self._configuration_code = None
        self._expired_time = None
        self._instance_id = None
        self._instance_name = None
        self._payment_method = None
        self._product = None
        self._remain_renew_times = None
        self._renew_type = None
        self._renewal_duration_unit = None
        self._renewal_times = None
        self._status = None
        self._sub_status = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if begin_time is not None:
            self.begin_time = begin_time
        if configuration_code is not None:
            self.configuration_code = configuration_code
        if expired_time is not None:
            self.expired_time = expired_time
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if payment_method is not None:
            self.payment_method = payment_method
        if product is not None:
            self.product = product
        if remain_renew_times is not None:
            self.remain_renew_times = remain_renew_times
        if renew_type is not None:
            self.renew_type = renew_type
        if renewal_duration_unit is not None:
            self.renewal_duration_unit = renewal_duration_unit
        if renewal_times is not None:
            self.renewal_times = renewal_times
        if status is not None:
            self.status = status
        if sub_status is not None:
            self.sub_status = sub_status

    @property
    def account_id(self):
        """Gets the account_id of this InstanceListForListAvailableInstancesOutput.  # noqa: E501


        :return: The account_id of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this InstanceListForListAvailableInstancesOutput.


        :param account_id: The account_id of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def begin_time(self):
        """Gets the begin_time of this InstanceListForListAvailableInstancesOutput.  # noqa: E501


        :return: The begin_time of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._begin_time

    @begin_time.setter
    def begin_time(self, begin_time):
        """Sets the begin_time of this InstanceListForListAvailableInstancesOutput.


        :param begin_time: The begin_time of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :type: str
        """

        self._begin_time = begin_time

    @property
    def configuration_code(self):
        """Gets the configuration_code of this InstanceListForListAvailableInstancesOutput.  # noqa: E501


        :return: The configuration_code of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._configuration_code

    @configuration_code.setter
    def configuration_code(self, configuration_code):
        """Sets the configuration_code of this InstanceListForListAvailableInstancesOutput.


        :param configuration_code: The configuration_code of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :type: str
        """

        self._configuration_code = configuration_code

    @property
    def expired_time(self):
        """Gets the expired_time of this InstanceListForListAvailableInstancesOutput.  # noqa: E501


        :return: The expired_time of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._expired_time

    @expired_time.setter
    def expired_time(self, expired_time):
        """Sets the expired_time of this InstanceListForListAvailableInstancesOutput.


        :param expired_time: The expired_time of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :type: str
        """

        self._expired_time = expired_time

    @property
    def instance_id(self):
        """Gets the instance_id of this InstanceListForListAvailableInstancesOutput.  # noqa: E501


        :return: The instance_id of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this InstanceListForListAvailableInstancesOutput.


        :param instance_id: The instance_id of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this InstanceListForListAvailableInstancesOutput.  # noqa: E501


        :return: The instance_name of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this InstanceListForListAvailableInstancesOutput.


        :param instance_name: The instance_name of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def payment_method(self):
        """Gets the payment_method of this InstanceListForListAvailableInstancesOutput.  # noqa: E501


        :return: The payment_method of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._payment_method

    @payment_method.setter
    def payment_method(self, payment_method):
        """Sets the payment_method of this InstanceListForListAvailableInstancesOutput.


        :param payment_method: The payment_method of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :type: str
        """

        self._payment_method = payment_method

    @property
    def product(self):
        """Gets the product of this InstanceListForListAvailableInstancesOutput.  # noqa: E501


        :return: The product of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._product

    @product.setter
    def product(self, product):
        """Sets the product of this InstanceListForListAvailableInstancesOutput.


        :param product: The product of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :type: str
        """

        self._product = product

    @property
    def remain_renew_times(self):
        """Gets the remain_renew_times of this InstanceListForListAvailableInstancesOutput.  # noqa: E501


        :return: The remain_renew_times of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._remain_renew_times

    @remain_renew_times.setter
    def remain_renew_times(self, remain_renew_times):
        """Sets the remain_renew_times of this InstanceListForListAvailableInstancesOutput.


        :param remain_renew_times: The remain_renew_times of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :type: str
        """

        self._remain_renew_times = remain_renew_times

    @property
    def renew_type(self):
        """Gets the renew_type of this InstanceListForListAvailableInstancesOutput.  # noqa: E501


        :return: The renew_type of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._renew_type

    @renew_type.setter
    def renew_type(self, renew_type):
        """Sets the renew_type of this InstanceListForListAvailableInstancesOutput.


        :param renew_type: The renew_type of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :type: str
        """

        self._renew_type = renew_type

    @property
    def renewal_duration_unit(self):
        """Gets the renewal_duration_unit of this InstanceListForListAvailableInstancesOutput.  # noqa: E501


        :return: The renewal_duration_unit of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._renewal_duration_unit

    @renewal_duration_unit.setter
    def renewal_duration_unit(self, renewal_duration_unit):
        """Sets the renewal_duration_unit of this InstanceListForListAvailableInstancesOutput.


        :param renewal_duration_unit: The renewal_duration_unit of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :type: str
        """

        self._renewal_duration_unit = renewal_duration_unit

    @property
    def renewal_times(self):
        """Gets the renewal_times of this InstanceListForListAvailableInstancesOutput.  # noqa: E501


        :return: The renewal_times of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._renewal_times

    @renewal_times.setter
    def renewal_times(self, renewal_times):
        """Sets the renewal_times of this InstanceListForListAvailableInstancesOutput.


        :param renewal_times: The renewal_times of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :type: str
        """

        self._renewal_times = renewal_times

    @property
    def status(self):
        """Gets the status of this InstanceListForListAvailableInstancesOutput.  # noqa: E501


        :return: The status of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this InstanceListForListAvailableInstancesOutput.


        :param status: The status of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def sub_status(self):
        """Gets the sub_status of this InstanceListForListAvailableInstancesOutput.  # noqa: E501


        :return: The sub_status of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._sub_status

    @sub_status.setter
    def sub_status(self, sub_status):
        """Sets the sub_status of this InstanceListForListAvailableInstancesOutput.


        :param sub_status: The sub_status of this InstanceListForListAvailableInstancesOutput.  # noqa: E501
        :type: str
        """

        self._sub_status = sub_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceListForListAvailableInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceListForListAvailableInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceListForListAvailableInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
