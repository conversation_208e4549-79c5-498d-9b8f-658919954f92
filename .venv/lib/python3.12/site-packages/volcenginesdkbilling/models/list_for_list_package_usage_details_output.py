# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListForListPackageUsageDetailsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'after_amount': 'str',
        'before_amount': 'str',
        'begin_time': 'str',
        'configuration_code': 'str',
        'configuration_name': 'str',
        'deduct_billing_factor': 'str',
        'deduction_account_id': 'str',
        'deduction_amount': 'str',
        'deduction_calculate_factor': 'str',
        'deduction_element_code': 'str',
        'deduction_instance_no': 'str',
        'deduction_instance_unit': 'str',
        'deduction_product': 'str',
        'deduction_ratio': 'str',
        'deduction_region_code': 'str',
        'deduction_specification': 'str',
        'deduction_specification_unit': 'str',
        'deduction_time': 'str',
        'deduction_use_amount': 'str',
        'deduction_user_name': 'str',
        'end_time': 'str',
        'instance_name': 'str',
        'instance_no': 'str',
        'owner_id': 'str',
        'package_type': 'str',
        'product': 'str',
        'product_name': 'str',
        'region_code': 'str',
        'spec_calculate_factor': 'str',
        'specification': 'str',
        'specification_unit': 'str',
        'subject_no': 'str',
        'unit': 'str',
        'user_name': 'str',
        'zone_code': 'str'
    }

    attribute_map = {
        'after_amount': 'AfterAmount',
        'before_amount': 'BeforeAmount',
        'begin_time': 'BeginTime',
        'configuration_code': 'ConfigurationCode',
        'configuration_name': 'ConfigurationName',
        'deduct_billing_factor': 'DeductBillingFactor',
        'deduction_account_id': 'DeductionAccountID',
        'deduction_amount': 'DeductionAmount',
        'deduction_calculate_factor': 'DeductionCalculateFactor',
        'deduction_element_code': 'DeductionElementCode',
        'deduction_instance_no': 'DeductionInstanceNo',
        'deduction_instance_unit': 'DeductionInstanceUnit',
        'deduction_product': 'DeductionProduct',
        'deduction_ratio': 'DeductionRatio',
        'deduction_region_code': 'DeductionRegionCode',
        'deduction_specification': 'DeductionSpecification',
        'deduction_specification_unit': 'DeductionSpecificationUnit',
        'deduction_time': 'DeductionTime',
        'deduction_use_amount': 'DeductionUseAmount',
        'deduction_user_name': 'DeductionUserName',
        'end_time': 'EndTime',
        'instance_name': 'InstanceName',
        'instance_no': 'InstanceNo',
        'owner_id': 'OwnerID',
        'package_type': 'PackageType',
        'product': 'Product',
        'product_name': 'ProductName',
        'region_code': 'RegionCode',
        'spec_calculate_factor': 'SpecCalculateFactor',
        'specification': 'Specification',
        'specification_unit': 'SpecificationUnit',
        'subject_no': 'SubjectNo',
        'unit': 'Unit',
        'user_name': 'UserName',
        'zone_code': 'ZoneCode'
    }

    def __init__(self, after_amount=None, before_amount=None, begin_time=None, configuration_code=None, configuration_name=None, deduct_billing_factor=None, deduction_account_id=None, deduction_amount=None, deduction_calculate_factor=None, deduction_element_code=None, deduction_instance_no=None, deduction_instance_unit=None, deduction_product=None, deduction_ratio=None, deduction_region_code=None, deduction_specification=None, deduction_specification_unit=None, deduction_time=None, deduction_use_amount=None, deduction_user_name=None, end_time=None, instance_name=None, instance_no=None, owner_id=None, package_type=None, product=None, product_name=None, region_code=None, spec_calculate_factor=None, specification=None, specification_unit=None, subject_no=None, unit=None, user_name=None, zone_code=None, _configuration=None):  # noqa: E501
        """ListForListPackageUsageDetailsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._after_amount = None
        self._before_amount = None
        self._begin_time = None
        self._configuration_code = None
        self._configuration_name = None
        self._deduct_billing_factor = None
        self._deduction_account_id = None
        self._deduction_amount = None
        self._deduction_calculate_factor = None
        self._deduction_element_code = None
        self._deduction_instance_no = None
        self._deduction_instance_unit = None
        self._deduction_product = None
        self._deduction_ratio = None
        self._deduction_region_code = None
        self._deduction_specification = None
        self._deduction_specification_unit = None
        self._deduction_time = None
        self._deduction_use_amount = None
        self._deduction_user_name = None
        self._end_time = None
        self._instance_name = None
        self._instance_no = None
        self._owner_id = None
        self._package_type = None
        self._product = None
        self._product_name = None
        self._region_code = None
        self._spec_calculate_factor = None
        self._specification = None
        self._specification_unit = None
        self._subject_no = None
        self._unit = None
        self._user_name = None
        self._zone_code = None
        self.discriminator = None

        if after_amount is not None:
            self.after_amount = after_amount
        if before_amount is not None:
            self.before_amount = before_amount
        if begin_time is not None:
            self.begin_time = begin_time
        if configuration_code is not None:
            self.configuration_code = configuration_code
        if configuration_name is not None:
            self.configuration_name = configuration_name
        if deduct_billing_factor is not None:
            self.deduct_billing_factor = deduct_billing_factor
        if deduction_account_id is not None:
            self.deduction_account_id = deduction_account_id
        if deduction_amount is not None:
            self.deduction_amount = deduction_amount
        if deduction_calculate_factor is not None:
            self.deduction_calculate_factor = deduction_calculate_factor
        if deduction_element_code is not None:
            self.deduction_element_code = deduction_element_code
        if deduction_instance_no is not None:
            self.deduction_instance_no = deduction_instance_no
        if deduction_instance_unit is not None:
            self.deduction_instance_unit = deduction_instance_unit
        if deduction_product is not None:
            self.deduction_product = deduction_product
        if deduction_ratio is not None:
            self.deduction_ratio = deduction_ratio
        if deduction_region_code is not None:
            self.deduction_region_code = deduction_region_code
        if deduction_specification is not None:
            self.deduction_specification = deduction_specification
        if deduction_specification_unit is not None:
            self.deduction_specification_unit = deduction_specification_unit
        if deduction_time is not None:
            self.deduction_time = deduction_time
        if deduction_use_amount is not None:
            self.deduction_use_amount = deduction_use_amount
        if deduction_user_name is not None:
            self.deduction_user_name = deduction_user_name
        if end_time is not None:
            self.end_time = end_time
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_no is not None:
            self.instance_no = instance_no
        if owner_id is not None:
            self.owner_id = owner_id
        if package_type is not None:
            self.package_type = package_type
        if product is not None:
            self.product = product
        if product_name is not None:
            self.product_name = product_name
        if region_code is not None:
            self.region_code = region_code
        if spec_calculate_factor is not None:
            self.spec_calculate_factor = spec_calculate_factor
        if specification is not None:
            self.specification = specification
        if specification_unit is not None:
            self.specification_unit = specification_unit
        if subject_no is not None:
            self.subject_no = subject_no
        if unit is not None:
            self.unit = unit
        if user_name is not None:
            self.user_name = user_name
        if zone_code is not None:
            self.zone_code = zone_code

    @property
    def after_amount(self):
        """Gets the after_amount of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The after_amount of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._after_amount

    @after_amount.setter
    def after_amount(self, after_amount):
        """Sets the after_amount of this ListForListPackageUsageDetailsOutput.


        :param after_amount: The after_amount of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._after_amount = after_amount

    @property
    def before_amount(self):
        """Gets the before_amount of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The before_amount of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._before_amount

    @before_amount.setter
    def before_amount(self, before_amount):
        """Sets the before_amount of this ListForListPackageUsageDetailsOutput.


        :param before_amount: The before_amount of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._before_amount = before_amount

    @property
    def begin_time(self):
        """Gets the begin_time of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The begin_time of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._begin_time

    @begin_time.setter
    def begin_time(self, begin_time):
        """Sets the begin_time of this ListForListPackageUsageDetailsOutput.


        :param begin_time: The begin_time of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._begin_time = begin_time

    @property
    def configuration_code(self):
        """Gets the configuration_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The configuration_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._configuration_code

    @configuration_code.setter
    def configuration_code(self, configuration_code):
        """Sets the configuration_code of this ListForListPackageUsageDetailsOutput.


        :param configuration_code: The configuration_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._configuration_code = configuration_code

    @property
    def configuration_name(self):
        """Gets the configuration_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The configuration_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._configuration_name

    @configuration_name.setter
    def configuration_name(self, configuration_name):
        """Sets the configuration_name of this ListForListPackageUsageDetailsOutput.


        :param configuration_name: The configuration_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._configuration_name = configuration_name

    @property
    def deduct_billing_factor(self):
        """Gets the deduct_billing_factor of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduct_billing_factor of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduct_billing_factor

    @deduct_billing_factor.setter
    def deduct_billing_factor(self, deduct_billing_factor):
        """Sets the deduct_billing_factor of this ListForListPackageUsageDetailsOutput.


        :param deduct_billing_factor: The deduct_billing_factor of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduct_billing_factor = deduct_billing_factor

    @property
    def deduction_account_id(self):
        """Gets the deduction_account_id of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduction_account_id of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_account_id

    @deduction_account_id.setter
    def deduction_account_id(self, deduction_account_id):
        """Sets the deduction_account_id of this ListForListPackageUsageDetailsOutput.


        :param deduction_account_id: The deduction_account_id of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduction_account_id = deduction_account_id

    @property
    def deduction_amount(self):
        """Gets the deduction_amount of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduction_amount of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_amount

    @deduction_amount.setter
    def deduction_amount(self, deduction_amount):
        """Sets the deduction_amount of this ListForListPackageUsageDetailsOutput.


        :param deduction_amount: The deduction_amount of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduction_amount = deduction_amount

    @property
    def deduction_calculate_factor(self):
        """Gets the deduction_calculate_factor of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduction_calculate_factor of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_calculate_factor

    @deduction_calculate_factor.setter
    def deduction_calculate_factor(self, deduction_calculate_factor):
        """Sets the deduction_calculate_factor of this ListForListPackageUsageDetailsOutput.


        :param deduction_calculate_factor: The deduction_calculate_factor of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduction_calculate_factor = deduction_calculate_factor

    @property
    def deduction_element_code(self):
        """Gets the deduction_element_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduction_element_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_element_code

    @deduction_element_code.setter
    def deduction_element_code(self, deduction_element_code):
        """Sets the deduction_element_code of this ListForListPackageUsageDetailsOutput.


        :param deduction_element_code: The deduction_element_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduction_element_code = deduction_element_code

    @property
    def deduction_instance_no(self):
        """Gets the deduction_instance_no of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduction_instance_no of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_instance_no

    @deduction_instance_no.setter
    def deduction_instance_no(self, deduction_instance_no):
        """Sets the deduction_instance_no of this ListForListPackageUsageDetailsOutput.


        :param deduction_instance_no: The deduction_instance_no of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduction_instance_no = deduction_instance_no

    @property
    def deduction_instance_unit(self):
        """Gets the deduction_instance_unit of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduction_instance_unit of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_instance_unit

    @deduction_instance_unit.setter
    def deduction_instance_unit(self, deduction_instance_unit):
        """Sets the deduction_instance_unit of this ListForListPackageUsageDetailsOutput.


        :param deduction_instance_unit: The deduction_instance_unit of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduction_instance_unit = deduction_instance_unit

    @property
    def deduction_product(self):
        """Gets the deduction_product of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduction_product of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_product

    @deduction_product.setter
    def deduction_product(self, deduction_product):
        """Sets the deduction_product of this ListForListPackageUsageDetailsOutput.


        :param deduction_product: The deduction_product of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduction_product = deduction_product

    @property
    def deduction_ratio(self):
        """Gets the deduction_ratio of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduction_ratio of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_ratio

    @deduction_ratio.setter
    def deduction_ratio(self, deduction_ratio):
        """Sets the deduction_ratio of this ListForListPackageUsageDetailsOutput.


        :param deduction_ratio: The deduction_ratio of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduction_ratio = deduction_ratio

    @property
    def deduction_region_code(self):
        """Gets the deduction_region_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduction_region_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_region_code

    @deduction_region_code.setter
    def deduction_region_code(self, deduction_region_code):
        """Sets the deduction_region_code of this ListForListPackageUsageDetailsOutput.


        :param deduction_region_code: The deduction_region_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduction_region_code = deduction_region_code

    @property
    def deduction_specification(self):
        """Gets the deduction_specification of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduction_specification of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_specification

    @deduction_specification.setter
    def deduction_specification(self, deduction_specification):
        """Sets the deduction_specification of this ListForListPackageUsageDetailsOutput.


        :param deduction_specification: The deduction_specification of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduction_specification = deduction_specification

    @property
    def deduction_specification_unit(self):
        """Gets the deduction_specification_unit of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduction_specification_unit of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_specification_unit

    @deduction_specification_unit.setter
    def deduction_specification_unit(self, deduction_specification_unit):
        """Sets the deduction_specification_unit of this ListForListPackageUsageDetailsOutput.


        :param deduction_specification_unit: The deduction_specification_unit of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduction_specification_unit = deduction_specification_unit

    @property
    def deduction_time(self):
        """Gets the deduction_time of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduction_time of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_time

    @deduction_time.setter
    def deduction_time(self, deduction_time):
        """Sets the deduction_time of this ListForListPackageUsageDetailsOutput.


        :param deduction_time: The deduction_time of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduction_time = deduction_time

    @property
    def deduction_use_amount(self):
        """Gets the deduction_use_amount of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduction_use_amount of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_use_amount

    @deduction_use_amount.setter
    def deduction_use_amount(self, deduction_use_amount):
        """Sets the deduction_use_amount of this ListForListPackageUsageDetailsOutput.


        :param deduction_use_amount: The deduction_use_amount of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduction_use_amount = deduction_use_amount

    @property
    def deduction_user_name(self):
        """Gets the deduction_user_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The deduction_user_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_user_name

    @deduction_user_name.setter
    def deduction_user_name(self, deduction_user_name):
        """Sets the deduction_user_name of this ListForListPackageUsageDetailsOutput.


        :param deduction_user_name: The deduction_user_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._deduction_user_name = deduction_user_name

    @property
    def end_time(self):
        """Gets the end_time of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The end_time of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this ListForListPackageUsageDetailsOutput.


        :param end_time: The end_time of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._end_time = end_time

    @property
    def instance_name(self):
        """Gets the instance_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The instance_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this ListForListPackageUsageDetailsOutput.


        :param instance_name: The instance_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_no(self):
        """Gets the instance_no of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The instance_no of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_no

    @instance_no.setter
    def instance_no(self, instance_no):
        """Sets the instance_no of this ListForListPackageUsageDetailsOutput.


        :param instance_no: The instance_no of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._instance_no = instance_no

    @property
    def owner_id(self):
        """Gets the owner_id of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The owner_id of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_id

    @owner_id.setter
    def owner_id(self, owner_id):
        """Sets the owner_id of this ListForListPackageUsageDetailsOutput.


        :param owner_id: The owner_id of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._owner_id = owner_id

    @property
    def package_type(self):
        """Gets the package_type of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The package_type of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._package_type

    @package_type.setter
    def package_type(self, package_type):
        """Sets the package_type of this ListForListPackageUsageDetailsOutput.


        :param package_type: The package_type of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._package_type = package_type

    @property
    def product(self):
        """Gets the product of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The product of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._product

    @product.setter
    def product(self, product):
        """Sets the product of this ListForListPackageUsageDetailsOutput.


        :param product: The product of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._product = product

    @property
    def product_name(self):
        """Gets the product_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The product_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._product_name

    @product_name.setter
    def product_name(self, product_name):
        """Sets the product_name of this ListForListPackageUsageDetailsOutput.


        :param product_name: The product_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._product_name = product_name

    @property
    def region_code(self):
        """Gets the region_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The region_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_code

    @region_code.setter
    def region_code(self, region_code):
        """Sets the region_code of this ListForListPackageUsageDetailsOutput.


        :param region_code: The region_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._region_code = region_code

    @property
    def spec_calculate_factor(self):
        """Gets the spec_calculate_factor of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The spec_calculate_factor of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._spec_calculate_factor

    @spec_calculate_factor.setter
    def spec_calculate_factor(self, spec_calculate_factor):
        """Sets the spec_calculate_factor of this ListForListPackageUsageDetailsOutput.


        :param spec_calculate_factor: The spec_calculate_factor of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._spec_calculate_factor = spec_calculate_factor

    @property
    def specification(self):
        """Gets the specification of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The specification of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._specification

    @specification.setter
    def specification(self, specification):
        """Sets the specification of this ListForListPackageUsageDetailsOutput.


        :param specification: The specification of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._specification = specification

    @property
    def specification_unit(self):
        """Gets the specification_unit of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The specification_unit of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._specification_unit

    @specification_unit.setter
    def specification_unit(self, specification_unit):
        """Sets the specification_unit of this ListForListPackageUsageDetailsOutput.


        :param specification_unit: The specification_unit of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._specification_unit = specification_unit

    @property
    def subject_no(self):
        """Gets the subject_no of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The subject_no of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._subject_no

    @subject_no.setter
    def subject_no(self, subject_no):
        """Sets the subject_no of this ListForListPackageUsageDetailsOutput.


        :param subject_no: The subject_no of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._subject_no = subject_no

    @property
    def unit(self):
        """Gets the unit of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The unit of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._unit

    @unit.setter
    def unit(self, unit):
        """Sets the unit of this ListForListPackageUsageDetailsOutput.


        :param unit: The unit of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._unit = unit

    @property
    def user_name(self):
        """Gets the user_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The user_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this ListForListPackageUsageDetailsOutput.


        :param user_name: The user_name of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    @property
    def zone_code(self):
        """Gets the zone_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501


        :return: The zone_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_code

    @zone_code.setter
    def zone_code(self, zone_code):
        """Sets the zone_code of this ListForListPackageUsageDetailsOutput.


        :param zone_code: The zone_code of this ListForListPackageUsageDetailsOutput.  # noqa: E501
        :type: str
        """

        self._zone_code = zone_code

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListForListPackageUsageDetailsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListForListPackageUsageDetailsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListForListPackageUsageDetailsOutput):
            return True

        return self.to_dict() != other.to_dict()
