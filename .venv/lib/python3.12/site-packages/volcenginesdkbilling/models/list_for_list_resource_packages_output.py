# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListForListResourcePackagesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'available_amount': 'str',
        'bill_time': 'str',
        'configuration_code': 'str',
        'configuration_name': 'str',
        'effective_time': 'str',
        'expiry_time': 'str',
        'instance_name': 'str',
        'instance_no': 'str',
        'owner_id': 'str',
        'package_type': 'str',
        'product': 'str',
        'product_name': 'str',
        'region_code': 'str',
        'reset_by_natural_month': 'str',
        'reset_period': 'str',
        'spec_calculate_factor': 'str',
        'specification': 'str',
        'specification_unit': 'str',
        'status': 'str',
        'subject_no': 'str',
        'total_amount': 'str',
        'unit': 'str',
        'user_name': 'str',
        'zone_code': 'str'
    }

    attribute_map = {
        'available_amount': 'AvailableAmount',
        'bill_time': 'BillTime',
        'configuration_code': 'ConfigurationCode',
        'configuration_name': 'ConfigurationName',
        'effective_time': 'EffectiveTime',
        'expiry_time': 'ExpiryTime',
        'instance_name': 'InstanceName',
        'instance_no': 'InstanceNo',
        'owner_id': 'OwnerID',
        'package_type': 'PackageType',
        'product': 'Product',
        'product_name': 'ProductName',
        'region_code': 'RegionCode',
        'reset_by_natural_month': 'ResetByNaturalMonth',
        'reset_period': 'ResetPeriod',
        'spec_calculate_factor': 'SpecCalculateFactor',
        'specification': 'Specification',
        'specification_unit': 'SpecificationUnit',
        'status': 'Status',
        'subject_no': 'SubjectNo',
        'total_amount': 'TotalAmount',
        'unit': 'Unit',
        'user_name': 'UserName',
        'zone_code': 'ZoneCode'
    }

    def __init__(self, available_amount=None, bill_time=None, configuration_code=None, configuration_name=None, effective_time=None, expiry_time=None, instance_name=None, instance_no=None, owner_id=None, package_type=None, product=None, product_name=None, region_code=None, reset_by_natural_month=None, reset_period=None, spec_calculate_factor=None, specification=None, specification_unit=None, status=None, subject_no=None, total_amount=None, unit=None, user_name=None, zone_code=None, _configuration=None):  # noqa: E501
        """ListForListResourcePackagesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._available_amount = None
        self._bill_time = None
        self._configuration_code = None
        self._configuration_name = None
        self._effective_time = None
        self._expiry_time = None
        self._instance_name = None
        self._instance_no = None
        self._owner_id = None
        self._package_type = None
        self._product = None
        self._product_name = None
        self._region_code = None
        self._reset_by_natural_month = None
        self._reset_period = None
        self._spec_calculate_factor = None
        self._specification = None
        self._specification_unit = None
        self._status = None
        self._subject_no = None
        self._total_amount = None
        self._unit = None
        self._user_name = None
        self._zone_code = None
        self.discriminator = None

        if available_amount is not None:
            self.available_amount = available_amount
        if bill_time is not None:
            self.bill_time = bill_time
        if configuration_code is not None:
            self.configuration_code = configuration_code
        if configuration_name is not None:
            self.configuration_name = configuration_name
        if effective_time is not None:
            self.effective_time = effective_time
        if expiry_time is not None:
            self.expiry_time = expiry_time
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_no is not None:
            self.instance_no = instance_no
        if owner_id is not None:
            self.owner_id = owner_id
        if package_type is not None:
            self.package_type = package_type
        if product is not None:
            self.product = product
        if product_name is not None:
            self.product_name = product_name
        if region_code is not None:
            self.region_code = region_code
        if reset_by_natural_month is not None:
            self.reset_by_natural_month = reset_by_natural_month
        if reset_period is not None:
            self.reset_period = reset_period
        if spec_calculate_factor is not None:
            self.spec_calculate_factor = spec_calculate_factor
        if specification is not None:
            self.specification = specification
        if specification_unit is not None:
            self.specification_unit = specification_unit
        if status is not None:
            self.status = status
        if subject_no is not None:
            self.subject_no = subject_no
        if total_amount is not None:
            self.total_amount = total_amount
        if unit is not None:
            self.unit = unit
        if user_name is not None:
            self.user_name = user_name
        if zone_code is not None:
            self.zone_code = zone_code

    @property
    def available_amount(self):
        """Gets the available_amount of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The available_amount of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._available_amount

    @available_amount.setter
    def available_amount(self, available_amount):
        """Sets the available_amount of this ListForListResourcePackagesOutput.


        :param available_amount: The available_amount of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._available_amount = available_amount

    @property
    def bill_time(self):
        """Gets the bill_time of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The bill_time of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_time

    @bill_time.setter
    def bill_time(self, bill_time):
        """Sets the bill_time of this ListForListResourcePackagesOutput.


        :param bill_time: The bill_time of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._bill_time = bill_time

    @property
    def configuration_code(self):
        """Gets the configuration_code of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The configuration_code of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._configuration_code

    @configuration_code.setter
    def configuration_code(self, configuration_code):
        """Sets the configuration_code of this ListForListResourcePackagesOutput.


        :param configuration_code: The configuration_code of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._configuration_code = configuration_code

    @property
    def configuration_name(self):
        """Gets the configuration_name of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The configuration_name of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._configuration_name

    @configuration_name.setter
    def configuration_name(self, configuration_name):
        """Sets the configuration_name of this ListForListResourcePackagesOutput.


        :param configuration_name: The configuration_name of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._configuration_name = configuration_name

    @property
    def effective_time(self):
        """Gets the effective_time of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The effective_time of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._effective_time

    @effective_time.setter
    def effective_time(self, effective_time):
        """Sets the effective_time of this ListForListResourcePackagesOutput.


        :param effective_time: The effective_time of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._effective_time = effective_time

    @property
    def expiry_time(self):
        """Gets the expiry_time of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The expiry_time of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._expiry_time

    @expiry_time.setter
    def expiry_time(self, expiry_time):
        """Sets the expiry_time of this ListForListResourcePackagesOutput.


        :param expiry_time: The expiry_time of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._expiry_time = expiry_time

    @property
    def instance_name(self):
        """Gets the instance_name of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The instance_name of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this ListForListResourcePackagesOutput.


        :param instance_name: The instance_name of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_no(self):
        """Gets the instance_no of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The instance_no of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_no

    @instance_no.setter
    def instance_no(self, instance_no):
        """Sets the instance_no of this ListForListResourcePackagesOutput.


        :param instance_no: The instance_no of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._instance_no = instance_no

    @property
    def owner_id(self):
        """Gets the owner_id of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The owner_id of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_id

    @owner_id.setter
    def owner_id(self, owner_id):
        """Sets the owner_id of this ListForListResourcePackagesOutput.


        :param owner_id: The owner_id of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._owner_id = owner_id

    @property
    def package_type(self):
        """Gets the package_type of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The package_type of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._package_type

    @package_type.setter
    def package_type(self, package_type):
        """Sets the package_type of this ListForListResourcePackagesOutput.


        :param package_type: The package_type of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._package_type = package_type

    @property
    def product(self):
        """Gets the product of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The product of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._product

    @product.setter
    def product(self, product):
        """Sets the product of this ListForListResourcePackagesOutput.


        :param product: The product of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._product = product

    @property
    def product_name(self):
        """Gets the product_name of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The product_name of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._product_name

    @product_name.setter
    def product_name(self, product_name):
        """Sets the product_name of this ListForListResourcePackagesOutput.


        :param product_name: The product_name of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._product_name = product_name

    @property
    def region_code(self):
        """Gets the region_code of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The region_code of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_code

    @region_code.setter
    def region_code(self, region_code):
        """Sets the region_code of this ListForListResourcePackagesOutput.


        :param region_code: The region_code of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._region_code = region_code

    @property
    def reset_by_natural_month(self):
        """Gets the reset_by_natural_month of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The reset_by_natural_month of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._reset_by_natural_month

    @reset_by_natural_month.setter
    def reset_by_natural_month(self, reset_by_natural_month):
        """Sets the reset_by_natural_month of this ListForListResourcePackagesOutput.


        :param reset_by_natural_month: The reset_by_natural_month of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._reset_by_natural_month = reset_by_natural_month

    @property
    def reset_period(self):
        """Gets the reset_period of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The reset_period of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._reset_period

    @reset_period.setter
    def reset_period(self, reset_period):
        """Sets the reset_period of this ListForListResourcePackagesOutput.


        :param reset_period: The reset_period of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._reset_period = reset_period

    @property
    def spec_calculate_factor(self):
        """Gets the spec_calculate_factor of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The spec_calculate_factor of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._spec_calculate_factor

    @spec_calculate_factor.setter
    def spec_calculate_factor(self, spec_calculate_factor):
        """Sets the spec_calculate_factor of this ListForListResourcePackagesOutput.


        :param spec_calculate_factor: The spec_calculate_factor of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._spec_calculate_factor = spec_calculate_factor

    @property
    def specification(self):
        """Gets the specification of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The specification of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._specification

    @specification.setter
    def specification(self, specification):
        """Sets the specification of this ListForListResourcePackagesOutput.


        :param specification: The specification of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._specification = specification

    @property
    def specification_unit(self):
        """Gets the specification_unit of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The specification_unit of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._specification_unit

    @specification_unit.setter
    def specification_unit(self, specification_unit):
        """Sets the specification_unit of this ListForListResourcePackagesOutput.


        :param specification_unit: The specification_unit of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._specification_unit = specification_unit

    @property
    def status(self):
        """Gets the status of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The status of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListForListResourcePackagesOutput.


        :param status: The status of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def subject_no(self):
        """Gets the subject_no of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The subject_no of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._subject_no

    @subject_no.setter
    def subject_no(self, subject_no):
        """Sets the subject_no of this ListForListResourcePackagesOutput.


        :param subject_no: The subject_no of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._subject_no = subject_no

    @property
    def total_amount(self):
        """Gets the total_amount of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The total_amount of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._total_amount

    @total_amount.setter
    def total_amount(self, total_amount):
        """Sets the total_amount of this ListForListResourcePackagesOutput.


        :param total_amount: The total_amount of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._total_amount = total_amount

    @property
    def unit(self):
        """Gets the unit of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The unit of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._unit

    @unit.setter
    def unit(self, unit):
        """Sets the unit of this ListForListResourcePackagesOutput.


        :param unit: The unit of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._unit = unit

    @property
    def user_name(self):
        """Gets the user_name of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The user_name of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this ListForListResourcePackagesOutput.


        :param user_name: The user_name of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    @property
    def zone_code(self):
        """Gets the zone_code of this ListForListResourcePackagesOutput.  # noqa: E501


        :return: The zone_code of this ListForListResourcePackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_code

    @zone_code.setter
    def zone_code(self, zone_code):
        """Sets the zone_code of this ListForListResourcePackagesOutput.


        :param zone_code: The zone_code of this ListForListResourcePackagesOutput.  # noqa: E501
        :type: str
        """

        self._zone_code = zone_code

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListForListResourcePackagesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListForListResourcePackagesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListForListResourcePackagesOutput):
            return True

        return self.to_dict() != other.to_dict()
