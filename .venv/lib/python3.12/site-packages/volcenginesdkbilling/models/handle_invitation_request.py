# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HandleInvitationRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action': 'int',
        'auth_id': 'str',
        'invitation_type': 'int',
        'major_account_id': 'int',
        'relation': 'int',
        'relation_id': 'str'
    }

    attribute_map = {
        'action': 'Action',
        'auth_id': 'AuthID',
        'invitation_type': 'InvitationType',
        'major_account_id': 'MajorAccountID',
        'relation': 'Relation',
        'relation_id': 'RelationID'
    }

    def __init__(self, action=None, auth_id=None, invitation_type=None, major_account_id=None, relation=None, relation_id=None, _configuration=None):  # noqa: E501
        """HandleInvitationRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action = None
        self._auth_id = None
        self._invitation_type = None
        self._major_account_id = None
        self._relation = None
        self._relation_id = None
        self.discriminator = None

        self.action = action
        if auth_id is not None:
            self.auth_id = auth_id
        self.invitation_type = invitation_type
        if major_account_id is not None:
            self.major_account_id = major_account_id
        if relation is not None:
            self.relation = relation
        self.relation_id = relation_id

    @property
    def action(self):
        """Gets the action of this HandleInvitationRequest.  # noqa: E501


        :return: The action of this HandleInvitationRequest.  # noqa: E501
        :rtype: int
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this HandleInvitationRequest.


        :param action: The action of this HandleInvitationRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and action is None:
            raise ValueError("Invalid value for `action`, must not be `None`")  # noqa: E501

        self._action = action

    @property
    def auth_id(self):
        """Gets the auth_id of this HandleInvitationRequest.  # noqa: E501


        :return: The auth_id of this HandleInvitationRequest.  # noqa: E501
        :rtype: str
        """
        return self._auth_id

    @auth_id.setter
    def auth_id(self, auth_id):
        """Sets the auth_id of this HandleInvitationRequest.


        :param auth_id: The auth_id of this HandleInvitationRequest.  # noqa: E501
        :type: str
        """

        self._auth_id = auth_id

    @property
    def invitation_type(self):
        """Gets the invitation_type of this HandleInvitationRequest.  # noqa: E501


        :return: The invitation_type of this HandleInvitationRequest.  # noqa: E501
        :rtype: int
        """
        return self._invitation_type

    @invitation_type.setter
    def invitation_type(self, invitation_type):
        """Sets the invitation_type of this HandleInvitationRequest.


        :param invitation_type: The invitation_type of this HandleInvitationRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and invitation_type is None:
            raise ValueError("Invalid value for `invitation_type`, must not be `None`")  # noqa: E501

        self._invitation_type = invitation_type

    @property
    def major_account_id(self):
        """Gets the major_account_id of this HandleInvitationRequest.  # noqa: E501


        :return: The major_account_id of this HandleInvitationRequest.  # noqa: E501
        :rtype: int
        """
        return self._major_account_id

    @major_account_id.setter
    def major_account_id(self, major_account_id):
        """Sets the major_account_id of this HandleInvitationRequest.


        :param major_account_id: The major_account_id of this HandleInvitationRequest.  # noqa: E501
        :type: int
        """

        self._major_account_id = major_account_id

    @property
    def relation(self):
        """Gets the relation of this HandleInvitationRequest.  # noqa: E501


        :return: The relation of this HandleInvitationRequest.  # noqa: E501
        :rtype: int
        """
        return self._relation

    @relation.setter
    def relation(self, relation):
        """Sets the relation of this HandleInvitationRequest.


        :param relation: The relation of this HandleInvitationRequest.  # noqa: E501
        :type: int
        """

        self._relation = relation

    @property
    def relation_id(self):
        """Gets the relation_id of this HandleInvitationRequest.  # noqa: E501


        :return: The relation_id of this HandleInvitationRequest.  # noqa: E501
        :rtype: str
        """
        return self._relation_id

    @relation_id.setter
    def relation_id(self, relation_id):
        """Sets the relation_id of this HandleInvitationRequest.


        :param relation_id: The relation_id of this HandleInvitationRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and relation_id is None:
            raise ValueError("Invalid value for `relation_id`, must not be `None`")  # noqa: E501

        self._relation_id = relation_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HandleInvitationRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HandleInvitationRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HandleInvitationRequest):
            return True

        return self.to_dict() != other.to_dict()
