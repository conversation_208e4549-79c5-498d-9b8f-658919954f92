# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AuthForListInvitationOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auth_id': 'str',
        'auth_list': 'list[int]',
        'major_account_id': 'int',
        'relation_id': 'str',
        'sub_account_id': 'int'
    }

    attribute_map = {
        'auth_id': 'AuthID',
        'auth_list': 'AuthList',
        'major_account_id': 'MajorAccountID',
        'relation_id': 'RelationID',
        'sub_account_id': 'SubAccountID'
    }

    def __init__(self, auth_id=None, auth_list=None, major_account_id=None, relation_id=None, sub_account_id=None, _configuration=None):  # noqa: E501
        """AuthForListInvitationOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auth_id = None
        self._auth_list = None
        self._major_account_id = None
        self._relation_id = None
        self._sub_account_id = None
        self.discriminator = None

        if auth_id is not None:
            self.auth_id = auth_id
        if auth_list is not None:
            self.auth_list = auth_list
        if major_account_id is not None:
            self.major_account_id = major_account_id
        if relation_id is not None:
            self.relation_id = relation_id
        if sub_account_id is not None:
            self.sub_account_id = sub_account_id

    @property
    def auth_id(self):
        """Gets the auth_id of this AuthForListInvitationOutput.  # noqa: E501


        :return: The auth_id of this AuthForListInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._auth_id

    @auth_id.setter
    def auth_id(self, auth_id):
        """Sets the auth_id of this AuthForListInvitationOutput.


        :param auth_id: The auth_id of this AuthForListInvitationOutput.  # noqa: E501
        :type: str
        """

        self._auth_id = auth_id

    @property
    def auth_list(self):
        """Gets the auth_list of this AuthForListInvitationOutput.  # noqa: E501


        :return: The auth_list of this AuthForListInvitationOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._auth_list

    @auth_list.setter
    def auth_list(self, auth_list):
        """Sets the auth_list of this AuthForListInvitationOutput.


        :param auth_list: The auth_list of this AuthForListInvitationOutput.  # noqa: E501
        :type: list[int]
        """

        self._auth_list = auth_list

    @property
    def major_account_id(self):
        """Gets the major_account_id of this AuthForListInvitationOutput.  # noqa: E501


        :return: The major_account_id of this AuthForListInvitationOutput.  # noqa: E501
        :rtype: int
        """
        return self._major_account_id

    @major_account_id.setter
    def major_account_id(self, major_account_id):
        """Sets the major_account_id of this AuthForListInvitationOutput.


        :param major_account_id: The major_account_id of this AuthForListInvitationOutput.  # noqa: E501
        :type: int
        """

        self._major_account_id = major_account_id

    @property
    def relation_id(self):
        """Gets the relation_id of this AuthForListInvitationOutput.  # noqa: E501


        :return: The relation_id of this AuthForListInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._relation_id

    @relation_id.setter
    def relation_id(self, relation_id):
        """Sets the relation_id of this AuthForListInvitationOutput.


        :param relation_id: The relation_id of this AuthForListInvitationOutput.  # noqa: E501
        :type: str
        """

        self._relation_id = relation_id

    @property
    def sub_account_id(self):
        """Gets the sub_account_id of this AuthForListInvitationOutput.  # noqa: E501


        :return: The sub_account_id of this AuthForListInvitationOutput.  # noqa: E501
        :rtype: int
        """
        return self._sub_account_id

    @sub_account_id.setter
    def sub_account_id(self, sub_account_id):
        """Sets the sub_account_id of this AuthForListInvitationOutput.


        :param sub_account_id: The sub_account_id of this AuthForListInvitationOutput.  # noqa: E501
        :type: int
        """

        self._sub_account_id = sub_account_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AuthForListInvitationOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AuthForListInvitationOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AuthForListInvitationOutput):
            return True

        return self.to_dict() != other.to_dict()
