# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListPackageUsageDetailsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'deduct_begin_time': 'str',
        'deduct_end_time': 'str',
        'instance_no': 'str',
        'max_results': 'str',
        'next_token': 'str',
        'resource_type': 'str'
    }

    attribute_map = {
        'deduct_begin_time': 'DeductBeginTime',
        'deduct_end_time': 'DeductEndTime',
        'instance_no': 'InstanceNo',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'resource_type': 'ResourceType'
    }

    def __init__(self, deduct_begin_time=None, deduct_end_time=None, instance_no=None, max_results=None, next_token=None, resource_type=None, _configuration=None):  # noqa: E501
        """ListPackageUsageDetailsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._deduct_begin_time = None
        self._deduct_end_time = None
        self._instance_no = None
        self._max_results = None
        self._next_token = None
        self._resource_type = None
        self.discriminator = None

        self.deduct_begin_time = deduct_begin_time
        self.deduct_end_time = deduct_end_time
        if instance_no is not None:
            self.instance_no = instance_no
        self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        self.resource_type = resource_type

    @property
    def deduct_begin_time(self):
        """Gets the deduct_begin_time of this ListPackageUsageDetailsRequest.  # noqa: E501


        :return: The deduct_begin_time of this ListPackageUsageDetailsRequest.  # noqa: E501
        :rtype: str
        """
        return self._deduct_begin_time

    @deduct_begin_time.setter
    def deduct_begin_time(self, deduct_begin_time):
        """Sets the deduct_begin_time of this ListPackageUsageDetailsRequest.


        :param deduct_begin_time: The deduct_begin_time of this ListPackageUsageDetailsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and deduct_begin_time is None:
            raise ValueError("Invalid value for `deduct_begin_time`, must not be `None`")  # noqa: E501

        self._deduct_begin_time = deduct_begin_time

    @property
    def deduct_end_time(self):
        """Gets the deduct_end_time of this ListPackageUsageDetailsRequest.  # noqa: E501


        :return: The deduct_end_time of this ListPackageUsageDetailsRequest.  # noqa: E501
        :rtype: str
        """
        return self._deduct_end_time

    @deduct_end_time.setter
    def deduct_end_time(self, deduct_end_time):
        """Sets the deduct_end_time of this ListPackageUsageDetailsRequest.


        :param deduct_end_time: The deduct_end_time of this ListPackageUsageDetailsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and deduct_end_time is None:
            raise ValueError("Invalid value for `deduct_end_time`, must not be `None`")  # noqa: E501

        self._deduct_end_time = deduct_end_time

    @property
    def instance_no(self):
        """Gets the instance_no of this ListPackageUsageDetailsRequest.  # noqa: E501


        :return: The instance_no of this ListPackageUsageDetailsRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_no

    @instance_no.setter
    def instance_no(self, instance_no):
        """Sets the instance_no of this ListPackageUsageDetailsRequest.


        :param instance_no: The instance_no of this ListPackageUsageDetailsRequest.  # noqa: E501
        :type: str
        """

        self._instance_no = instance_no

    @property
    def max_results(self):
        """Gets the max_results of this ListPackageUsageDetailsRequest.  # noqa: E501


        :return: The max_results of this ListPackageUsageDetailsRequest.  # noqa: E501
        :rtype: str
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this ListPackageUsageDetailsRequest.


        :param max_results: The max_results of this ListPackageUsageDetailsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and max_results is None:
            raise ValueError("Invalid value for `max_results`, must not be `None`")  # noqa: E501

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this ListPackageUsageDetailsRequest.  # noqa: E501


        :return: The next_token of this ListPackageUsageDetailsRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListPackageUsageDetailsRequest.


        :param next_token: The next_token of this ListPackageUsageDetailsRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def resource_type(self):
        """Gets the resource_type of this ListPackageUsageDetailsRequest.  # noqa: E501


        :return: The resource_type of this ListPackageUsageDetailsRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this ListPackageUsageDetailsRequest.


        :param resource_type: The resource_type of this ListPackageUsageDetailsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and resource_type is None:
            raise ValueError("Invalid value for `resource_type`, must not be `None`")  # noqa: E501
        allowed_values = ["Package", "RI", "RSC"]  # noqa: E501
        if (self._configuration.client_side_validation and
                resource_type not in allowed_values):
            raise ValueError(
                "Invalid value for `resource_type` ({0}), must be one of {1}"  # noqa: E501
                .format(resource_type, allowed_values)
            )

        self._resource_type = resource_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListPackageUsageDetailsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListPackageUsageDetailsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListPackageUsageDetailsRequest):
            return True

        return self.to_dict() != other.to_dict()
