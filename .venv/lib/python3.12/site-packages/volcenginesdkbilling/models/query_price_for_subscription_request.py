# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryPriceForSubscriptionRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'config_list': 'list[ConfigListForQueryPriceForSubscriptionInput]',
        'product': 'str'
    }

    attribute_map = {
        'config_list': 'ConfigList',
        'product': 'Product'
    }

    def __init__(self, config_list=None, product=None, _configuration=None):  # noqa: E501
        """QueryPriceForSubscriptionRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._config_list = None
        self._product = None
        self.discriminator = None

        if config_list is not None:
            self.config_list = config_list
        self.product = product

    @property
    def config_list(self):
        """Gets the config_list of this QueryPriceForSubscriptionRequest.  # noqa: E501


        :return: The config_list of this QueryPriceForSubscriptionRequest.  # noqa: E501
        :rtype: list[ConfigListForQueryPriceForSubscriptionInput]
        """
        return self._config_list

    @config_list.setter
    def config_list(self, config_list):
        """Sets the config_list of this QueryPriceForSubscriptionRequest.


        :param config_list: The config_list of this QueryPriceForSubscriptionRequest.  # noqa: E501
        :type: list[ConfigListForQueryPriceForSubscriptionInput]
        """

        self._config_list = config_list

    @property
    def product(self):
        """Gets the product of this QueryPriceForSubscriptionRequest.  # noqa: E501


        :return: The product of this QueryPriceForSubscriptionRequest.  # noqa: E501
        :rtype: str
        """
        return self._product

    @product.setter
    def product(self, product):
        """Sets the product of this QueryPriceForSubscriptionRequest.


        :param product: The product of this QueryPriceForSubscriptionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and product is None:
            raise ValueError("Invalid value for `product`, must not be `None`")  # noqa: E501

        self._product = product

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryPriceForSubscriptionRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryPriceForSubscriptionRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryPriceForSubscriptionRequest):
            return True

        return self.to_dict() != other.to_dict()
