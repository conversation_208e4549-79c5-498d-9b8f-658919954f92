# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AuthInfoForListFinancialRelationOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auth_id': 'str',
        'auth_list': 'list[int]',
        'auth_status': 'int'
    }

    attribute_map = {
        'auth_id': 'AuthID',
        'auth_list': 'AuthList',
        'auth_status': 'AuthStatus'
    }

    def __init__(self, auth_id=None, auth_list=None, auth_status=None, _configuration=None):  # noqa: E501
        """AuthInfoForListFinancialRelationOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auth_id = None
        self._auth_list = None
        self._auth_status = None
        self.discriminator = None

        if auth_id is not None:
            self.auth_id = auth_id
        if auth_list is not None:
            self.auth_list = auth_list
        if auth_status is not None:
            self.auth_status = auth_status

    @property
    def auth_id(self):
        """Gets the auth_id of this AuthInfoForListFinancialRelationOutput.  # noqa: E501


        :return: The auth_id of this AuthInfoForListFinancialRelationOutput.  # noqa: E501
        :rtype: str
        """
        return self._auth_id

    @auth_id.setter
    def auth_id(self, auth_id):
        """Sets the auth_id of this AuthInfoForListFinancialRelationOutput.


        :param auth_id: The auth_id of this AuthInfoForListFinancialRelationOutput.  # noqa: E501
        :type: str
        """

        self._auth_id = auth_id

    @property
    def auth_list(self):
        """Gets the auth_list of this AuthInfoForListFinancialRelationOutput.  # noqa: E501


        :return: The auth_list of this AuthInfoForListFinancialRelationOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._auth_list

    @auth_list.setter
    def auth_list(self, auth_list):
        """Sets the auth_list of this AuthInfoForListFinancialRelationOutput.


        :param auth_list: The auth_list of this AuthInfoForListFinancialRelationOutput.  # noqa: E501
        :type: list[int]
        """

        self._auth_list = auth_list

    @property
    def auth_status(self):
        """Gets the auth_status of this AuthInfoForListFinancialRelationOutput.  # noqa: E501


        :return: The auth_status of this AuthInfoForListFinancialRelationOutput.  # noqa: E501
        :rtype: int
        """
        return self._auth_status

    @auth_status.setter
    def auth_status(self, auth_status):
        """Sets the auth_status of this AuthInfoForListFinancialRelationOutput.


        :param auth_status: The auth_status of this AuthInfoForListFinancialRelationOutput.  # noqa: E501
        :type: int
        """

        self._auth_status = auth_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AuthInfoForListFinancialRelationOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AuthInfoForListFinancialRelationOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AuthInfoForListFinancialRelationOutput):
            return True

        return self.to_dict() != other.to_dict()
