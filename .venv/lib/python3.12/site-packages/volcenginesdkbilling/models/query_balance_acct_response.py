# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryBalanceAcctResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'int',
        'arrears_balance': 'str',
        'available_balance': 'str',
        'cash_balance': 'str',
        'credit_limit': 'str',
        'freeze_amount': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'arrears_balance': 'ArrearsBalance',
        'available_balance': 'AvailableBalance',
        'cash_balance': 'CashBalance',
        'credit_limit': 'CreditLimit',
        'freeze_amount': 'FreezeAmount'
    }

    def __init__(self, account_id=None, arrears_balance=None, available_balance=None, cash_balance=None, credit_limit=None, freeze_amount=None, _configuration=None):  # noqa: E501
        """QueryBalanceAcctResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._arrears_balance = None
        self._available_balance = None
        self._cash_balance = None
        self._credit_limit = None
        self._freeze_amount = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if arrears_balance is not None:
            self.arrears_balance = arrears_balance
        if available_balance is not None:
            self.available_balance = available_balance
        if cash_balance is not None:
            self.cash_balance = cash_balance
        if credit_limit is not None:
            self.credit_limit = credit_limit
        if freeze_amount is not None:
            self.freeze_amount = freeze_amount

    @property
    def account_id(self):
        """Gets the account_id of this QueryBalanceAcctResponse.  # noqa: E501


        :return: The account_id of this QueryBalanceAcctResponse.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this QueryBalanceAcctResponse.


        :param account_id: The account_id of this QueryBalanceAcctResponse.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def arrears_balance(self):
        """Gets the arrears_balance of this QueryBalanceAcctResponse.  # noqa: E501


        :return: The arrears_balance of this QueryBalanceAcctResponse.  # noqa: E501
        :rtype: str
        """
        return self._arrears_balance

    @arrears_balance.setter
    def arrears_balance(self, arrears_balance):
        """Sets the arrears_balance of this QueryBalanceAcctResponse.


        :param arrears_balance: The arrears_balance of this QueryBalanceAcctResponse.  # noqa: E501
        :type: str
        """

        self._arrears_balance = arrears_balance

    @property
    def available_balance(self):
        """Gets the available_balance of this QueryBalanceAcctResponse.  # noqa: E501


        :return: The available_balance of this QueryBalanceAcctResponse.  # noqa: E501
        :rtype: str
        """
        return self._available_balance

    @available_balance.setter
    def available_balance(self, available_balance):
        """Sets the available_balance of this QueryBalanceAcctResponse.


        :param available_balance: The available_balance of this QueryBalanceAcctResponse.  # noqa: E501
        :type: str
        """

        self._available_balance = available_balance

    @property
    def cash_balance(self):
        """Gets the cash_balance of this QueryBalanceAcctResponse.  # noqa: E501


        :return: The cash_balance of this QueryBalanceAcctResponse.  # noqa: E501
        :rtype: str
        """
        return self._cash_balance

    @cash_balance.setter
    def cash_balance(self, cash_balance):
        """Sets the cash_balance of this QueryBalanceAcctResponse.


        :param cash_balance: The cash_balance of this QueryBalanceAcctResponse.  # noqa: E501
        :type: str
        """

        self._cash_balance = cash_balance

    @property
    def credit_limit(self):
        """Gets the credit_limit of this QueryBalanceAcctResponse.  # noqa: E501


        :return: The credit_limit of this QueryBalanceAcctResponse.  # noqa: E501
        :rtype: str
        """
        return self._credit_limit

    @credit_limit.setter
    def credit_limit(self, credit_limit):
        """Sets the credit_limit of this QueryBalanceAcctResponse.


        :param credit_limit: The credit_limit of this QueryBalanceAcctResponse.  # noqa: E501
        :type: str
        """

        self._credit_limit = credit_limit

    @property
    def freeze_amount(self):
        """Gets the freeze_amount of this QueryBalanceAcctResponse.  # noqa: E501


        :return: The freeze_amount of this QueryBalanceAcctResponse.  # noqa: E501
        :rtype: str
        """
        return self._freeze_amount

    @freeze_amount.setter
    def freeze_amount(self, freeze_amount):
        """Sets the freeze_amount of this QueryBalanceAcctResponse.


        :param freeze_amount: The freeze_amount of this QueryBalanceAcctResponse.  # noqa: E501
        :type: str
        """

        self._freeze_amount = freeze_amount

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryBalanceAcctResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryBalanceAcctResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryBalanceAcctResponse):
            return True

        return self.to_dict() != other.to_dict()
