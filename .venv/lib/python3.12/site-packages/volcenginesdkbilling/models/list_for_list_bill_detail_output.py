# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListForListBillDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bill_category': 'str',
        'bill_detail_id': 'str',
        'bill_id': 'str',
        'bill_period': 'str',
        'billing_function': 'str',
        'billing_method_code': 'str',
        'billing_mode': 'str',
        'busi_period': 'str',
        'business_mode': 'str',
        'config_name': 'str',
        'configuration_code': 'str',
        'count': 'str',
        'country_region': 'str',
        'coupon_amount': 'str',
        'credit_carried_amount': 'str',
        'currency': 'str',
        'deduction_count': 'str',
        'deduction_use_duration': 'str',
        'discount_bill_amount': 'str',
        'discount_biz_billing_function': 'str',
        'discount_biz_measure_interval': 'str',
        'discount_biz_unit_price': 'str',
        'discount_biz_unit_price_interval': 'str',
        'effective_factor': 'str',
        'element': 'str',
        'element_code': 'str',
        'expand_field': 'str',
        'expense_begin_time': 'str',
        'expense_date': 'str',
        'expense_end_time': 'str',
        'factor': 'str',
        'factor_code': 'str',
        'formula': 'str',
        'instance_name': 'str',
        'instance_no': 'str',
        'market_price': 'str',
        'measure_interval': 'str',
        'original_bill_amount': 'str',
        'owner_customer_name': 'str',
        'owner_id': 'str',
        'owner_user_name': 'str',
        'paid_amount': 'str',
        'payable_amount': 'str',
        'payer_customer_name': 'str',
        'payer_id': 'str',
        'payer_user_name': 'str',
        'posttax_amount': 'str',
        'preferential_bill_amount': 'str',
        'pretax_amount': 'str',
        'pretax_real_value': 'str',
        'price': 'str',
        'price_interval': 'str',
        'price_unit': 'str',
        'product': 'str',
        'product_zh': 'str',
        'project': 'str',
        'project_display_name': 'str',
        'real_value': 'str',
        'region': 'str',
        'region_code': 'str',
        'reservation_instance': 'str',
        'round_amount': 'float',
        'seller_customer_name': 'str',
        'seller_id': 'str',
        'seller_user_name': 'str',
        'selling_mode': 'str',
        'settle_posttax_amount': 'str',
        'settle_pretax_amount': 'str',
        'settle_pretax_real_value': 'str',
        'settle_real_value': 'str',
        'settle_tax': 'str',
        'settlement_type': 'str',
        'solution_zh': 'str',
        'subject_name': 'str',
        'tag': 'str',
        'tax': 'str',
        'tax_rate': 'str',
        'trade_time': 'str',
        'unit': 'str',
        'unpaid_amount': 'str',
        'use_duration': 'str',
        'use_duration_unit': 'str',
        'zone': 'str',
        'zone_code': 'str'
    }

    attribute_map = {
        'bill_category': 'BillCategory',
        'bill_detail_id': 'BillDetailId',
        'bill_id': 'BillID',
        'bill_period': 'BillPeriod',
        'billing_function': 'BillingFunction',
        'billing_method_code': 'BillingMethodCode',
        'billing_mode': 'BillingMode',
        'busi_period': 'BusiPeriod',
        'business_mode': 'BusinessMode',
        'config_name': 'ConfigName',
        'configuration_code': 'ConfigurationCode',
        'count': 'Count',
        'country_region': 'CountryRegion',
        'coupon_amount': 'CouponAmount',
        'credit_carried_amount': 'CreditCarriedAmount',
        'currency': 'Currency',
        'deduction_count': 'DeductionCount',
        'deduction_use_duration': 'DeductionUseDuration',
        'discount_bill_amount': 'DiscountBillAmount',
        'discount_biz_billing_function': 'DiscountBizBillingFunction',
        'discount_biz_measure_interval': 'DiscountBizMeasureInterval',
        'discount_biz_unit_price': 'DiscountBizUnitPrice',
        'discount_biz_unit_price_interval': 'DiscountBizUnitPriceInterval',
        'effective_factor': 'EffectiveFactor',
        'element': 'Element',
        'element_code': 'ElementCode',
        'expand_field': 'ExpandField',
        'expense_begin_time': 'ExpenseBeginTime',
        'expense_date': 'ExpenseDate',
        'expense_end_time': 'ExpenseEndTime',
        'factor': 'Factor',
        'factor_code': 'FactorCode',
        'formula': 'Formula',
        'instance_name': 'InstanceName',
        'instance_no': 'InstanceNo',
        'market_price': 'MarketPrice',
        'measure_interval': 'MeasureInterval',
        'original_bill_amount': 'OriginalBillAmount',
        'owner_customer_name': 'OwnerCustomerName',
        'owner_id': 'OwnerID',
        'owner_user_name': 'OwnerUserName',
        'paid_amount': 'PaidAmount',
        'payable_amount': 'PayableAmount',
        'payer_customer_name': 'PayerCustomerName',
        'payer_id': 'PayerID',
        'payer_user_name': 'PayerUserName',
        'posttax_amount': 'PosttaxAmount',
        'preferential_bill_amount': 'PreferentialBillAmount',
        'pretax_amount': 'PretaxAmount',
        'pretax_real_value': 'PretaxRealValue',
        'price': 'Price',
        'price_interval': 'PriceInterval',
        'price_unit': 'PriceUnit',
        'product': 'Product',
        'product_zh': 'ProductZh',
        'project': 'Project',
        'project_display_name': 'ProjectDisplayName',
        'real_value': 'RealValue',
        'region': 'Region',
        'region_code': 'RegionCode',
        'reservation_instance': 'ReservationInstance',
        'round_amount': 'RoundAmount',
        'seller_customer_name': 'SellerCustomerName',
        'seller_id': 'SellerID',
        'seller_user_name': 'SellerUserName',
        'selling_mode': 'SellingMode',
        'settle_posttax_amount': 'SettlePosttaxAmount',
        'settle_pretax_amount': 'SettlePretaxAmount',
        'settle_pretax_real_value': 'SettlePretaxRealValue',
        'settle_real_value': 'SettleRealValue',
        'settle_tax': 'SettleTax',
        'settlement_type': 'SettlementType',
        'solution_zh': 'SolutionZh',
        'subject_name': 'SubjectName',
        'tag': 'Tag',
        'tax': 'Tax',
        'tax_rate': 'TaxRate',
        'trade_time': 'TradeTime',
        'unit': 'Unit',
        'unpaid_amount': 'UnpaidAmount',
        'use_duration': 'UseDuration',
        'use_duration_unit': 'UseDurationUnit',
        'zone': 'Zone',
        'zone_code': 'ZoneCode'
    }

    def __init__(self, bill_category=None, bill_detail_id=None, bill_id=None, bill_period=None, billing_function=None, billing_method_code=None, billing_mode=None, busi_period=None, business_mode=None, config_name=None, configuration_code=None, count=None, country_region=None, coupon_amount=None, credit_carried_amount=None, currency=None, deduction_count=None, deduction_use_duration=None, discount_bill_amount=None, discount_biz_billing_function=None, discount_biz_measure_interval=None, discount_biz_unit_price=None, discount_biz_unit_price_interval=None, effective_factor=None, element=None, element_code=None, expand_field=None, expense_begin_time=None, expense_date=None, expense_end_time=None, factor=None, factor_code=None, formula=None, instance_name=None, instance_no=None, market_price=None, measure_interval=None, original_bill_amount=None, owner_customer_name=None, owner_id=None, owner_user_name=None, paid_amount=None, payable_amount=None, payer_customer_name=None, payer_id=None, payer_user_name=None, posttax_amount=None, preferential_bill_amount=None, pretax_amount=None, pretax_real_value=None, price=None, price_interval=None, price_unit=None, product=None, product_zh=None, project=None, project_display_name=None, real_value=None, region=None, region_code=None, reservation_instance=None, round_amount=None, seller_customer_name=None, seller_id=None, seller_user_name=None, selling_mode=None, settle_posttax_amount=None, settle_pretax_amount=None, settle_pretax_real_value=None, settle_real_value=None, settle_tax=None, settlement_type=None, solution_zh=None, subject_name=None, tag=None, tax=None, tax_rate=None, trade_time=None, unit=None, unpaid_amount=None, use_duration=None, use_duration_unit=None, zone=None, zone_code=None, _configuration=None):  # noqa: E501
        """ListForListBillDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bill_category = None
        self._bill_detail_id = None
        self._bill_id = None
        self._bill_period = None
        self._billing_function = None
        self._billing_method_code = None
        self._billing_mode = None
        self._busi_period = None
        self._business_mode = None
        self._config_name = None
        self._configuration_code = None
        self._count = None
        self._country_region = None
        self._coupon_amount = None
        self._credit_carried_amount = None
        self._currency = None
        self._deduction_count = None
        self._deduction_use_duration = None
        self._discount_bill_amount = None
        self._discount_biz_billing_function = None
        self._discount_biz_measure_interval = None
        self._discount_biz_unit_price = None
        self._discount_biz_unit_price_interval = None
        self._effective_factor = None
        self._element = None
        self._element_code = None
        self._expand_field = None
        self._expense_begin_time = None
        self._expense_date = None
        self._expense_end_time = None
        self._factor = None
        self._factor_code = None
        self._formula = None
        self._instance_name = None
        self._instance_no = None
        self._market_price = None
        self._measure_interval = None
        self._original_bill_amount = None
        self._owner_customer_name = None
        self._owner_id = None
        self._owner_user_name = None
        self._paid_amount = None
        self._payable_amount = None
        self._payer_customer_name = None
        self._payer_id = None
        self._payer_user_name = None
        self._posttax_amount = None
        self._preferential_bill_amount = None
        self._pretax_amount = None
        self._pretax_real_value = None
        self._price = None
        self._price_interval = None
        self._price_unit = None
        self._product = None
        self._product_zh = None
        self._project = None
        self._project_display_name = None
        self._real_value = None
        self._region = None
        self._region_code = None
        self._reservation_instance = None
        self._round_amount = None
        self._seller_customer_name = None
        self._seller_id = None
        self._seller_user_name = None
        self._selling_mode = None
        self._settle_posttax_amount = None
        self._settle_pretax_amount = None
        self._settle_pretax_real_value = None
        self._settle_real_value = None
        self._settle_tax = None
        self._settlement_type = None
        self._solution_zh = None
        self._subject_name = None
        self._tag = None
        self._tax = None
        self._tax_rate = None
        self._trade_time = None
        self._unit = None
        self._unpaid_amount = None
        self._use_duration = None
        self._use_duration_unit = None
        self._zone = None
        self._zone_code = None
        self.discriminator = None

        if bill_category is not None:
            self.bill_category = bill_category
        if bill_detail_id is not None:
            self.bill_detail_id = bill_detail_id
        if bill_id is not None:
            self.bill_id = bill_id
        if bill_period is not None:
            self.bill_period = bill_period
        if billing_function is not None:
            self.billing_function = billing_function
        if billing_method_code is not None:
            self.billing_method_code = billing_method_code
        if billing_mode is not None:
            self.billing_mode = billing_mode
        if busi_period is not None:
            self.busi_period = busi_period
        if business_mode is not None:
            self.business_mode = business_mode
        if config_name is not None:
            self.config_name = config_name
        if configuration_code is not None:
            self.configuration_code = configuration_code
        if count is not None:
            self.count = count
        if country_region is not None:
            self.country_region = country_region
        if coupon_amount is not None:
            self.coupon_amount = coupon_amount
        if credit_carried_amount is not None:
            self.credit_carried_amount = credit_carried_amount
        if currency is not None:
            self.currency = currency
        if deduction_count is not None:
            self.deduction_count = deduction_count
        if deduction_use_duration is not None:
            self.deduction_use_duration = deduction_use_duration
        if discount_bill_amount is not None:
            self.discount_bill_amount = discount_bill_amount
        if discount_biz_billing_function is not None:
            self.discount_biz_billing_function = discount_biz_billing_function
        if discount_biz_measure_interval is not None:
            self.discount_biz_measure_interval = discount_biz_measure_interval
        if discount_biz_unit_price is not None:
            self.discount_biz_unit_price = discount_biz_unit_price
        if discount_biz_unit_price_interval is not None:
            self.discount_biz_unit_price_interval = discount_biz_unit_price_interval
        if effective_factor is not None:
            self.effective_factor = effective_factor
        if element is not None:
            self.element = element
        if element_code is not None:
            self.element_code = element_code
        if expand_field is not None:
            self.expand_field = expand_field
        if expense_begin_time is not None:
            self.expense_begin_time = expense_begin_time
        if expense_date is not None:
            self.expense_date = expense_date
        if expense_end_time is not None:
            self.expense_end_time = expense_end_time
        if factor is not None:
            self.factor = factor
        if factor_code is not None:
            self.factor_code = factor_code
        if formula is not None:
            self.formula = formula
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_no is not None:
            self.instance_no = instance_no
        if market_price is not None:
            self.market_price = market_price
        if measure_interval is not None:
            self.measure_interval = measure_interval
        if original_bill_amount is not None:
            self.original_bill_amount = original_bill_amount
        if owner_customer_name is not None:
            self.owner_customer_name = owner_customer_name
        if owner_id is not None:
            self.owner_id = owner_id
        if owner_user_name is not None:
            self.owner_user_name = owner_user_name
        if paid_amount is not None:
            self.paid_amount = paid_amount
        if payable_amount is not None:
            self.payable_amount = payable_amount
        if payer_customer_name is not None:
            self.payer_customer_name = payer_customer_name
        if payer_id is not None:
            self.payer_id = payer_id
        if payer_user_name is not None:
            self.payer_user_name = payer_user_name
        if posttax_amount is not None:
            self.posttax_amount = posttax_amount
        if preferential_bill_amount is not None:
            self.preferential_bill_amount = preferential_bill_amount
        if pretax_amount is not None:
            self.pretax_amount = pretax_amount
        if pretax_real_value is not None:
            self.pretax_real_value = pretax_real_value
        if price is not None:
            self.price = price
        if price_interval is not None:
            self.price_interval = price_interval
        if price_unit is not None:
            self.price_unit = price_unit
        if product is not None:
            self.product = product
        if product_zh is not None:
            self.product_zh = product_zh
        if project is not None:
            self.project = project
        if project_display_name is not None:
            self.project_display_name = project_display_name
        if real_value is not None:
            self.real_value = real_value
        if region is not None:
            self.region = region
        if region_code is not None:
            self.region_code = region_code
        if reservation_instance is not None:
            self.reservation_instance = reservation_instance
        if round_amount is not None:
            self.round_amount = round_amount
        if seller_customer_name is not None:
            self.seller_customer_name = seller_customer_name
        if seller_id is not None:
            self.seller_id = seller_id
        if seller_user_name is not None:
            self.seller_user_name = seller_user_name
        if selling_mode is not None:
            self.selling_mode = selling_mode
        if settle_posttax_amount is not None:
            self.settle_posttax_amount = settle_posttax_amount
        if settle_pretax_amount is not None:
            self.settle_pretax_amount = settle_pretax_amount
        if settle_pretax_real_value is not None:
            self.settle_pretax_real_value = settle_pretax_real_value
        if settle_real_value is not None:
            self.settle_real_value = settle_real_value
        if settle_tax is not None:
            self.settle_tax = settle_tax
        if settlement_type is not None:
            self.settlement_type = settlement_type
        if solution_zh is not None:
            self.solution_zh = solution_zh
        if subject_name is not None:
            self.subject_name = subject_name
        if tag is not None:
            self.tag = tag
        if tax is not None:
            self.tax = tax
        if tax_rate is not None:
            self.tax_rate = tax_rate
        if trade_time is not None:
            self.trade_time = trade_time
        if unit is not None:
            self.unit = unit
        if unpaid_amount is not None:
            self.unpaid_amount = unpaid_amount
        if use_duration is not None:
            self.use_duration = use_duration
        if use_duration_unit is not None:
            self.use_duration_unit = use_duration_unit
        if zone is not None:
            self.zone = zone
        if zone_code is not None:
            self.zone_code = zone_code

    @property
    def bill_category(self):
        """Gets the bill_category of this ListForListBillDetailOutput.  # noqa: E501


        :return: The bill_category of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_category

    @bill_category.setter
    def bill_category(self, bill_category):
        """Sets the bill_category of this ListForListBillDetailOutput.


        :param bill_category: The bill_category of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._bill_category = bill_category

    @property
    def bill_detail_id(self):
        """Gets the bill_detail_id of this ListForListBillDetailOutput.  # noqa: E501


        :return: The bill_detail_id of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_detail_id

    @bill_detail_id.setter
    def bill_detail_id(self, bill_detail_id):
        """Sets the bill_detail_id of this ListForListBillDetailOutput.


        :param bill_detail_id: The bill_detail_id of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._bill_detail_id = bill_detail_id

    @property
    def bill_id(self):
        """Gets the bill_id of this ListForListBillDetailOutput.  # noqa: E501


        :return: The bill_id of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_id

    @bill_id.setter
    def bill_id(self, bill_id):
        """Sets the bill_id of this ListForListBillDetailOutput.


        :param bill_id: The bill_id of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._bill_id = bill_id

    @property
    def bill_period(self):
        """Gets the bill_period of this ListForListBillDetailOutput.  # noqa: E501


        :return: The bill_period of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_period

    @bill_period.setter
    def bill_period(self, bill_period):
        """Sets the bill_period of this ListForListBillDetailOutput.


        :param bill_period: The bill_period of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._bill_period = bill_period

    @property
    def billing_function(self):
        """Gets the billing_function of this ListForListBillDetailOutput.  # noqa: E501


        :return: The billing_function of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._billing_function

    @billing_function.setter
    def billing_function(self, billing_function):
        """Sets the billing_function of this ListForListBillDetailOutput.


        :param billing_function: The billing_function of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._billing_function = billing_function

    @property
    def billing_method_code(self):
        """Gets the billing_method_code of this ListForListBillDetailOutput.  # noqa: E501


        :return: The billing_method_code of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._billing_method_code

    @billing_method_code.setter
    def billing_method_code(self, billing_method_code):
        """Sets the billing_method_code of this ListForListBillDetailOutput.


        :param billing_method_code: The billing_method_code of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._billing_method_code = billing_method_code

    @property
    def billing_mode(self):
        """Gets the billing_mode of this ListForListBillDetailOutput.  # noqa: E501


        :return: The billing_mode of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._billing_mode

    @billing_mode.setter
    def billing_mode(self, billing_mode):
        """Sets the billing_mode of this ListForListBillDetailOutput.


        :param billing_mode: The billing_mode of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._billing_mode = billing_mode

    @property
    def busi_period(self):
        """Gets the busi_period of this ListForListBillDetailOutput.  # noqa: E501


        :return: The busi_period of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._busi_period

    @busi_period.setter
    def busi_period(self, busi_period):
        """Sets the busi_period of this ListForListBillDetailOutput.


        :param busi_period: The busi_period of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._busi_period = busi_period

    @property
    def business_mode(self):
        """Gets the business_mode of this ListForListBillDetailOutput.  # noqa: E501


        :return: The business_mode of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._business_mode

    @business_mode.setter
    def business_mode(self, business_mode):
        """Sets the business_mode of this ListForListBillDetailOutput.


        :param business_mode: The business_mode of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._business_mode = business_mode

    @property
    def config_name(self):
        """Gets the config_name of this ListForListBillDetailOutput.  # noqa: E501


        :return: The config_name of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._config_name

    @config_name.setter
    def config_name(self, config_name):
        """Sets the config_name of this ListForListBillDetailOutput.


        :param config_name: The config_name of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._config_name = config_name

    @property
    def configuration_code(self):
        """Gets the configuration_code of this ListForListBillDetailOutput.  # noqa: E501


        :return: The configuration_code of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._configuration_code

    @configuration_code.setter
    def configuration_code(self, configuration_code):
        """Sets the configuration_code of this ListForListBillDetailOutput.


        :param configuration_code: The configuration_code of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._configuration_code = configuration_code

    @property
    def count(self):
        """Gets the count of this ListForListBillDetailOutput.  # noqa: E501


        :return: The count of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._count

    @count.setter
    def count(self, count):
        """Sets the count of this ListForListBillDetailOutput.


        :param count: The count of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._count = count

    @property
    def country_region(self):
        """Gets the country_region of this ListForListBillDetailOutput.  # noqa: E501


        :return: The country_region of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._country_region

    @country_region.setter
    def country_region(self, country_region):
        """Sets the country_region of this ListForListBillDetailOutput.


        :param country_region: The country_region of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._country_region = country_region

    @property
    def coupon_amount(self):
        """Gets the coupon_amount of this ListForListBillDetailOutput.  # noqa: E501


        :return: The coupon_amount of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._coupon_amount

    @coupon_amount.setter
    def coupon_amount(self, coupon_amount):
        """Sets the coupon_amount of this ListForListBillDetailOutput.


        :param coupon_amount: The coupon_amount of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._coupon_amount = coupon_amount

    @property
    def credit_carried_amount(self):
        """Gets the credit_carried_amount of this ListForListBillDetailOutput.  # noqa: E501


        :return: The credit_carried_amount of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._credit_carried_amount

    @credit_carried_amount.setter
    def credit_carried_amount(self, credit_carried_amount):
        """Sets the credit_carried_amount of this ListForListBillDetailOutput.


        :param credit_carried_amount: The credit_carried_amount of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._credit_carried_amount = credit_carried_amount

    @property
    def currency(self):
        """Gets the currency of this ListForListBillDetailOutput.  # noqa: E501


        :return: The currency of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this ListForListBillDetailOutput.


        :param currency: The currency of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._currency = currency

    @property
    def deduction_count(self):
        """Gets the deduction_count of this ListForListBillDetailOutput.  # noqa: E501


        :return: The deduction_count of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_count

    @deduction_count.setter
    def deduction_count(self, deduction_count):
        """Sets the deduction_count of this ListForListBillDetailOutput.


        :param deduction_count: The deduction_count of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._deduction_count = deduction_count

    @property
    def deduction_use_duration(self):
        """Gets the deduction_use_duration of this ListForListBillDetailOutput.  # noqa: E501


        :return: The deduction_use_duration of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_use_duration

    @deduction_use_duration.setter
    def deduction_use_duration(self, deduction_use_duration):
        """Sets the deduction_use_duration of this ListForListBillDetailOutput.


        :param deduction_use_duration: The deduction_use_duration of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._deduction_use_duration = deduction_use_duration

    @property
    def discount_bill_amount(self):
        """Gets the discount_bill_amount of this ListForListBillDetailOutput.  # noqa: E501


        :return: The discount_bill_amount of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_bill_amount

    @discount_bill_amount.setter
    def discount_bill_amount(self, discount_bill_amount):
        """Sets the discount_bill_amount of this ListForListBillDetailOutput.


        :param discount_bill_amount: The discount_bill_amount of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._discount_bill_amount = discount_bill_amount

    @property
    def discount_biz_billing_function(self):
        """Gets the discount_biz_billing_function of this ListForListBillDetailOutput.  # noqa: E501


        :return: The discount_biz_billing_function of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_biz_billing_function

    @discount_biz_billing_function.setter
    def discount_biz_billing_function(self, discount_biz_billing_function):
        """Sets the discount_biz_billing_function of this ListForListBillDetailOutput.


        :param discount_biz_billing_function: The discount_biz_billing_function of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._discount_biz_billing_function = discount_biz_billing_function

    @property
    def discount_biz_measure_interval(self):
        """Gets the discount_biz_measure_interval of this ListForListBillDetailOutput.  # noqa: E501


        :return: The discount_biz_measure_interval of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_biz_measure_interval

    @discount_biz_measure_interval.setter
    def discount_biz_measure_interval(self, discount_biz_measure_interval):
        """Sets the discount_biz_measure_interval of this ListForListBillDetailOutput.


        :param discount_biz_measure_interval: The discount_biz_measure_interval of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._discount_biz_measure_interval = discount_biz_measure_interval

    @property
    def discount_biz_unit_price(self):
        """Gets the discount_biz_unit_price of this ListForListBillDetailOutput.  # noqa: E501


        :return: The discount_biz_unit_price of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_biz_unit_price

    @discount_biz_unit_price.setter
    def discount_biz_unit_price(self, discount_biz_unit_price):
        """Sets the discount_biz_unit_price of this ListForListBillDetailOutput.


        :param discount_biz_unit_price: The discount_biz_unit_price of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._discount_biz_unit_price = discount_biz_unit_price

    @property
    def discount_biz_unit_price_interval(self):
        """Gets the discount_biz_unit_price_interval of this ListForListBillDetailOutput.  # noqa: E501


        :return: The discount_biz_unit_price_interval of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_biz_unit_price_interval

    @discount_biz_unit_price_interval.setter
    def discount_biz_unit_price_interval(self, discount_biz_unit_price_interval):
        """Sets the discount_biz_unit_price_interval of this ListForListBillDetailOutput.


        :param discount_biz_unit_price_interval: The discount_biz_unit_price_interval of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._discount_biz_unit_price_interval = discount_biz_unit_price_interval

    @property
    def effective_factor(self):
        """Gets the effective_factor of this ListForListBillDetailOutput.  # noqa: E501


        :return: The effective_factor of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._effective_factor

    @effective_factor.setter
    def effective_factor(self, effective_factor):
        """Sets the effective_factor of this ListForListBillDetailOutput.


        :param effective_factor: The effective_factor of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._effective_factor = effective_factor

    @property
    def element(self):
        """Gets the element of this ListForListBillDetailOutput.  # noqa: E501


        :return: The element of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._element

    @element.setter
    def element(self, element):
        """Sets the element of this ListForListBillDetailOutput.


        :param element: The element of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._element = element

    @property
    def element_code(self):
        """Gets the element_code of this ListForListBillDetailOutput.  # noqa: E501


        :return: The element_code of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._element_code

    @element_code.setter
    def element_code(self, element_code):
        """Sets the element_code of this ListForListBillDetailOutput.


        :param element_code: The element_code of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._element_code = element_code

    @property
    def expand_field(self):
        """Gets the expand_field of this ListForListBillDetailOutput.  # noqa: E501


        :return: The expand_field of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._expand_field

    @expand_field.setter
    def expand_field(self, expand_field):
        """Sets the expand_field of this ListForListBillDetailOutput.


        :param expand_field: The expand_field of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._expand_field = expand_field

    @property
    def expense_begin_time(self):
        """Gets the expense_begin_time of this ListForListBillDetailOutput.  # noqa: E501


        :return: The expense_begin_time of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._expense_begin_time

    @expense_begin_time.setter
    def expense_begin_time(self, expense_begin_time):
        """Sets the expense_begin_time of this ListForListBillDetailOutput.


        :param expense_begin_time: The expense_begin_time of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._expense_begin_time = expense_begin_time

    @property
    def expense_date(self):
        """Gets the expense_date of this ListForListBillDetailOutput.  # noqa: E501


        :return: The expense_date of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._expense_date

    @expense_date.setter
    def expense_date(self, expense_date):
        """Sets the expense_date of this ListForListBillDetailOutput.


        :param expense_date: The expense_date of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._expense_date = expense_date

    @property
    def expense_end_time(self):
        """Gets the expense_end_time of this ListForListBillDetailOutput.  # noqa: E501


        :return: The expense_end_time of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._expense_end_time

    @expense_end_time.setter
    def expense_end_time(self, expense_end_time):
        """Sets the expense_end_time of this ListForListBillDetailOutput.


        :param expense_end_time: The expense_end_time of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._expense_end_time = expense_end_time

    @property
    def factor(self):
        """Gets the factor of this ListForListBillDetailOutput.  # noqa: E501


        :return: The factor of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._factor

    @factor.setter
    def factor(self, factor):
        """Sets the factor of this ListForListBillDetailOutput.


        :param factor: The factor of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._factor = factor

    @property
    def factor_code(self):
        """Gets the factor_code of this ListForListBillDetailOutput.  # noqa: E501


        :return: The factor_code of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._factor_code

    @factor_code.setter
    def factor_code(self, factor_code):
        """Sets the factor_code of this ListForListBillDetailOutput.


        :param factor_code: The factor_code of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._factor_code = factor_code

    @property
    def formula(self):
        """Gets the formula of this ListForListBillDetailOutput.  # noqa: E501


        :return: The formula of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._formula

    @formula.setter
    def formula(self, formula):
        """Sets the formula of this ListForListBillDetailOutput.


        :param formula: The formula of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._formula = formula

    @property
    def instance_name(self):
        """Gets the instance_name of this ListForListBillDetailOutput.  # noqa: E501


        :return: The instance_name of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this ListForListBillDetailOutput.


        :param instance_name: The instance_name of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_no(self):
        """Gets the instance_no of this ListForListBillDetailOutput.  # noqa: E501


        :return: The instance_no of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_no

    @instance_no.setter
    def instance_no(self, instance_no):
        """Sets the instance_no of this ListForListBillDetailOutput.


        :param instance_no: The instance_no of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_no = instance_no

    @property
    def market_price(self):
        """Gets the market_price of this ListForListBillDetailOutput.  # noqa: E501


        :return: The market_price of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._market_price

    @market_price.setter
    def market_price(self, market_price):
        """Sets the market_price of this ListForListBillDetailOutput.


        :param market_price: The market_price of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._market_price = market_price

    @property
    def measure_interval(self):
        """Gets the measure_interval of this ListForListBillDetailOutput.  # noqa: E501


        :return: The measure_interval of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._measure_interval

    @measure_interval.setter
    def measure_interval(self, measure_interval):
        """Sets the measure_interval of this ListForListBillDetailOutput.


        :param measure_interval: The measure_interval of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._measure_interval = measure_interval

    @property
    def original_bill_amount(self):
        """Gets the original_bill_amount of this ListForListBillDetailOutput.  # noqa: E501


        :return: The original_bill_amount of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._original_bill_amount

    @original_bill_amount.setter
    def original_bill_amount(self, original_bill_amount):
        """Sets the original_bill_amount of this ListForListBillDetailOutput.


        :param original_bill_amount: The original_bill_amount of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._original_bill_amount = original_bill_amount

    @property
    def owner_customer_name(self):
        """Gets the owner_customer_name of this ListForListBillDetailOutput.  # noqa: E501


        :return: The owner_customer_name of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_customer_name

    @owner_customer_name.setter
    def owner_customer_name(self, owner_customer_name):
        """Sets the owner_customer_name of this ListForListBillDetailOutput.


        :param owner_customer_name: The owner_customer_name of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._owner_customer_name = owner_customer_name

    @property
    def owner_id(self):
        """Gets the owner_id of this ListForListBillDetailOutput.  # noqa: E501


        :return: The owner_id of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_id

    @owner_id.setter
    def owner_id(self, owner_id):
        """Sets the owner_id of this ListForListBillDetailOutput.


        :param owner_id: The owner_id of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._owner_id = owner_id

    @property
    def owner_user_name(self):
        """Gets the owner_user_name of this ListForListBillDetailOutput.  # noqa: E501


        :return: The owner_user_name of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_user_name

    @owner_user_name.setter
    def owner_user_name(self, owner_user_name):
        """Sets the owner_user_name of this ListForListBillDetailOutput.


        :param owner_user_name: The owner_user_name of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._owner_user_name = owner_user_name

    @property
    def paid_amount(self):
        """Gets the paid_amount of this ListForListBillDetailOutput.  # noqa: E501


        :return: The paid_amount of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._paid_amount

    @paid_amount.setter
    def paid_amount(self, paid_amount):
        """Sets the paid_amount of this ListForListBillDetailOutput.


        :param paid_amount: The paid_amount of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._paid_amount = paid_amount

    @property
    def payable_amount(self):
        """Gets the payable_amount of this ListForListBillDetailOutput.  # noqa: E501


        :return: The payable_amount of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._payable_amount

    @payable_amount.setter
    def payable_amount(self, payable_amount):
        """Sets the payable_amount of this ListForListBillDetailOutput.


        :param payable_amount: The payable_amount of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._payable_amount = payable_amount

    @property
    def payer_customer_name(self):
        """Gets the payer_customer_name of this ListForListBillDetailOutput.  # noqa: E501


        :return: The payer_customer_name of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_customer_name

    @payer_customer_name.setter
    def payer_customer_name(self, payer_customer_name):
        """Sets the payer_customer_name of this ListForListBillDetailOutput.


        :param payer_customer_name: The payer_customer_name of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._payer_customer_name = payer_customer_name

    @property
    def payer_id(self):
        """Gets the payer_id of this ListForListBillDetailOutput.  # noqa: E501


        :return: The payer_id of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_id

    @payer_id.setter
    def payer_id(self, payer_id):
        """Sets the payer_id of this ListForListBillDetailOutput.


        :param payer_id: The payer_id of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._payer_id = payer_id

    @property
    def payer_user_name(self):
        """Gets the payer_user_name of this ListForListBillDetailOutput.  # noqa: E501


        :return: The payer_user_name of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_user_name

    @payer_user_name.setter
    def payer_user_name(self, payer_user_name):
        """Sets the payer_user_name of this ListForListBillDetailOutput.


        :param payer_user_name: The payer_user_name of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._payer_user_name = payer_user_name

    @property
    def posttax_amount(self):
        """Gets the posttax_amount of this ListForListBillDetailOutput.  # noqa: E501


        :return: The posttax_amount of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._posttax_amount

    @posttax_amount.setter
    def posttax_amount(self, posttax_amount):
        """Sets the posttax_amount of this ListForListBillDetailOutput.


        :param posttax_amount: The posttax_amount of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._posttax_amount = posttax_amount

    @property
    def preferential_bill_amount(self):
        """Gets the preferential_bill_amount of this ListForListBillDetailOutput.  # noqa: E501


        :return: The preferential_bill_amount of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._preferential_bill_amount

    @preferential_bill_amount.setter
    def preferential_bill_amount(self, preferential_bill_amount):
        """Sets the preferential_bill_amount of this ListForListBillDetailOutput.


        :param preferential_bill_amount: The preferential_bill_amount of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._preferential_bill_amount = preferential_bill_amount

    @property
    def pretax_amount(self):
        """Gets the pretax_amount of this ListForListBillDetailOutput.  # noqa: E501


        :return: The pretax_amount of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._pretax_amount

    @pretax_amount.setter
    def pretax_amount(self, pretax_amount):
        """Sets the pretax_amount of this ListForListBillDetailOutput.


        :param pretax_amount: The pretax_amount of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._pretax_amount = pretax_amount

    @property
    def pretax_real_value(self):
        """Gets the pretax_real_value of this ListForListBillDetailOutput.  # noqa: E501


        :return: The pretax_real_value of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._pretax_real_value

    @pretax_real_value.setter
    def pretax_real_value(self, pretax_real_value):
        """Sets the pretax_real_value of this ListForListBillDetailOutput.


        :param pretax_real_value: The pretax_real_value of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._pretax_real_value = pretax_real_value

    @property
    def price(self):
        """Gets the price of this ListForListBillDetailOutput.  # noqa: E501


        :return: The price of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._price

    @price.setter
    def price(self, price):
        """Sets the price of this ListForListBillDetailOutput.


        :param price: The price of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._price = price

    @property
    def price_interval(self):
        """Gets the price_interval of this ListForListBillDetailOutput.  # noqa: E501


        :return: The price_interval of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._price_interval

    @price_interval.setter
    def price_interval(self, price_interval):
        """Sets the price_interval of this ListForListBillDetailOutput.


        :param price_interval: The price_interval of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._price_interval = price_interval

    @property
    def price_unit(self):
        """Gets the price_unit of this ListForListBillDetailOutput.  # noqa: E501


        :return: The price_unit of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._price_unit

    @price_unit.setter
    def price_unit(self, price_unit):
        """Sets the price_unit of this ListForListBillDetailOutput.


        :param price_unit: The price_unit of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._price_unit = price_unit

    @property
    def product(self):
        """Gets the product of this ListForListBillDetailOutput.  # noqa: E501


        :return: The product of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._product

    @product.setter
    def product(self, product):
        """Sets the product of this ListForListBillDetailOutput.


        :param product: The product of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._product = product

    @property
    def product_zh(self):
        """Gets the product_zh of this ListForListBillDetailOutput.  # noqa: E501


        :return: The product_zh of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._product_zh

    @product_zh.setter
    def product_zh(self, product_zh):
        """Sets the product_zh of this ListForListBillDetailOutput.


        :param product_zh: The product_zh of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._product_zh = product_zh

    @property
    def project(self):
        """Gets the project of this ListForListBillDetailOutput.  # noqa: E501


        :return: The project of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._project

    @project.setter
    def project(self, project):
        """Sets the project of this ListForListBillDetailOutput.


        :param project: The project of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._project = project

    @property
    def project_display_name(self):
        """Gets the project_display_name of this ListForListBillDetailOutput.  # noqa: E501


        :return: The project_display_name of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_display_name

    @project_display_name.setter
    def project_display_name(self, project_display_name):
        """Sets the project_display_name of this ListForListBillDetailOutput.


        :param project_display_name: The project_display_name of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._project_display_name = project_display_name

    @property
    def real_value(self):
        """Gets the real_value of this ListForListBillDetailOutput.  # noqa: E501


        :return: The real_value of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._real_value

    @real_value.setter
    def real_value(self, real_value):
        """Sets the real_value of this ListForListBillDetailOutput.


        :param real_value: The real_value of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._real_value = real_value

    @property
    def region(self):
        """Gets the region of this ListForListBillDetailOutput.  # noqa: E501


        :return: The region of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this ListForListBillDetailOutput.


        :param region: The region of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def region_code(self):
        """Gets the region_code of this ListForListBillDetailOutput.  # noqa: E501


        :return: The region_code of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_code

    @region_code.setter
    def region_code(self, region_code):
        """Sets the region_code of this ListForListBillDetailOutput.


        :param region_code: The region_code of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._region_code = region_code

    @property
    def reservation_instance(self):
        """Gets the reservation_instance of this ListForListBillDetailOutput.  # noqa: E501


        :return: The reservation_instance of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._reservation_instance

    @reservation_instance.setter
    def reservation_instance(self, reservation_instance):
        """Sets the reservation_instance of this ListForListBillDetailOutput.


        :param reservation_instance: The reservation_instance of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._reservation_instance = reservation_instance

    @property
    def round_amount(self):
        """Gets the round_amount of this ListForListBillDetailOutput.  # noqa: E501


        :return: The round_amount of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: float
        """
        return self._round_amount

    @round_amount.setter
    def round_amount(self, round_amount):
        """Sets the round_amount of this ListForListBillDetailOutput.


        :param round_amount: The round_amount of this ListForListBillDetailOutput.  # noqa: E501
        :type: float
        """

        self._round_amount = round_amount

    @property
    def seller_customer_name(self):
        """Gets the seller_customer_name of this ListForListBillDetailOutput.  # noqa: E501


        :return: The seller_customer_name of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_customer_name

    @seller_customer_name.setter
    def seller_customer_name(self, seller_customer_name):
        """Sets the seller_customer_name of this ListForListBillDetailOutput.


        :param seller_customer_name: The seller_customer_name of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._seller_customer_name = seller_customer_name

    @property
    def seller_id(self):
        """Gets the seller_id of this ListForListBillDetailOutput.  # noqa: E501


        :return: The seller_id of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_id

    @seller_id.setter
    def seller_id(self, seller_id):
        """Sets the seller_id of this ListForListBillDetailOutput.


        :param seller_id: The seller_id of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._seller_id = seller_id

    @property
    def seller_user_name(self):
        """Gets the seller_user_name of this ListForListBillDetailOutput.  # noqa: E501


        :return: The seller_user_name of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_user_name

    @seller_user_name.setter
    def seller_user_name(self, seller_user_name):
        """Sets the seller_user_name of this ListForListBillDetailOutput.


        :param seller_user_name: The seller_user_name of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._seller_user_name = seller_user_name

    @property
    def selling_mode(self):
        """Gets the selling_mode of this ListForListBillDetailOutput.  # noqa: E501


        :return: The selling_mode of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._selling_mode

    @selling_mode.setter
    def selling_mode(self, selling_mode):
        """Sets the selling_mode of this ListForListBillDetailOutput.


        :param selling_mode: The selling_mode of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._selling_mode = selling_mode

    @property
    def settle_posttax_amount(self):
        """Gets the settle_posttax_amount of this ListForListBillDetailOutput.  # noqa: E501


        :return: The settle_posttax_amount of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_posttax_amount

    @settle_posttax_amount.setter
    def settle_posttax_amount(self, settle_posttax_amount):
        """Sets the settle_posttax_amount of this ListForListBillDetailOutput.


        :param settle_posttax_amount: The settle_posttax_amount of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._settle_posttax_amount = settle_posttax_amount

    @property
    def settle_pretax_amount(self):
        """Gets the settle_pretax_amount of this ListForListBillDetailOutput.  # noqa: E501


        :return: The settle_pretax_amount of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_pretax_amount

    @settle_pretax_amount.setter
    def settle_pretax_amount(self, settle_pretax_amount):
        """Sets the settle_pretax_amount of this ListForListBillDetailOutput.


        :param settle_pretax_amount: The settle_pretax_amount of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._settle_pretax_amount = settle_pretax_amount

    @property
    def settle_pretax_real_value(self):
        """Gets the settle_pretax_real_value of this ListForListBillDetailOutput.  # noqa: E501


        :return: The settle_pretax_real_value of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_pretax_real_value

    @settle_pretax_real_value.setter
    def settle_pretax_real_value(self, settle_pretax_real_value):
        """Sets the settle_pretax_real_value of this ListForListBillDetailOutput.


        :param settle_pretax_real_value: The settle_pretax_real_value of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._settle_pretax_real_value = settle_pretax_real_value

    @property
    def settle_real_value(self):
        """Gets the settle_real_value of this ListForListBillDetailOutput.  # noqa: E501


        :return: The settle_real_value of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_real_value

    @settle_real_value.setter
    def settle_real_value(self, settle_real_value):
        """Sets the settle_real_value of this ListForListBillDetailOutput.


        :param settle_real_value: The settle_real_value of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._settle_real_value = settle_real_value

    @property
    def settle_tax(self):
        """Gets the settle_tax of this ListForListBillDetailOutput.  # noqa: E501


        :return: The settle_tax of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_tax

    @settle_tax.setter
    def settle_tax(self, settle_tax):
        """Sets the settle_tax of this ListForListBillDetailOutput.


        :param settle_tax: The settle_tax of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._settle_tax = settle_tax

    @property
    def settlement_type(self):
        """Gets the settlement_type of this ListForListBillDetailOutput.  # noqa: E501


        :return: The settlement_type of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._settlement_type

    @settlement_type.setter
    def settlement_type(self, settlement_type):
        """Sets the settlement_type of this ListForListBillDetailOutput.


        :param settlement_type: The settlement_type of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._settlement_type = settlement_type

    @property
    def solution_zh(self):
        """Gets the solution_zh of this ListForListBillDetailOutput.  # noqa: E501


        :return: The solution_zh of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._solution_zh

    @solution_zh.setter
    def solution_zh(self, solution_zh):
        """Sets the solution_zh of this ListForListBillDetailOutput.


        :param solution_zh: The solution_zh of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._solution_zh = solution_zh

    @property
    def subject_name(self):
        """Gets the subject_name of this ListForListBillDetailOutput.  # noqa: E501


        :return: The subject_name of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._subject_name

    @subject_name.setter
    def subject_name(self, subject_name):
        """Sets the subject_name of this ListForListBillDetailOutput.


        :param subject_name: The subject_name of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._subject_name = subject_name

    @property
    def tag(self):
        """Gets the tag of this ListForListBillDetailOutput.  # noqa: E501


        :return: The tag of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this ListForListBillDetailOutput.


        :param tag: The tag of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def tax(self):
        """Gets the tax of this ListForListBillDetailOutput.  # noqa: E501


        :return: The tax of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._tax

    @tax.setter
    def tax(self, tax):
        """Sets the tax of this ListForListBillDetailOutput.


        :param tax: The tax of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._tax = tax

    @property
    def tax_rate(self):
        """Gets the tax_rate of this ListForListBillDetailOutput.  # noqa: E501


        :return: The tax_rate of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._tax_rate

    @tax_rate.setter
    def tax_rate(self, tax_rate):
        """Sets the tax_rate of this ListForListBillDetailOutput.


        :param tax_rate: The tax_rate of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._tax_rate = tax_rate

    @property
    def trade_time(self):
        """Gets the trade_time of this ListForListBillDetailOutput.  # noqa: E501


        :return: The trade_time of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._trade_time

    @trade_time.setter
    def trade_time(self, trade_time):
        """Sets the trade_time of this ListForListBillDetailOutput.


        :param trade_time: The trade_time of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._trade_time = trade_time

    @property
    def unit(self):
        """Gets the unit of this ListForListBillDetailOutput.  # noqa: E501


        :return: The unit of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._unit

    @unit.setter
    def unit(self, unit):
        """Sets the unit of this ListForListBillDetailOutput.


        :param unit: The unit of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._unit = unit

    @property
    def unpaid_amount(self):
        """Gets the unpaid_amount of this ListForListBillDetailOutput.  # noqa: E501


        :return: The unpaid_amount of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._unpaid_amount

    @unpaid_amount.setter
    def unpaid_amount(self, unpaid_amount):
        """Sets the unpaid_amount of this ListForListBillDetailOutput.


        :param unpaid_amount: The unpaid_amount of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._unpaid_amount = unpaid_amount

    @property
    def use_duration(self):
        """Gets the use_duration of this ListForListBillDetailOutput.  # noqa: E501


        :return: The use_duration of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._use_duration

    @use_duration.setter
    def use_duration(self, use_duration):
        """Sets the use_duration of this ListForListBillDetailOutput.


        :param use_duration: The use_duration of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._use_duration = use_duration

    @property
    def use_duration_unit(self):
        """Gets the use_duration_unit of this ListForListBillDetailOutput.  # noqa: E501


        :return: The use_duration_unit of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._use_duration_unit

    @use_duration_unit.setter
    def use_duration_unit(self, use_duration_unit):
        """Sets the use_duration_unit of this ListForListBillDetailOutput.


        :param use_duration_unit: The use_duration_unit of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._use_duration_unit = use_duration_unit

    @property
    def zone(self):
        """Gets the zone of this ListForListBillDetailOutput.  # noqa: E501


        :return: The zone of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone

    @zone.setter
    def zone(self, zone):
        """Sets the zone of this ListForListBillDetailOutput.


        :param zone: The zone of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._zone = zone

    @property
    def zone_code(self):
        """Gets the zone_code of this ListForListBillDetailOutput.  # noqa: E501


        :return: The zone_code of this ListForListBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_code

    @zone_code.setter
    def zone_code(self, zone_code):
        """Sets the zone_code of this ListForListBillDetailOutput.


        :param zone_code: The zone_code of this ListForListBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._zone_code = zone_code

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListForListBillDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListForListBillDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListForListBillDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
