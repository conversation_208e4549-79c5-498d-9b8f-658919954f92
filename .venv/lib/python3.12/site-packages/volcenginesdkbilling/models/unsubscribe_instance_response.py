# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UnsubscribeInstanceResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'order_id': 'str',
        'order_id_list': 'list[str]',
        'success_instance_infos': 'list[SuccessInstanceInfoForUnsubscribeInstanceOutput]'
    }

    attribute_map = {
        'order_id': 'OrderID',
        'order_id_list': 'OrderIDList',
        'success_instance_infos': 'SuccessInstanceInfos'
    }

    def __init__(self, order_id=None, order_id_list=None, success_instance_infos=None, _configuration=None):  # noqa: E501
        """UnsubscribeInstanceResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._order_id = None
        self._order_id_list = None
        self._success_instance_infos = None
        self.discriminator = None

        if order_id is not None:
            self.order_id = order_id
        if order_id_list is not None:
            self.order_id_list = order_id_list
        if success_instance_infos is not None:
            self.success_instance_infos = success_instance_infos

    @property
    def order_id(self):
        """Gets the order_id of this UnsubscribeInstanceResponse.  # noqa: E501


        :return: The order_id of this UnsubscribeInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id):
        """Sets the order_id of this UnsubscribeInstanceResponse.


        :param order_id: The order_id of this UnsubscribeInstanceResponse.  # noqa: E501
        :type: str
        """

        self._order_id = order_id

    @property
    def order_id_list(self):
        """Gets the order_id_list of this UnsubscribeInstanceResponse.  # noqa: E501


        :return: The order_id_list of this UnsubscribeInstanceResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._order_id_list

    @order_id_list.setter
    def order_id_list(self, order_id_list):
        """Sets the order_id_list of this UnsubscribeInstanceResponse.


        :param order_id_list: The order_id_list of this UnsubscribeInstanceResponse.  # noqa: E501
        :type: list[str]
        """

        self._order_id_list = order_id_list

    @property
    def success_instance_infos(self):
        """Gets the success_instance_infos of this UnsubscribeInstanceResponse.  # noqa: E501


        :return: The success_instance_infos of this UnsubscribeInstanceResponse.  # noqa: E501
        :rtype: list[SuccessInstanceInfoForUnsubscribeInstanceOutput]
        """
        return self._success_instance_infos

    @success_instance_infos.setter
    def success_instance_infos(self, success_instance_infos):
        """Sets the success_instance_infos of this UnsubscribeInstanceResponse.


        :param success_instance_infos: The success_instance_infos of this UnsubscribeInstanceResponse.  # noqa: E501
        :type: list[SuccessInstanceInfoForUnsubscribeInstanceOutput]
        """

        self._success_instance_infos = success_instance_infos

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UnsubscribeInstanceResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UnsubscribeInstanceResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UnsubscribeInstanceResponse):
            return True

        return self.to_dict() != other.to_dict()
