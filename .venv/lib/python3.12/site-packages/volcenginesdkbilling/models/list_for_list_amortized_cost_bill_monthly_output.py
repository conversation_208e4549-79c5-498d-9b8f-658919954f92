# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListForListAmortizedCostBillMonthlyOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'amortized_begin_time': 'str',
        'amortized_day_num': 'str',
        'amortized_end_time': 'str',
        'amortized_month': 'str',
        'amortized_type': 'str',
        'before_amortized_coupon_amount': 'str',
        'before_amortized_discount_bill_amount': 'str',
        'before_amortized_original_bill_amount': 'str',
        'before_amortized_paid_amount': 'str',
        'before_amortized_payable_amount': 'str',
        'before_amortized_preferential_bill_amount': 'str',
        'before_amortized_round_amount': 'str',
        'bill_category': 'str',
        'bill_id': 'str',
        'bill_period': 'str',
        'billing_function': 'str',
        'billing_method_code': 'str',
        'billing_mode': 'str',
        'busi_period': 'str',
        'business_mode': 'str',
        'config_name': 'str',
        'count': 'str',
        'coupon_amount': 'str',
        'currency': 'str',
        'daily_amortized_coupon_amount': 'str',
        'daily_amortized_discount_bill_amount': 'str',
        'daily_amortized_original_bill_amount': 'str',
        'daily_amortized_paid_amount': 'str',
        'daily_amortized_payable_amount': 'str',
        'daily_amortized_preferential_bill_amount': 'str',
        'daily_amortized_round_amount': 'str',
        'deduction_use_duration': 'str',
        'discount_bill_amount': 'str',
        'discount_biz_billing_function': 'str',
        'discount_biz_measure_interval': 'str',
        'discount_biz_unit_price': 'str',
        'discount_biz_unit_price_interval': 'str',
        'effective_factor': 'str',
        'element': 'str',
        'expand_field': 'str',
        'expense_time': 'str',
        'factor': 'str',
        'instance_name': 'str',
        'instance_no': 'str',
        'market_price': 'str',
        'measure_interval': 'str',
        'now_amortized_coupon_amount': 'str',
        'now_amortized_discount_bill_amount': 'str',
        'now_amortized_original_bill_amount': 'str',
        'now_amortized_paid_amount': 'str',
        'now_amortized_payable_amount': 'str',
        'now_amortized_preferential_bill_amount': 'str',
        'now_amortized_round_amount': 'str',
        'original_bill_amount': 'str',
        'owner_customer_name': 'str',
        'owner_id': 'str',
        'owner_user_name': 'str',
        'paid_amount': 'str',
        'payable_amount': 'str',
        'payer_customer_name': 'str',
        'payer_id': 'str',
        'payer_user_name': 'str',
        'preferential_bill_amount': 'str',
        'price': 'str',
        'price_interval': 'str',
        'price_unit': 'str',
        'product': 'str',
        'product_zh': 'str',
        'project': 'str',
        'project_display_name': 'str',
        'region': 'str',
        'round_amount': 'str',
        'seller_customer_name': 'str',
        'seller_id': 'str',
        'seller_user_name': 'str',
        'split_item_id': 'str',
        'split_item_name': 'str',
        'subject_name': 'str',
        'tag': 'str',
        'unamortized_coupon_amount': 'str',
        'unamortized_discount_bill_amount': 'str',
        'unamortized_original_bill_amount': 'str',
        'unamortized_paid_amount': 'str',
        'unamortized_payable_amount': 'str',
        'unamortized_preferential_bill_amount': 'str',
        'unamortized_round_amount': 'str',
        'unit': 'str',
        'use_duration': 'str',
        'use_duration_unit': 'str',
        'zone': 'str'
    }

    attribute_map = {
        'amortized_begin_time': 'AmortizedBeginTime',
        'amortized_day_num': 'AmortizedDayNum',
        'amortized_end_time': 'AmortizedEndTime',
        'amortized_month': 'AmortizedMonth',
        'amortized_type': 'AmortizedType',
        'before_amortized_coupon_amount': 'BeforeAmortizedCouponAmount',
        'before_amortized_discount_bill_amount': 'BeforeAmortizedDiscountBillAmount',
        'before_amortized_original_bill_amount': 'BeforeAmortizedOriginalBillAmount',
        'before_amortized_paid_amount': 'BeforeAmortizedPaidAmount',
        'before_amortized_payable_amount': 'BeforeAmortizedPayableAmount',
        'before_amortized_preferential_bill_amount': 'BeforeAmortizedPreferentialBillAmount',
        'before_amortized_round_amount': 'BeforeAmortizedRoundAmount',
        'bill_category': 'BillCategory',
        'bill_id': 'BillID',
        'bill_period': 'BillPeriod',
        'billing_function': 'BillingFunction',
        'billing_method_code': 'BillingMethodCode',
        'billing_mode': 'BillingMode',
        'busi_period': 'BusiPeriod',
        'business_mode': 'BusinessMode',
        'config_name': 'ConfigName',
        'count': 'Count',
        'coupon_amount': 'CouponAmount',
        'currency': 'Currency',
        'daily_amortized_coupon_amount': 'DailyAmortizedCouponAmount',
        'daily_amortized_discount_bill_amount': 'DailyAmortizedDiscountBillAmount',
        'daily_amortized_original_bill_amount': 'DailyAmortizedOriginalBillAmount',
        'daily_amortized_paid_amount': 'DailyAmortizedPaidAmount',
        'daily_amortized_payable_amount': 'DailyAmortizedPayableAmount',
        'daily_amortized_preferential_bill_amount': 'DailyAmortizedPreferentialBillAmount',
        'daily_amortized_round_amount': 'DailyAmortizedRoundAmount',
        'deduction_use_duration': 'DeductionUseDuration',
        'discount_bill_amount': 'DiscountBillAmount',
        'discount_biz_billing_function': 'DiscountBizBillingFunction',
        'discount_biz_measure_interval': 'DiscountBizMeasureInterval',
        'discount_biz_unit_price': 'DiscountBizUnitPrice',
        'discount_biz_unit_price_interval': 'DiscountBizUnitPriceInterval',
        'effective_factor': 'EffectiveFactor',
        'element': 'Element',
        'expand_field': 'ExpandField',
        'expense_time': 'ExpenseTime',
        'factor': 'Factor',
        'instance_name': 'InstanceName',
        'instance_no': 'InstanceNo',
        'market_price': 'MarketPrice',
        'measure_interval': 'MeasureInterval',
        'now_amortized_coupon_amount': 'NowAmortizedCouponAmount',
        'now_amortized_discount_bill_amount': 'NowAmortizedDiscountBillAmount',
        'now_amortized_original_bill_amount': 'NowAmortizedOriginalBillAmount',
        'now_amortized_paid_amount': 'NowAmortizedPaidAmount',
        'now_amortized_payable_amount': 'NowAmortizedPayableAmount',
        'now_amortized_preferential_bill_amount': 'NowAmortizedPreferentialBillAmount',
        'now_amortized_round_amount': 'NowAmortizedRoundAmount',
        'original_bill_amount': 'OriginalBillAmount',
        'owner_customer_name': 'OwnerCustomerName',
        'owner_id': 'OwnerID',
        'owner_user_name': 'OwnerUserName',
        'paid_amount': 'PaidAmount',
        'payable_amount': 'PayableAmount',
        'payer_customer_name': 'PayerCustomerName',
        'payer_id': 'PayerID',
        'payer_user_name': 'PayerUserName',
        'preferential_bill_amount': 'PreferentialBillAmount',
        'price': 'Price',
        'price_interval': 'PriceInterval',
        'price_unit': 'PriceUnit',
        'product': 'Product',
        'product_zh': 'ProductZh',
        'project': 'Project',
        'project_display_name': 'ProjectDisplayName',
        'region': 'Region',
        'round_amount': 'RoundAmount',
        'seller_customer_name': 'SellerCustomerName',
        'seller_id': 'SellerID',
        'seller_user_name': 'SellerUserName',
        'split_item_id': 'SplitItemID',
        'split_item_name': 'SplitItemName',
        'subject_name': 'SubjectName',
        'tag': 'Tag',
        'unamortized_coupon_amount': 'UnamortizedCouponAmount',
        'unamortized_discount_bill_amount': 'UnamortizedDiscountBillAmount',
        'unamortized_original_bill_amount': 'UnamortizedOriginalBillAmount',
        'unamortized_paid_amount': 'UnamortizedPaidAmount',
        'unamortized_payable_amount': 'UnamortizedPayableAmount',
        'unamortized_preferential_bill_amount': 'UnamortizedPreferentialBillAmount',
        'unamortized_round_amount': 'UnamortizedRoundAmount',
        'unit': 'Unit',
        'use_duration': 'UseDuration',
        'use_duration_unit': 'UseDurationUnit',
        'zone': 'Zone'
    }

    def __init__(self, amortized_begin_time=None, amortized_day_num=None, amortized_end_time=None, amortized_month=None, amortized_type=None, before_amortized_coupon_amount=None, before_amortized_discount_bill_amount=None, before_amortized_original_bill_amount=None, before_amortized_paid_amount=None, before_amortized_payable_amount=None, before_amortized_preferential_bill_amount=None, before_amortized_round_amount=None, bill_category=None, bill_id=None, bill_period=None, billing_function=None, billing_method_code=None, billing_mode=None, busi_period=None, business_mode=None, config_name=None, count=None, coupon_amount=None, currency=None, daily_amortized_coupon_amount=None, daily_amortized_discount_bill_amount=None, daily_amortized_original_bill_amount=None, daily_amortized_paid_amount=None, daily_amortized_payable_amount=None, daily_amortized_preferential_bill_amount=None, daily_amortized_round_amount=None, deduction_use_duration=None, discount_bill_amount=None, discount_biz_billing_function=None, discount_biz_measure_interval=None, discount_biz_unit_price=None, discount_biz_unit_price_interval=None, effective_factor=None, element=None, expand_field=None, expense_time=None, factor=None, instance_name=None, instance_no=None, market_price=None, measure_interval=None, now_amortized_coupon_amount=None, now_amortized_discount_bill_amount=None, now_amortized_original_bill_amount=None, now_amortized_paid_amount=None, now_amortized_payable_amount=None, now_amortized_preferential_bill_amount=None, now_amortized_round_amount=None, original_bill_amount=None, owner_customer_name=None, owner_id=None, owner_user_name=None, paid_amount=None, payable_amount=None, payer_customer_name=None, payer_id=None, payer_user_name=None, preferential_bill_amount=None, price=None, price_interval=None, price_unit=None, product=None, product_zh=None, project=None, project_display_name=None, region=None, round_amount=None, seller_customer_name=None, seller_id=None, seller_user_name=None, split_item_id=None, split_item_name=None, subject_name=None, tag=None, unamortized_coupon_amount=None, unamortized_discount_bill_amount=None, unamortized_original_bill_amount=None, unamortized_paid_amount=None, unamortized_payable_amount=None, unamortized_preferential_bill_amount=None, unamortized_round_amount=None, unit=None, use_duration=None, use_duration_unit=None, zone=None, _configuration=None):  # noqa: E501
        """ListForListAmortizedCostBillMonthlyOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._amortized_begin_time = None
        self._amortized_day_num = None
        self._amortized_end_time = None
        self._amortized_month = None
        self._amortized_type = None
        self._before_amortized_coupon_amount = None
        self._before_amortized_discount_bill_amount = None
        self._before_amortized_original_bill_amount = None
        self._before_amortized_paid_amount = None
        self._before_amortized_payable_amount = None
        self._before_amortized_preferential_bill_amount = None
        self._before_amortized_round_amount = None
        self._bill_category = None
        self._bill_id = None
        self._bill_period = None
        self._billing_function = None
        self._billing_method_code = None
        self._billing_mode = None
        self._busi_period = None
        self._business_mode = None
        self._config_name = None
        self._count = None
        self._coupon_amount = None
        self._currency = None
        self._daily_amortized_coupon_amount = None
        self._daily_amortized_discount_bill_amount = None
        self._daily_amortized_original_bill_amount = None
        self._daily_amortized_paid_amount = None
        self._daily_amortized_payable_amount = None
        self._daily_amortized_preferential_bill_amount = None
        self._daily_amortized_round_amount = None
        self._deduction_use_duration = None
        self._discount_bill_amount = None
        self._discount_biz_billing_function = None
        self._discount_biz_measure_interval = None
        self._discount_biz_unit_price = None
        self._discount_biz_unit_price_interval = None
        self._effective_factor = None
        self._element = None
        self._expand_field = None
        self._expense_time = None
        self._factor = None
        self._instance_name = None
        self._instance_no = None
        self._market_price = None
        self._measure_interval = None
        self._now_amortized_coupon_amount = None
        self._now_amortized_discount_bill_amount = None
        self._now_amortized_original_bill_amount = None
        self._now_amortized_paid_amount = None
        self._now_amortized_payable_amount = None
        self._now_amortized_preferential_bill_amount = None
        self._now_amortized_round_amount = None
        self._original_bill_amount = None
        self._owner_customer_name = None
        self._owner_id = None
        self._owner_user_name = None
        self._paid_amount = None
        self._payable_amount = None
        self._payer_customer_name = None
        self._payer_id = None
        self._payer_user_name = None
        self._preferential_bill_amount = None
        self._price = None
        self._price_interval = None
        self._price_unit = None
        self._product = None
        self._product_zh = None
        self._project = None
        self._project_display_name = None
        self._region = None
        self._round_amount = None
        self._seller_customer_name = None
        self._seller_id = None
        self._seller_user_name = None
        self._split_item_id = None
        self._split_item_name = None
        self._subject_name = None
        self._tag = None
        self._unamortized_coupon_amount = None
        self._unamortized_discount_bill_amount = None
        self._unamortized_original_bill_amount = None
        self._unamortized_paid_amount = None
        self._unamortized_payable_amount = None
        self._unamortized_preferential_bill_amount = None
        self._unamortized_round_amount = None
        self._unit = None
        self._use_duration = None
        self._use_duration_unit = None
        self._zone = None
        self.discriminator = None

        if amortized_begin_time is not None:
            self.amortized_begin_time = amortized_begin_time
        if amortized_day_num is not None:
            self.amortized_day_num = amortized_day_num
        if amortized_end_time is not None:
            self.amortized_end_time = amortized_end_time
        if amortized_month is not None:
            self.amortized_month = amortized_month
        if amortized_type is not None:
            self.amortized_type = amortized_type
        if before_amortized_coupon_amount is not None:
            self.before_amortized_coupon_amount = before_amortized_coupon_amount
        if before_amortized_discount_bill_amount is not None:
            self.before_amortized_discount_bill_amount = before_amortized_discount_bill_amount
        if before_amortized_original_bill_amount is not None:
            self.before_amortized_original_bill_amount = before_amortized_original_bill_amount
        if before_amortized_paid_amount is not None:
            self.before_amortized_paid_amount = before_amortized_paid_amount
        if before_amortized_payable_amount is not None:
            self.before_amortized_payable_amount = before_amortized_payable_amount
        if before_amortized_preferential_bill_amount is not None:
            self.before_amortized_preferential_bill_amount = before_amortized_preferential_bill_amount
        if before_amortized_round_amount is not None:
            self.before_amortized_round_amount = before_amortized_round_amount
        if bill_category is not None:
            self.bill_category = bill_category
        if bill_id is not None:
            self.bill_id = bill_id
        if bill_period is not None:
            self.bill_period = bill_period
        if billing_function is not None:
            self.billing_function = billing_function
        if billing_method_code is not None:
            self.billing_method_code = billing_method_code
        if billing_mode is not None:
            self.billing_mode = billing_mode
        if busi_period is not None:
            self.busi_period = busi_period
        if business_mode is not None:
            self.business_mode = business_mode
        if config_name is not None:
            self.config_name = config_name
        if count is not None:
            self.count = count
        if coupon_amount is not None:
            self.coupon_amount = coupon_amount
        if currency is not None:
            self.currency = currency
        if daily_amortized_coupon_amount is not None:
            self.daily_amortized_coupon_amount = daily_amortized_coupon_amount
        if daily_amortized_discount_bill_amount is not None:
            self.daily_amortized_discount_bill_amount = daily_amortized_discount_bill_amount
        if daily_amortized_original_bill_amount is not None:
            self.daily_amortized_original_bill_amount = daily_amortized_original_bill_amount
        if daily_amortized_paid_amount is not None:
            self.daily_amortized_paid_amount = daily_amortized_paid_amount
        if daily_amortized_payable_amount is not None:
            self.daily_amortized_payable_amount = daily_amortized_payable_amount
        if daily_amortized_preferential_bill_amount is not None:
            self.daily_amortized_preferential_bill_amount = daily_amortized_preferential_bill_amount
        if daily_amortized_round_amount is not None:
            self.daily_amortized_round_amount = daily_amortized_round_amount
        if deduction_use_duration is not None:
            self.deduction_use_duration = deduction_use_duration
        if discount_bill_amount is not None:
            self.discount_bill_amount = discount_bill_amount
        if discount_biz_billing_function is not None:
            self.discount_biz_billing_function = discount_biz_billing_function
        if discount_biz_measure_interval is not None:
            self.discount_biz_measure_interval = discount_biz_measure_interval
        if discount_biz_unit_price is not None:
            self.discount_biz_unit_price = discount_biz_unit_price
        if discount_biz_unit_price_interval is not None:
            self.discount_biz_unit_price_interval = discount_biz_unit_price_interval
        if effective_factor is not None:
            self.effective_factor = effective_factor
        if element is not None:
            self.element = element
        if expand_field is not None:
            self.expand_field = expand_field
        if expense_time is not None:
            self.expense_time = expense_time
        if factor is not None:
            self.factor = factor
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_no is not None:
            self.instance_no = instance_no
        if market_price is not None:
            self.market_price = market_price
        if measure_interval is not None:
            self.measure_interval = measure_interval
        if now_amortized_coupon_amount is not None:
            self.now_amortized_coupon_amount = now_amortized_coupon_amount
        if now_amortized_discount_bill_amount is not None:
            self.now_amortized_discount_bill_amount = now_amortized_discount_bill_amount
        if now_amortized_original_bill_amount is not None:
            self.now_amortized_original_bill_amount = now_amortized_original_bill_amount
        if now_amortized_paid_amount is not None:
            self.now_amortized_paid_amount = now_amortized_paid_amount
        if now_amortized_payable_amount is not None:
            self.now_amortized_payable_amount = now_amortized_payable_amount
        if now_amortized_preferential_bill_amount is not None:
            self.now_amortized_preferential_bill_amount = now_amortized_preferential_bill_amount
        if now_amortized_round_amount is not None:
            self.now_amortized_round_amount = now_amortized_round_amount
        if original_bill_amount is not None:
            self.original_bill_amount = original_bill_amount
        if owner_customer_name is not None:
            self.owner_customer_name = owner_customer_name
        if owner_id is not None:
            self.owner_id = owner_id
        if owner_user_name is not None:
            self.owner_user_name = owner_user_name
        if paid_amount is not None:
            self.paid_amount = paid_amount
        if payable_amount is not None:
            self.payable_amount = payable_amount
        if payer_customer_name is not None:
            self.payer_customer_name = payer_customer_name
        if payer_id is not None:
            self.payer_id = payer_id
        if payer_user_name is not None:
            self.payer_user_name = payer_user_name
        if preferential_bill_amount is not None:
            self.preferential_bill_amount = preferential_bill_amount
        if price is not None:
            self.price = price
        if price_interval is not None:
            self.price_interval = price_interval
        if price_unit is not None:
            self.price_unit = price_unit
        if product is not None:
            self.product = product
        if product_zh is not None:
            self.product_zh = product_zh
        if project is not None:
            self.project = project
        if project_display_name is not None:
            self.project_display_name = project_display_name
        if region is not None:
            self.region = region
        if round_amount is not None:
            self.round_amount = round_amount
        if seller_customer_name is not None:
            self.seller_customer_name = seller_customer_name
        if seller_id is not None:
            self.seller_id = seller_id
        if seller_user_name is not None:
            self.seller_user_name = seller_user_name
        if split_item_id is not None:
            self.split_item_id = split_item_id
        if split_item_name is not None:
            self.split_item_name = split_item_name
        if subject_name is not None:
            self.subject_name = subject_name
        if tag is not None:
            self.tag = tag
        if unamortized_coupon_amount is not None:
            self.unamortized_coupon_amount = unamortized_coupon_amount
        if unamortized_discount_bill_amount is not None:
            self.unamortized_discount_bill_amount = unamortized_discount_bill_amount
        if unamortized_original_bill_amount is not None:
            self.unamortized_original_bill_amount = unamortized_original_bill_amount
        if unamortized_paid_amount is not None:
            self.unamortized_paid_amount = unamortized_paid_amount
        if unamortized_payable_amount is not None:
            self.unamortized_payable_amount = unamortized_payable_amount
        if unamortized_preferential_bill_amount is not None:
            self.unamortized_preferential_bill_amount = unamortized_preferential_bill_amount
        if unamortized_round_amount is not None:
            self.unamortized_round_amount = unamortized_round_amount
        if unit is not None:
            self.unit = unit
        if use_duration is not None:
            self.use_duration = use_duration
        if use_duration_unit is not None:
            self.use_duration_unit = use_duration_unit
        if zone is not None:
            self.zone = zone

    @property
    def amortized_begin_time(self):
        """Gets the amortized_begin_time of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The amortized_begin_time of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._amortized_begin_time

    @amortized_begin_time.setter
    def amortized_begin_time(self, amortized_begin_time):
        """Sets the amortized_begin_time of this ListForListAmortizedCostBillMonthlyOutput.


        :param amortized_begin_time: The amortized_begin_time of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._amortized_begin_time = amortized_begin_time

    @property
    def amortized_day_num(self):
        """Gets the amortized_day_num of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The amortized_day_num of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._amortized_day_num

    @amortized_day_num.setter
    def amortized_day_num(self, amortized_day_num):
        """Sets the amortized_day_num of this ListForListAmortizedCostBillMonthlyOutput.


        :param amortized_day_num: The amortized_day_num of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._amortized_day_num = amortized_day_num

    @property
    def amortized_end_time(self):
        """Gets the amortized_end_time of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The amortized_end_time of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._amortized_end_time

    @amortized_end_time.setter
    def amortized_end_time(self, amortized_end_time):
        """Sets the amortized_end_time of this ListForListAmortizedCostBillMonthlyOutput.


        :param amortized_end_time: The amortized_end_time of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._amortized_end_time = amortized_end_time

    @property
    def amortized_month(self):
        """Gets the amortized_month of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The amortized_month of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._amortized_month

    @amortized_month.setter
    def amortized_month(self, amortized_month):
        """Sets the amortized_month of this ListForListAmortizedCostBillMonthlyOutput.


        :param amortized_month: The amortized_month of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._amortized_month = amortized_month

    @property
    def amortized_type(self):
        """Gets the amortized_type of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The amortized_type of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._amortized_type

    @amortized_type.setter
    def amortized_type(self, amortized_type):
        """Sets the amortized_type of this ListForListAmortizedCostBillMonthlyOutput.


        :param amortized_type: The amortized_type of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._amortized_type = amortized_type

    @property
    def before_amortized_coupon_amount(self):
        """Gets the before_amortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The before_amortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._before_amortized_coupon_amount

    @before_amortized_coupon_amount.setter
    def before_amortized_coupon_amount(self, before_amortized_coupon_amount):
        """Sets the before_amortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param before_amortized_coupon_amount: The before_amortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._before_amortized_coupon_amount = before_amortized_coupon_amount

    @property
    def before_amortized_discount_bill_amount(self):
        """Gets the before_amortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The before_amortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._before_amortized_discount_bill_amount

    @before_amortized_discount_bill_amount.setter
    def before_amortized_discount_bill_amount(self, before_amortized_discount_bill_amount):
        """Sets the before_amortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param before_amortized_discount_bill_amount: The before_amortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._before_amortized_discount_bill_amount = before_amortized_discount_bill_amount

    @property
    def before_amortized_original_bill_amount(self):
        """Gets the before_amortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The before_amortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._before_amortized_original_bill_amount

    @before_amortized_original_bill_amount.setter
    def before_amortized_original_bill_amount(self, before_amortized_original_bill_amount):
        """Sets the before_amortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param before_amortized_original_bill_amount: The before_amortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._before_amortized_original_bill_amount = before_amortized_original_bill_amount

    @property
    def before_amortized_paid_amount(self):
        """Gets the before_amortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The before_amortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._before_amortized_paid_amount

    @before_amortized_paid_amount.setter
    def before_amortized_paid_amount(self, before_amortized_paid_amount):
        """Sets the before_amortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param before_amortized_paid_amount: The before_amortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._before_amortized_paid_amount = before_amortized_paid_amount

    @property
    def before_amortized_payable_amount(self):
        """Gets the before_amortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The before_amortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._before_amortized_payable_amount

    @before_amortized_payable_amount.setter
    def before_amortized_payable_amount(self, before_amortized_payable_amount):
        """Sets the before_amortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param before_amortized_payable_amount: The before_amortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._before_amortized_payable_amount = before_amortized_payable_amount

    @property
    def before_amortized_preferential_bill_amount(self):
        """Gets the before_amortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The before_amortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._before_amortized_preferential_bill_amount

    @before_amortized_preferential_bill_amount.setter
    def before_amortized_preferential_bill_amount(self, before_amortized_preferential_bill_amount):
        """Sets the before_amortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param before_amortized_preferential_bill_amount: The before_amortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._before_amortized_preferential_bill_amount = before_amortized_preferential_bill_amount

    @property
    def before_amortized_round_amount(self):
        """Gets the before_amortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The before_amortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._before_amortized_round_amount

    @before_amortized_round_amount.setter
    def before_amortized_round_amount(self, before_amortized_round_amount):
        """Sets the before_amortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param before_amortized_round_amount: The before_amortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._before_amortized_round_amount = before_amortized_round_amount

    @property
    def bill_category(self):
        """Gets the bill_category of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The bill_category of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_category

    @bill_category.setter
    def bill_category(self, bill_category):
        """Sets the bill_category of this ListForListAmortizedCostBillMonthlyOutput.


        :param bill_category: The bill_category of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._bill_category = bill_category

    @property
    def bill_id(self):
        """Gets the bill_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The bill_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_id

    @bill_id.setter
    def bill_id(self, bill_id):
        """Sets the bill_id of this ListForListAmortizedCostBillMonthlyOutput.


        :param bill_id: The bill_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._bill_id = bill_id

    @property
    def bill_period(self):
        """Gets the bill_period of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The bill_period of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_period

    @bill_period.setter
    def bill_period(self, bill_period):
        """Sets the bill_period of this ListForListAmortizedCostBillMonthlyOutput.


        :param bill_period: The bill_period of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._bill_period = bill_period

    @property
    def billing_function(self):
        """Gets the billing_function of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The billing_function of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._billing_function

    @billing_function.setter
    def billing_function(self, billing_function):
        """Sets the billing_function of this ListForListAmortizedCostBillMonthlyOutput.


        :param billing_function: The billing_function of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._billing_function = billing_function

    @property
    def billing_method_code(self):
        """Gets the billing_method_code of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The billing_method_code of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._billing_method_code

    @billing_method_code.setter
    def billing_method_code(self, billing_method_code):
        """Sets the billing_method_code of this ListForListAmortizedCostBillMonthlyOutput.


        :param billing_method_code: The billing_method_code of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._billing_method_code = billing_method_code

    @property
    def billing_mode(self):
        """Gets the billing_mode of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The billing_mode of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._billing_mode

    @billing_mode.setter
    def billing_mode(self, billing_mode):
        """Sets the billing_mode of this ListForListAmortizedCostBillMonthlyOutput.


        :param billing_mode: The billing_mode of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._billing_mode = billing_mode

    @property
    def busi_period(self):
        """Gets the busi_period of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The busi_period of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._busi_period

    @busi_period.setter
    def busi_period(self, busi_period):
        """Sets the busi_period of this ListForListAmortizedCostBillMonthlyOutput.


        :param busi_period: The busi_period of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._busi_period = busi_period

    @property
    def business_mode(self):
        """Gets the business_mode of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The business_mode of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._business_mode

    @business_mode.setter
    def business_mode(self, business_mode):
        """Sets the business_mode of this ListForListAmortizedCostBillMonthlyOutput.


        :param business_mode: The business_mode of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._business_mode = business_mode

    @property
    def config_name(self):
        """Gets the config_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The config_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._config_name

    @config_name.setter
    def config_name(self, config_name):
        """Sets the config_name of this ListForListAmortizedCostBillMonthlyOutput.


        :param config_name: The config_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._config_name = config_name

    @property
    def count(self):
        """Gets the count of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The count of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._count

    @count.setter
    def count(self, count):
        """Sets the count of this ListForListAmortizedCostBillMonthlyOutput.


        :param count: The count of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._count = count

    @property
    def coupon_amount(self):
        """Gets the coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._coupon_amount

    @coupon_amount.setter
    def coupon_amount(self, coupon_amount):
        """Sets the coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param coupon_amount: The coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._coupon_amount = coupon_amount

    @property
    def currency(self):
        """Gets the currency of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The currency of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this ListForListAmortizedCostBillMonthlyOutput.


        :param currency: The currency of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._currency = currency

    @property
    def daily_amortized_coupon_amount(self):
        """Gets the daily_amortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The daily_amortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_coupon_amount

    @daily_amortized_coupon_amount.setter
    def daily_amortized_coupon_amount(self, daily_amortized_coupon_amount):
        """Sets the daily_amortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param daily_amortized_coupon_amount: The daily_amortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_coupon_amount = daily_amortized_coupon_amount

    @property
    def daily_amortized_discount_bill_amount(self):
        """Gets the daily_amortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The daily_amortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_discount_bill_amount

    @daily_amortized_discount_bill_amount.setter
    def daily_amortized_discount_bill_amount(self, daily_amortized_discount_bill_amount):
        """Sets the daily_amortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param daily_amortized_discount_bill_amount: The daily_amortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_discount_bill_amount = daily_amortized_discount_bill_amount

    @property
    def daily_amortized_original_bill_amount(self):
        """Gets the daily_amortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The daily_amortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_original_bill_amount

    @daily_amortized_original_bill_amount.setter
    def daily_amortized_original_bill_amount(self, daily_amortized_original_bill_amount):
        """Sets the daily_amortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param daily_amortized_original_bill_amount: The daily_amortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_original_bill_amount = daily_amortized_original_bill_amount

    @property
    def daily_amortized_paid_amount(self):
        """Gets the daily_amortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The daily_amortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_paid_amount

    @daily_amortized_paid_amount.setter
    def daily_amortized_paid_amount(self, daily_amortized_paid_amount):
        """Sets the daily_amortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param daily_amortized_paid_amount: The daily_amortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_paid_amount = daily_amortized_paid_amount

    @property
    def daily_amortized_payable_amount(self):
        """Gets the daily_amortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The daily_amortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_payable_amount

    @daily_amortized_payable_amount.setter
    def daily_amortized_payable_amount(self, daily_amortized_payable_amount):
        """Sets the daily_amortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param daily_amortized_payable_amount: The daily_amortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_payable_amount = daily_amortized_payable_amount

    @property
    def daily_amortized_preferential_bill_amount(self):
        """Gets the daily_amortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The daily_amortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_preferential_bill_amount

    @daily_amortized_preferential_bill_amount.setter
    def daily_amortized_preferential_bill_amount(self, daily_amortized_preferential_bill_amount):
        """Sets the daily_amortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param daily_amortized_preferential_bill_amount: The daily_amortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_preferential_bill_amount = daily_amortized_preferential_bill_amount

    @property
    def daily_amortized_round_amount(self):
        """Gets the daily_amortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The daily_amortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_round_amount

    @daily_amortized_round_amount.setter
    def daily_amortized_round_amount(self, daily_amortized_round_amount):
        """Sets the daily_amortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param daily_amortized_round_amount: The daily_amortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_round_amount = daily_amortized_round_amount

    @property
    def deduction_use_duration(self):
        """Gets the deduction_use_duration of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The deduction_use_duration of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_use_duration

    @deduction_use_duration.setter
    def deduction_use_duration(self, deduction_use_duration):
        """Sets the deduction_use_duration of this ListForListAmortizedCostBillMonthlyOutput.


        :param deduction_use_duration: The deduction_use_duration of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._deduction_use_duration = deduction_use_duration

    @property
    def discount_bill_amount(self):
        """Gets the discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_bill_amount

    @discount_bill_amount.setter
    def discount_bill_amount(self, discount_bill_amount):
        """Sets the discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param discount_bill_amount: The discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._discount_bill_amount = discount_bill_amount

    @property
    def discount_biz_billing_function(self):
        """Gets the discount_biz_billing_function of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The discount_biz_billing_function of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_biz_billing_function

    @discount_biz_billing_function.setter
    def discount_biz_billing_function(self, discount_biz_billing_function):
        """Sets the discount_biz_billing_function of this ListForListAmortizedCostBillMonthlyOutput.


        :param discount_biz_billing_function: The discount_biz_billing_function of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._discount_biz_billing_function = discount_biz_billing_function

    @property
    def discount_biz_measure_interval(self):
        """Gets the discount_biz_measure_interval of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The discount_biz_measure_interval of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_biz_measure_interval

    @discount_biz_measure_interval.setter
    def discount_biz_measure_interval(self, discount_biz_measure_interval):
        """Sets the discount_biz_measure_interval of this ListForListAmortizedCostBillMonthlyOutput.


        :param discount_biz_measure_interval: The discount_biz_measure_interval of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._discount_biz_measure_interval = discount_biz_measure_interval

    @property
    def discount_biz_unit_price(self):
        """Gets the discount_biz_unit_price of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The discount_biz_unit_price of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_biz_unit_price

    @discount_biz_unit_price.setter
    def discount_biz_unit_price(self, discount_biz_unit_price):
        """Sets the discount_biz_unit_price of this ListForListAmortizedCostBillMonthlyOutput.


        :param discount_biz_unit_price: The discount_biz_unit_price of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._discount_biz_unit_price = discount_biz_unit_price

    @property
    def discount_biz_unit_price_interval(self):
        """Gets the discount_biz_unit_price_interval of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The discount_biz_unit_price_interval of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_biz_unit_price_interval

    @discount_biz_unit_price_interval.setter
    def discount_biz_unit_price_interval(self, discount_biz_unit_price_interval):
        """Sets the discount_biz_unit_price_interval of this ListForListAmortizedCostBillMonthlyOutput.


        :param discount_biz_unit_price_interval: The discount_biz_unit_price_interval of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._discount_biz_unit_price_interval = discount_biz_unit_price_interval

    @property
    def effective_factor(self):
        """Gets the effective_factor of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The effective_factor of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._effective_factor

    @effective_factor.setter
    def effective_factor(self, effective_factor):
        """Sets the effective_factor of this ListForListAmortizedCostBillMonthlyOutput.


        :param effective_factor: The effective_factor of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._effective_factor = effective_factor

    @property
    def element(self):
        """Gets the element of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The element of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._element

    @element.setter
    def element(self, element):
        """Sets the element of this ListForListAmortizedCostBillMonthlyOutput.


        :param element: The element of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._element = element

    @property
    def expand_field(self):
        """Gets the expand_field of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The expand_field of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._expand_field

    @expand_field.setter
    def expand_field(self, expand_field):
        """Sets the expand_field of this ListForListAmortizedCostBillMonthlyOutput.


        :param expand_field: The expand_field of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._expand_field = expand_field

    @property
    def expense_time(self):
        """Gets the expense_time of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The expense_time of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._expense_time

    @expense_time.setter
    def expense_time(self, expense_time):
        """Sets the expense_time of this ListForListAmortizedCostBillMonthlyOutput.


        :param expense_time: The expense_time of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._expense_time = expense_time

    @property
    def factor(self):
        """Gets the factor of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The factor of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._factor

    @factor.setter
    def factor(self, factor):
        """Sets the factor of this ListForListAmortizedCostBillMonthlyOutput.


        :param factor: The factor of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._factor = factor

    @property
    def instance_name(self):
        """Gets the instance_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The instance_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this ListForListAmortizedCostBillMonthlyOutput.


        :param instance_name: The instance_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_no(self):
        """Gets the instance_no of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The instance_no of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_no

    @instance_no.setter
    def instance_no(self, instance_no):
        """Sets the instance_no of this ListForListAmortizedCostBillMonthlyOutput.


        :param instance_no: The instance_no of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._instance_no = instance_no

    @property
    def market_price(self):
        """Gets the market_price of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The market_price of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._market_price

    @market_price.setter
    def market_price(self, market_price):
        """Sets the market_price of this ListForListAmortizedCostBillMonthlyOutput.


        :param market_price: The market_price of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._market_price = market_price

    @property
    def measure_interval(self):
        """Gets the measure_interval of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The measure_interval of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._measure_interval

    @measure_interval.setter
    def measure_interval(self, measure_interval):
        """Sets the measure_interval of this ListForListAmortizedCostBillMonthlyOutput.


        :param measure_interval: The measure_interval of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._measure_interval = measure_interval

    @property
    def now_amortized_coupon_amount(self):
        """Gets the now_amortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The now_amortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._now_amortized_coupon_amount

    @now_amortized_coupon_amount.setter
    def now_amortized_coupon_amount(self, now_amortized_coupon_amount):
        """Sets the now_amortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param now_amortized_coupon_amount: The now_amortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._now_amortized_coupon_amount = now_amortized_coupon_amount

    @property
    def now_amortized_discount_bill_amount(self):
        """Gets the now_amortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The now_amortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._now_amortized_discount_bill_amount

    @now_amortized_discount_bill_amount.setter
    def now_amortized_discount_bill_amount(self, now_amortized_discount_bill_amount):
        """Sets the now_amortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param now_amortized_discount_bill_amount: The now_amortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._now_amortized_discount_bill_amount = now_amortized_discount_bill_amount

    @property
    def now_amortized_original_bill_amount(self):
        """Gets the now_amortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The now_amortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._now_amortized_original_bill_amount

    @now_amortized_original_bill_amount.setter
    def now_amortized_original_bill_amount(self, now_amortized_original_bill_amount):
        """Sets the now_amortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param now_amortized_original_bill_amount: The now_amortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._now_amortized_original_bill_amount = now_amortized_original_bill_amount

    @property
    def now_amortized_paid_amount(self):
        """Gets the now_amortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The now_amortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._now_amortized_paid_amount

    @now_amortized_paid_amount.setter
    def now_amortized_paid_amount(self, now_amortized_paid_amount):
        """Sets the now_amortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param now_amortized_paid_amount: The now_amortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._now_amortized_paid_amount = now_amortized_paid_amount

    @property
    def now_amortized_payable_amount(self):
        """Gets the now_amortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The now_amortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._now_amortized_payable_amount

    @now_amortized_payable_amount.setter
    def now_amortized_payable_amount(self, now_amortized_payable_amount):
        """Sets the now_amortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param now_amortized_payable_amount: The now_amortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._now_amortized_payable_amount = now_amortized_payable_amount

    @property
    def now_amortized_preferential_bill_amount(self):
        """Gets the now_amortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The now_amortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._now_amortized_preferential_bill_amount

    @now_amortized_preferential_bill_amount.setter
    def now_amortized_preferential_bill_amount(self, now_amortized_preferential_bill_amount):
        """Sets the now_amortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param now_amortized_preferential_bill_amount: The now_amortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._now_amortized_preferential_bill_amount = now_amortized_preferential_bill_amount

    @property
    def now_amortized_round_amount(self):
        """Gets the now_amortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The now_amortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._now_amortized_round_amount

    @now_amortized_round_amount.setter
    def now_amortized_round_amount(self, now_amortized_round_amount):
        """Sets the now_amortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param now_amortized_round_amount: The now_amortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._now_amortized_round_amount = now_amortized_round_amount

    @property
    def original_bill_amount(self):
        """Gets the original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._original_bill_amount

    @original_bill_amount.setter
    def original_bill_amount(self, original_bill_amount):
        """Sets the original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param original_bill_amount: The original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._original_bill_amount = original_bill_amount

    @property
    def owner_customer_name(self):
        """Gets the owner_customer_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The owner_customer_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_customer_name

    @owner_customer_name.setter
    def owner_customer_name(self, owner_customer_name):
        """Sets the owner_customer_name of this ListForListAmortizedCostBillMonthlyOutput.


        :param owner_customer_name: The owner_customer_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._owner_customer_name = owner_customer_name

    @property
    def owner_id(self):
        """Gets the owner_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The owner_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_id

    @owner_id.setter
    def owner_id(self, owner_id):
        """Sets the owner_id of this ListForListAmortizedCostBillMonthlyOutput.


        :param owner_id: The owner_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._owner_id = owner_id

    @property
    def owner_user_name(self):
        """Gets the owner_user_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The owner_user_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_user_name

    @owner_user_name.setter
    def owner_user_name(self, owner_user_name):
        """Sets the owner_user_name of this ListForListAmortizedCostBillMonthlyOutput.


        :param owner_user_name: The owner_user_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._owner_user_name = owner_user_name

    @property
    def paid_amount(self):
        """Gets the paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._paid_amount

    @paid_amount.setter
    def paid_amount(self, paid_amount):
        """Sets the paid_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param paid_amount: The paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._paid_amount = paid_amount

    @property
    def payable_amount(self):
        """Gets the payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._payable_amount

    @payable_amount.setter
    def payable_amount(self, payable_amount):
        """Sets the payable_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param payable_amount: The payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._payable_amount = payable_amount

    @property
    def payer_customer_name(self):
        """Gets the payer_customer_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The payer_customer_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_customer_name

    @payer_customer_name.setter
    def payer_customer_name(self, payer_customer_name):
        """Sets the payer_customer_name of this ListForListAmortizedCostBillMonthlyOutput.


        :param payer_customer_name: The payer_customer_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._payer_customer_name = payer_customer_name

    @property
    def payer_id(self):
        """Gets the payer_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The payer_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_id

    @payer_id.setter
    def payer_id(self, payer_id):
        """Sets the payer_id of this ListForListAmortizedCostBillMonthlyOutput.


        :param payer_id: The payer_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._payer_id = payer_id

    @property
    def payer_user_name(self):
        """Gets the payer_user_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The payer_user_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_user_name

    @payer_user_name.setter
    def payer_user_name(self, payer_user_name):
        """Sets the payer_user_name of this ListForListAmortizedCostBillMonthlyOutput.


        :param payer_user_name: The payer_user_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._payer_user_name = payer_user_name

    @property
    def preferential_bill_amount(self):
        """Gets the preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._preferential_bill_amount

    @preferential_bill_amount.setter
    def preferential_bill_amount(self, preferential_bill_amount):
        """Sets the preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param preferential_bill_amount: The preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._preferential_bill_amount = preferential_bill_amount

    @property
    def price(self):
        """Gets the price of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The price of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._price

    @price.setter
    def price(self, price):
        """Sets the price of this ListForListAmortizedCostBillMonthlyOutput.


        :param price: The price of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._price = price

    @property
    def price_interval(self):
        """Gets the price_interval of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The price_interval of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._price_interval

    @price_interval.setter
    def price_interval(self, price_interval):
        """Sets the price_interval of this ListForListAmortizedCostBillMonthlyOutput.


        :param price_interval: The price_interval of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._price_interval = price_interval

    @property
    def price_unit(self):
        """Gets the price_unit of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The price_unit of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._price_unit

    @price_unit.setter
    def price_unit(self, price_unit):
        """Sets the price_unit of this ListForListAmortizedCostBillMonthlyOutput.


        :param price_unit: The price_unit of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._price_unit = price_unit

    @property
    def product(self):
        """Gets the product of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The product of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._product

    @product.setter
    def product(self, product):
        """Sets the product of this ListForListAmortizedCostBillMonthlyOutput.


        :param product: The product of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._product = product

    @property
    def product_zh(self):
        """Gets the product_zh of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The product_zh of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._product_zh

    @product_zh.setter
    def product_zh(self, product_zh):
        """Sets the product_zh of this ListForListAmortizedCostBillMonthlyOutput.


        :param product_zh: The product_zh of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._product_zh = product_zh

    @property
    def project(self):
        """Gets the project of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The project of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._project

    @project.setter
    def project(self, project):
        """Sets the project of this ListForListAmortizedCostBillMonthlyOutput.


        :param project: The project of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._project = project

    @property
    def project_display_name(self):
        """Gets the project_display_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The project_display_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_display_name

    @project_display_name.setter
    def project_display_name(self, project_display_name):
        """Sets the project_display_name of this ListForListAmortizedCostBillMonthlyOutput.


        :param project_display_name: The project_display_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._project_display_name = project_display_name

    @property
    def region(self):
        """Gets the region of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The region of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this ListForListAmortizedCostBillMonthlyOutput.


        :param region: The region of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def round_amount(self):
        """Gets the round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._round_amount

    @round_amount.setter
    def round_amount(self, round_amount):
        """Sets the round_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param round_amount: The round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._round_amount = round_amount

    @property
    def seller_customer_name(self):
        """Gets the seller_customer_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The seller_customer_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_customer_name

    @seller_customer_name.setter
    def seller_customer_name(self, seller_customer_name):
        """Sets the seller_customer_name of this ListForListAmortizedCostBillMonthlyOutput.


        :param seller_customer_name: The seller_customer_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._seller_customer_name = seller_customer_name

    @property
    def seller_id(self):
        """Gets the seller_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The seller_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_id

    @seller_id.setter
    def seller_id(self, seller_id):
        """Sets the seller_id of this ListForListAmortizedCostBillMonthlyOutput.


        :param seller_id: The seller_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._seller_id = seller_id

    @property
    def seller_user_name(self):
        """Gets the seller_user_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The seller_user_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_user_name

    @seller_user_name.setter
    def seller_user_name(self, seller_user_name):
        """Sets the seller_user_name of this ListForListAmortizedCostBillMonthlyOutput.


        :param seller_user_name: The seller_user_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._seller_user_name = seller_user_name

    @property
    def split_item_id(self):
        """Gets the split_item_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The split_item_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._split_item_id

    @split_item_id.setter
    def split_item_id(self, split_item_id):
        """Sets the split_item_id of this ListForListAmortizedCostBillMonthlyOutput.


        :param split_item_id: The split_item_id of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._split_item_id = split_item_id

    @property
    def split_item_name(self):
        """Gets the split_item_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The split_item_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._split_item_name

    @split_item_name.setter
    def split_item_name(self, split_item_name):
        """Sets the split_item_name of this ListForListAmortizedCostBillMonthlyOutput.


        :param split_item_name: The split_item_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._split_item_name = split_item_name

    @property
    def subject_name(self):
        """Gets the subject_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The subject_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._subject_name

    @subject_name.setter
    def subject_name(self, subject_name):
        """Sets the subject_name of this ListForListAmortizedCostBillMonthlyOutput.


        :param subject_name: The subject_name of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._subject_name = subject_name

    @property
    def tag(self):
        """Gets the tag of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The tag of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this ListForListAmortizedCostBillMonthlyOutput.


        :param tag: The tag of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def unamortized_coupon_amount(self):
        """Gets the unamortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The unamortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._unamortized_coupon_amount

    @unamortized_coupon_amount.setter
    def unamortized_coupon_amount(self, unamortized_coupon_amount):
        """Sets the unamortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param unamortized_coupon_amount: The unamortized_coupon_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._unamortized_coupon_amount = unamortized_coupon_amount

    @property
    def unamortized_discount_bill_amount(self):
        """Gets the unamortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The unamortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._unamortized_discount_bill_amount

    @unamortized_discount_bill_amount.setter
    def unamortized_discount_bill_amount(self, unamortized_discount_bill_amount):
        """Sets the unamortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param unamortized_discount_bill_amount: The unamortized_discount_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._unamortized_discount_bill_amount = unamortized_discount_bill_amount

    @property
    def unamortized_original_bill_amount(self):
        """Gets the unamortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The unamortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._unamortized_original_bill_amount

    @unamortized_original_bill_amount.setter
    def unamortized_original_bill_amount(self, unamortized_original_bill_amount):
        """Sets the unamortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param unamortized_original_bill_amount: The unamortized_original_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._unamortized_original_bill_amount = unamortized_original_bill_amount

    @property
    def unamortized_paid_amount(self):
        """Gets the unamortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The unamortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._unamortized_paid_amount

    @unamortized_paid_amount.setter
    def unamortized_paid_amount(self, unamortized_paid_amount):
        """Sets the unamortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param unamortized_paid_amount: The unamortized_paid_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._unamortized_paid_amount = unamortized_paid_amount

    @property
    def unamortized_payable_amount(self):
        """Gets the unamortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The unamortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._unamortized_payable_amount

    @unamortized_payable_amount.setter
    def unamortized_payable_amount(self, unamortized_payable_amount):
        """Sets the unamortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param unamortized_payable_amount: The unamortized_payable_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._unamortized_payable_amount = unamortized_payable_amount

    @property
    def unamortized_preferential_bill_amount(self):
        """Gets the unamortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The unamortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._unamortized_preferential_bill_amount

    @unamortized_preferential_bill_amount.setter
    def unamortized_preferential_bill_amount(self, unamortized_preferential_bill_amount):
        """Sets the unamortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param unamortized_preferential_bill_amount: The unamortized_preferential_bill_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._unamortized_preferential_bill_amount = unamortized_preferential_bill_amount

    @property
    def unamortized_round_amount(self):
        """Gets the unamortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The unamortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._unamortized_round_amount

    @unamortized_round_amount.setter
    def unamortized_round_amount(self, unamortized_round_amount):
        """Sets the unamortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.


        :param unamortized_round_amount: The unamortized_round_amount of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._unamortized_round_amount = unamortized_round_amount

    @property
    def unit(self):
        """Gets the unit of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The unit of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._unit

    @unit.setter
    def unit(self, unit):
        """Sets the unit of this ListForListAmortizedCostBillMonthlyOutput.


        :param unit: The unit of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._unit = unit

    @property
    def use_duration(self):
        """Gets the use_duration of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The use_duration of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._use_duration

    @use_duration.setter
    def use_duration(self, use_duration):
        """Sets the use_duration of this ListForListAmortizedCostBillMonthlyOutput.


        :param use_duration: The use_duration of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._use_duration = use_duration

    @property
    def use_duration_unit(self):
        """Gets the use_duration_unit of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The use_duration_unit of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._use_duration_unit

    @use_duration_unit.setter
    def use_duration_unit(self, use_duration_unit):
        """Sets the use_duration_unit of this ListForListAmortizedCostBillMonthlyOutput.


        :param use_duration_unit: The use_duration_unit of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._use_duration_unit = use_duration_unit

    @property
    def zone(self):
        """Gets the zone of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501


        :return: The zone of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone

    @zone.setter
    def zone(self, zone):
        """Sets the zone of this ListForListAmortizedCostBillMonthlyOutput.


        :param zone: The zone of this ListForListAmortizedCostBillMonthlyOutput.  # noqa: E501
        :type: str
        """

        self._zone = zone

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListForListAmortizedCostBillMonthlyOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListForListAmortizedCostBillMonthlyOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListForListAmortizedCostBillMonthlyOutput):
            return True

        return self.to_dict() != other.to_dict()
