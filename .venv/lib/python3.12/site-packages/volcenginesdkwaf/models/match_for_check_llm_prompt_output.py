# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MatchForCheckLLMPromptOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'label': 'str',
        'word': 'str'
    }

    attribute_map = {
        'label': 'Label',
        'word': 'Word'
    }

    def __init__(self, label=None, word=None, _configuration=None):  # noqa: E501
        """MatchForCheckLLMPromptOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._label = None
        self._word = None
        self.discriminator = None

        if label is not None:
            self.label = label
        if word is not None:
            self.word = word

    @property
    def label(self):
        """Gets the label of this MatchForCheckLLMPromptOutput.  # noqa: E501


        :return: The label of this MatchForCheckLLMPromptOutput.  # noqa: E501
        :rtype: str
        """
        return self._label

    @label.setter
    def label(self, label):
        """Sets the label of this MatchForCheckLLMPromptOutput.


        :param label: The label of this MatchForCheckLLMPromptOutput.  # noqa: E501
        :type: str
        """

        self._label = label

    @property
    def word(self):
        """Gets the word of this MatchForCheckLLMPromptOutput.  # noqa: E501


        :return: The word of this MatchForCheckLLMPromptOutput.  # noqa: E501
        :rtype: str
        """
        return self._word

    @word.setter
    def word(self, word):
        """Sets the word of this MatchForCheckLLMPromptOutput.


        :param word: The word of this MatchForCheckLLMPromptOutput.  # noqa: E501
        :type: str
        """

        self._word = word

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MatchForCheckLLMPromptOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MatchForCheckLLMPromptOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MatchForCheckLLMPromptOutput):
            return True

        return self.to_dict() != other.to_dict()
