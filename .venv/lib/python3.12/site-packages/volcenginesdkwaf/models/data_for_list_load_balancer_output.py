# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListLoadBalancerOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'eip_address': 'str',
        'eni_address': 'str',
        'eni_ipv6_address': 'str',
        'id': 'str',
        'listener_id': 'str',
        'listener_name': 'str',
        'load_balancer_type': 'str',
        'name': 'str',
        'port': 'int',
        'protocol': 'str'
    }

    attribute_map = {
        'eip_address': 'EipAddress',
        'eni_address': 'EniAddress',
        'eni_ipv6_address': 'EniIpv6Address',
        'id': 'Id',
        'listener_id': 'ListenerID',
        'listener_name': 'ListenerName',
        'load_balancer_type': 'LoadBalancerType',
        'name': 'Name',
        'port': 'Port',
        'protocol': 'Protocol'
    }

    def __init__(self, eip_address=None, eni_address=None, eni_ipv6_address=None, id=None, listener_id=None, listener_name=None, load_balancer_type=None, name=None, port=None, protocol=None, _configuration=None):  # noqa: E501
        """DataForListLoadBalancerOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._eip_address = None
        self._eni_address = None
        self._eni_ipv6_address = None
        self._id = None
        self._listener_id = None
        self._listener_name = None
        self._load_balancer_type = None
        self._name = None
        self._port = None
        self._protocol = None
        self.discriminator = None

        if eip_address is not None:
            self.eip_address = eip_address
        if eni_address is not None:
            self.eni_address = eni_address
        if eni_ipv6_address is not None:
            self.eni_ipv6_address = eni_ipv6_address
        if id is not None:
            self.id = id
        if listener_id is not None:
            self.listener_id = listener_id
        if listener_name is not None:
            self.listener_name = listener_name
        if load_balancer_type is not None:
            self.load_balancer_type = load_balancer_type
        if name is not None:
            self.name = name
        if port is not None:
            self.port = port
        if protocol is not None:
            self.protocol = protocol

    @property
    def eip_address(self):
        """Gets the eip_address of this DataForListLoadBalancerOutput.  # noqa: E501


        :return: The eip_address of this DataForListLoadBalancerOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this DataForListLoadBalancerOutput.


        :param eip_address: The eip_address of this DataForListLoadBalancerOutput.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def eni_address(self):
        """Gets the eni_address of this DataForListLoadBalancerOutput.  # noqa: E501


        :return: The eni_address of this DataForListLoadBalancerOutput.  # noqa: E501
        :rtype: str
        """
        return self._eni_address

    @eni_address.setter
    def eni_address(self, eni_address):
        """Sets the eni_address of this DataForListLoadBalancerOutput.


        :param eni_address: The eni_address of this DataForListLoadBalancerOutput.  # noqa: E501
        :type: str
        """

        self._eni_address = eni_address

    @property
    def eni_ipv6_address(self):
        """Gets the eni_ipv6_address of this DataForListLoadBalancerOutput.  # noqa: E501


        :return: The eni_ipv6_address of this DataForListLoadBalancerOutput.  # noqa: E501
        :rtype: str
        """
        return self._eni_ipv6_address

    @eni_ipv6_address.setter
    def eni_ipv6_address(self, eni_ipv6_address):
        """Sets the eni_ipv6_address of this DataForListLoadBalancerOutput.


        :param eni_ipv6_address: The eni_ipv6_address of this DataForListLoadBalancerOutput.  # noqa: E501
        :type: str
        """

        self._eni_ipv6_address = eni_ipv6_address

    @property
    def id(self):
        """Gets the id of this DataForListLoadBalancerOutput.  # noqa: E501


        :return: The id of this DataForListLoadBalancerOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListLoadBalancerOutput.


        :param id: The id of this DataForListLoadBalancerOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def listener_id(self):
        """Gets the listener_id of this DataForListLoadBalancerOutput.  # noqa: E501


        :return: The listener_id of this DataForListLoadBalancerOutput.  # noqa: E501
        :rtype: str
        """
        return self._listener_id

    @listener_id.setter
    def listener_id(self, listener_id):
        """Sets the listener_id of this DataForListLoadBalancerOutput.


        :param listener_id: The listener_id of this DataForListLoadBalancerOutput.  # noqa: E501
        :type: str
        """

        self._listener_id = listener_id

    @property
    def listener_name(self):
        """Gets the listener_name of this DataForListLoadBalancerOutput.  # noqa: E501


        :return: The listener_name of this DataForListLoadBalancerOutput.  # noqa: E501
        :rtype: str
        """
        return self._listener_name

    @listener_name.setter
    def listener_name(self, listener_name):
        """Sets the listener_name of this DataForListLoadBalancerOutput.


        :param listener_name: The listener_name of this DataForListLoadBalancerOutput.  # noqa: E501
        :type: str
        """

        self._listener_name = listener_name

    @property
    def load_balancer_type(self):
        """Gets the load_balancer_type of this DataForListLoadBalancerOutput.  # noqa: E501


        :return: The load_balancer_type of this DataForListLoadBalancerOutput.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_type

    @load_balancer_type.setter
    def load_balancer_type(self, load_balancer_type):
        """Sets the load_balancer_type of this DataForListLoadBalancerOutput.


        :param load_balancer_type: The load_balancer_type of this DataForListLoadBalancerOutput.  # noqa: E501
        :type: str
        """

        self._load_balancer_type = load_balancer_type

    @property
    def name(self):
        """Gets the name of this DataForListLoadBalancerOutput.  # noqa: E501


        :return: The name of this DataForListLoadBalancerOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForListLoadBalancerOutput.


        :param name: The name of this DataForListLoadBalancerOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def port(self):
        """Gets the port of this DataForListLoadBalancerOutput.  # noqa: E501


        :return: The port of this DataForListLoadBalancerOutput.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this DataForListLoadBalancerOutput.


        :param port: The port of this DataForListLoadBalancerOutput.  # noqa: E501
        :type: int
        """

        self._port = port

    @property
    def protocol(self):
        """Gets the protocol of this DataForListLoadBalancerOutput.  # noqa: E501


        :return: The protocol of this DataForListLoadBalancerOutput.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this DataForListLoadBalancerOutput.


        :param protocol: The protocol of this DataForListLoadBalancerOutput.  # noqa: E501
        :type: str
        """

        self._protocol = protocol

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListLoadBalancerOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListLoadBalancerOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListLoadBalancerOutput):
            return True

        return self.to_dict() != other.to_dict()
