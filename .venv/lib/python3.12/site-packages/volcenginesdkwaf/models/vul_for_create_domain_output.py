# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VulForCreateDomainOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action': 'str',
        'rule_mode': 'str',
        'waf_enable': 'int'
    }

    attribute_map = {
        'action': 'Action',
        'rule_mode': 'RuleMode',
        'waf_enable': 'WafEnable'
    }

    def __init__(self, action=None, rule_mode=None, waf_enable=None, _configuration=None):  # noqa: E501
        """VulForCreateDomainOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action = None
        self._rule_mode = None
        self._waf_enable = None
        self.discriminator = None

        if action is not None:
            self.action = action
        if rule_mode is not None:
            self.rule_mode = rule_mode
        if waf_enable is not None:
            self.waf_enable = waf_enable

    @property
    def action(self):
        """Gets the action of this VulForCreateDomainOutput.  # noqa: E501


        :return: The action of this VulForCreateDomainOutput.  # noqa: E501
        :rtype: str
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this VulForCreateDomainOutput.


        :param action: The action of this VulForCreateDomainOutput.  # noqa: E501
        :type: str
        """

        self._action = action

    @property
    def rule_mode(self):
        """Gets the rule_mode of this VulForCreateDomainOutput.  # noqa: E501


        :return: The rule_mode of this VulForCreateDomainOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_mode

    @rule_mode.setter
    def rule_mode(self, rule_mode):
        """Sets the rule_mode of this VulForCreateDomainOutput.


        :param rule_mode: The rule_mode of this VulForCreateDomainOutput.  # noqa: E501
        :type: str
        """

        self._rule_mode = rule_mode

    @property
    def waf_enable(self):
        """Gets the waf_enable of this VulForCreateDomainOutput.  # noqa: E501


        :return: The waf_enable of this VulForCreateDomainOutput.  # noqa: E501
        :rtype: int
        """
        return self._waf_enable

    @waf_enable.setter
    def waf_enable(self, waf_enable):
        """Sets the waf_enable of this VulForCreateDomainOutput.


        :param waf_enable: The waf_enable of this VulForCreateDomainOutput.  # noqa: E501
        :type: int
        """

        self._waf_enable = waf_enable

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VulForCreateDomainOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VulForCreateDomainOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VulForCreateDomainOutput):
            return True

        return self.to_dict() != other.to_dict()
