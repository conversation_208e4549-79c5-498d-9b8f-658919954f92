# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetInstanceCtlResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_enable': 'int',
        'block_enable': 'int',
        'prompt_defence_enable': 'int',
        'region': 'str',
        'token_consume_enable': 'int'
    }

    attribute_map = {
        'allow_enable': 'AllowEnable',
        'block_enable': 'BlockEnable',
        'prompt_defence_enable': 'PromptDefenceEnable',
        'region': 'Region',
        'token_consume_enable': 'TokenConsumeEnable'
    }

    def __init__(self, allow_enable=None, block_enable=None, prompt_defence_enable=None, region=None, token_consume_enable=None, _configuration=None):  # noqa: E501
        """GetInstanceCtlResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_enable = None
        self._block_enable = None
        self._prompt_defence_enable = None
        self._region = None
        self._token_consume_enable = None
        self.discriminator = None

        if allow_enable is not None:
            self.allow_enable = allow_enable
        if block_enable is not None:
            self.block_enable = block_enable
        if prompt_defence_enable is not None:
            self.prompt_defence_enable = prompt_defence_enable
        if region is not None:
            self.region = region
        if token_consume_enable is not None:
            self.token_consume_enable = token_consume_enable

    @property
    def allow_enable(self):
        """Gets the allow_enable of this GetInstanceCtlResponse.  # noqa: E501


        :return: The allow_enable of this GetInstanceCtlResponse.  # noqa: E501
        :rtype: int
        """
        return self._allow_enable

    @allow_enable.setter
    def allow_enable(self, allow_enable):
        """Sets the allow_enable of this GetInstanceCtlResponse.


        :param allow_enable: The allow_enable of this GetInstanceCtlResponse.  # noqa: E501
        :type: int
        """

        self._allow_enable = allow_enable

    @property
    def block_enable(self):
        """Gets the block_enable of this GetInstanceCtlResponse.  # noqa: E501


        :return: The block_enable of this GetInstanceCtlResponse.  # noqa: E501
        :rtype: int
        """
        return self._block_enable

    @block_enable.setter
    def block_enable(self, block_enable):
        """Sets the block_enable of this GetInstanceCtlResponse.


        :param block_enable: The block_enable of this GetInstanceCtlResponse.  # noqa: E501
        :type: int
        """

        self._block_enable = block_enable

    @property
    def prompt_defence_enable(self):
        """Gets the prompt_defence_enable of this GetInstanceCtlResponse.  # noqa: E501


        :return: The prompt_defence_enable of this GetInstanceCtlResponse.  # noqa: E501
        :rtype: int
        """
        return self._prompt_defence_enable

    @prompt_defence_enable.setter
    def prompt_defence_enable(self, prompt_defence_enable):
        """Sets the prompt_defence_enable of this GetInstanceCtlResponse.


        :param prompt_defence_enable: The prompt_defence_enable of this GetInstanceCtlResponse.  # noqa: E501
        :type: int
        """

        self._prompt_defence_enable = prompt_defence_enable

    @property
    def region(self):
        """Gets the region of this GetInstanceCtlResponse.  # noqa: E501


        :return: The region of this GetInstanceCtlResponse.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this GetInstanceCtlResponse.


        :param region: The region of this GetInstanceCtlResponse.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def token_consume_enable(self):
        """Gets the token_consume_enable of this GetInstanceCtlResponse.  # noqa: E501


        :return: The token_consume_enable of this GetInstanceCtlResponse.  # noqa: E501
        :rtype: int
        """
        return self._token_consume_enable

    @token_consume_enable.setter
    def token_consume_enable(self, token_consume_enable):
        """Sets the token_consume_enable of this GetInstanceCtlResponse.


        :param token_consume_enable: The token_consume_enable of this GetInstanceCtlResponse.  # noqa: E501
        :type: int
        """

        self._token_consume_enable = token_consume_enable

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetInstanceCtlResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetInstanceCtlResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetInstanceCtlResponse):
            return True

        return self.to_dict() != other.to_dict()
