# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryAttackAnalysisTermsAggLbRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'analysis_key': 'str',
        'end_time': 'int',
        'host': 'str',
        'plugins': 'list[str]',
        'project_name': 'str',
        'start_time': 'int'
    }

    attribute_map = {
        'analysis_key': 'AnalysisKey',
        'end_time': 'EndTime',
        'host': 'Host',
        'plugins': 'Plugins',
        'project_name': 'ProjectName',
        'start_time': 'StartTime'
    }

    def __init__(self, analysis_key=None, end_time=None, host=None, plugins=None, project_name=None, start_time=None, _configuration=None):  # noqa: E501
        """QueryAttackAnalysisTermsAggLbRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._analysis_key = None
        self._end_time = None
        self._host = None
        self._plugins = None
        self._project_name = None
        self._start_time = None
        self.discriminator = None

        self.analysis_key = analysis_key
        self.end_time = end_time
        if host is not None:
            self.host = host
        if plugins is not None:
            self.plugins = plugins
        if project_name is not None:
            self.project_name = project_name
        self.start_time = start_time

    @property
    def analysis_key(self):
        """Gets the analysis_key of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501


        :return: The analysis_key of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501
        :rtype: str
        """
        return self._analysis_key

    @analysis_key.setter
    def analysis_key(self, analysis_key):
        """Sets the analysis_key of this QueryAttackAnalysisTermsAggLbRequest.


        :param analysis_key: The analysis_key of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and analysis_key is None:
            raise ValueError("Invalid value for `analysis_key`, must not be `None`")  # noqa: E501

        self._analysis_key = analysis_key

    @property
    def end_time(self):
        """Gets the end_time of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501


        :return: The end_time of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this QueryAttackAnalysisTermsAggLbRequest.


        :param end_time: The end_time of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and end_time is None:
            raise ValueError("Invalid value for `end_time`, must not be `None`")  # noqa: E501

        self._end_time = end_time

    @property
    def host(self):
        """Gets the host of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501


        :return: The host of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501
        :rtype: str
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this QueryAttackAnalysisTermsAggLbRequest.


        :param host: The host of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501
        :type: str
        """

        self._host = host

    @property
    def plugins(self):
        """Gets the plugins of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501


        :return: The plugins of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._plugins

    @plugins.setter
    def plugins(self, plugins):
        """Sets the plugins of this QueryAttackAnalysisTermsAggLbRequest.


        :param plugins: The plugins of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501
        :type: list[str]
        """

        self._plugins = plugins

    @property
    def project_name(self):
        """Gets the project_name of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501


        :return: The project_name of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this QueryAttackAnalysisTermsAggLbRequest.


        :param project_name: The project_name of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def start_time(self):
        """Gets the start_time of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501


        :return: The start_time of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this QueryAttackAnalysisTermsAggLbRequest.


        :param start_time: The start_time of this QueryAttackAnalysisTermsAggLbRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and start_time is None:
            raise ValueError("Invalid value for `start_time`, must not be `None`")  # noqa: E501

        self._start_time = start_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryAttackAnalysisTermsAggLbRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryAttackAnalysisTermsAggLbRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryAttackAnalysisTermsAggLbRequest):
            return True

        return self.to_dict() != other.to_dict()
