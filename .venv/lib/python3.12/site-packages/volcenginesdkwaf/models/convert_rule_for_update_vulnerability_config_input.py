# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConvertRuleForUpdateVulnerabilityConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action': 'int',
        'count_time': 'int',
        'effect_time': 'int',
        'effect_time_view_unit': 'str',
        'field': 'str',
        'hit_request_count': 'int'
    }

    attribute_map = {
        'action': 'Action',
        'count_time': 'CountTime',
        'effect_time': 'EffectTime',
        'effect_time_view_unit': 'EffectTimeViewUnit',
        'field': 'Field',
        'hit_request_count': 'HitRequestCount'
    }

    def __init__(self, action=None, count_time=None, effect_time=None, effect_time_view_unit=None, field=None, hit_request_count=None, _configuration=None):  # noqa: E501
        """ConvertRuleForUpdateVulnerabilityConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action = None
        self._count_time = None
        self._effect_time = None
        self._effect_time_view_unit = None
        self._field = None
        self._hit_request_count = None
        self.discriminator = None

        if action is not None:
            self.action = action
        if count_time is not None:
            self.count_time = count_time
        if effect_time is not None:
            self.effect_time = effect_time
        if effect_time_view_unit is not None:
            self.effect_time_view_unit = effect_time_view_unit
        if field is not None:
            self.field = field
        if hit_request_count is not None:
            self.hit_request_count = hit_request_count

    @property
    def action(self):
        """Gets the action of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501


        :return: The action of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this ConvertRuleForUpdateVulnerabilityConfigInput.


        :param action: The action of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501
        :type: int
        """

        self._action = action

    @property
    def count_time(self):
        """Gets the count_time of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501


        :return: The count_time of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._count_time

    @count_time.setter
    def count_time(self, count_time):
        """Sets the count_time of this ConvertRuleForUpdateVulnerabilityConfigInput.


        :param count_time: The count_time of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501
        :type: int
        """

        self._count_time = count_time

    @property
    def effect_time(self):
        """Gets the effect_time of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501


        :return: The effect_time of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._effect_time

    @effect_time.setter
    def effect_time(self, effect_time):
        """Sets the effect_time of this ConvertRuleForUpdateVulnerabilityConfigInput.


        :param effect_time: The effect_time of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501
        :type: int
        """

        self._effect_time = effect_time

    @property
    def effect_time_view_unit(self):
        """Gets the effect_time_view_unit of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501


        :return: The effect_time_view_unit of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._effect_time_view_unit

    @effect_time_view_unit.setter
    def effect_time_view_unit(self, effect_time_view_unit):
        """Sets the effect_time_view_unit of this ConvertRuleForUpdateVulnerabilityConfigInput.


        :param effect_time_view_unit: The effect_time_view_unit of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501
        :type: str
        """

        self._effect_time_view_unit = effect_time_view_unit

    @property
    def field(self):
        """Gets the field of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501


        :return: The field of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._field

    @field.setter
    def field(self, field):
        """Sets the field of this ConvertRuleForUpdateVulnerabilityConfigInput.


        :param field: The field of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501
        :type: str
        """

        self._field = field

    @property
    def hit_request_count(self):
        """Gets the hit_request_count of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501


        :return: The hit_request_count of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._hit_request_count

    @hit_request_count.setter
    def hit_request_count(self, hit_request_count):
        """Sets the hit_request_count of this ConvertRuleForUpdateVulnerabilityConfigInput.


        :param hit_request_count: The hit_request_count of this ConvertRuleForUpdateVulnerabilityConfigInput.  # noqa: E501
        :type: int
        """

        self._hit_request_count = hit_request_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConvertRuleForUpdateVulnerabilityConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConvertRuleForUpdateVulnerabilityConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConvertRuleForUpdateVulnerabilityConfigInput):
            return True

        return self.to_dict() != other.to_dict()
