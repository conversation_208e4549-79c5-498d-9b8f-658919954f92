# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryLLMGenerateRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'msg_id': 'str',
        'use_stream': 'bool'
    }

    attribute_map = {
        'msg_id': 'MsgID',
        'use_stream': 'UseStream'
    }

    def __init__(self, msg_id=None, use_stream=None, _configuration=None):  # noqa: E501
        """QueryLLMGenerateRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._msg_id = None
        self._use_stream = None
        self.discriminator = None

        self.msg_id = msg_id
        if use_stream is not None:
            self.use_stream = use_stream

    @property
    def msg_id(self):
        """Gets the msg_id of this QueryLLMGenerateRequest.  # noqa: E501


        :return: The msg_id of this QueryLLMGenerateRequest.  # noqa: E501
        :rtype: str
        """
        return self._msg_id

    @msg_id.setter
    def msg_id(self, msg_id):
        """Sets the msg_id of this QueryLLMGenerateRequest.


        :param msg_id: The msg_id of this QueryLLMGenerateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and msg_id is None:
            raise ValueError("Invalid value for `msg_id`, must not be `None`")  # noqa: E501

        self._msg_id = msg_id

    @property
    def use_stream(self):
        """Gets the use_stream of this QueryLLMGenerateRequest.  # noqa: E501


        :return: The use_stream of this QueryLLMGenerateRequest.  # noqa: E501
        :rtype: bool
        """
        return self._use_stream

    @use_stream.setter
    def use_stream(self, use_stream):
        """Sets the use_stream of this QueryLLMGenerateRequest.


        :param use_stream: The use_stream of this QueryLLMGenerateRequest.  # noqa: E501
        :type: bool
        """

        self._use_stream = use_stream

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryLLMGenerateRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryLLMGenerateRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryLLMGenerateRequest):
            return True

        return self.to_dict() != other.to_dict()
