# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetVulnerabilityConfigResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action': 'str',
        'advance_config': 'AdvanceConfigForGetVulnerabilityConfigOutput',
        'rule_mode': 'str',
        'rule_set_info': 'RuleSetInfoForGetVulnerabilityConfigOutput'
    }

    attribute_map = {
        'action': 'Action',
        'advance_config': 'AdvanceConfig',
        'rule_mode': 'RuleMode',
        'rule_set_info': 'RuleSetInfo'
    }

    def __init__(self, action=None, advance_config=None, rule_mode=None, rule_set_info=None, _configuration=None):  # noqa: E501
        """GetVulnerabilityConfigResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action = None
        self._advance_config = None
        self._rule_mode = None
        self._rule_set_info = None
        self.discriminator = None

        if action is not None:
            self.action = action
        if advance_config is not None:
            self.advance_config = advance_config
        if rule_mode is not None:
            self.rule_mode = rule_mode
        if rule_set_info is not None:
            self.rule_set_info = rule_set_info

    @property
    def action(self):
        """Gets the action of this GetVulnerabilityConfigResponse.  # noqa: E501


        :return: The action of this GetVulnerabilityConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this GetVulnerabilityConfigResponse.


        :param action: The action of this GetVulnerabilityConfigResponse.  # noqa: E501
        :type: str
        """

        self._action = action

    @property
    def advance_config(self):
        """Gets the advance_config of this GetVulnerabilityConfigResponse.  # noqa: E501


        :return: The advance_config of this GetVulnerabilityConfigResponse.  # noqa: E501
        :rtype: AdvanceConfigForGetVulnerabilityConfigOutput
        """
        return self._advance_config

    @advance_config.setter
    def advance_config(self, advance_config):
        """Sets the advance_config of this GetVulnerabilityConfigResponse.


        :param advance_config: The advance_config of this GetVulnerabilityConfigResponse.  # noqa: E501
        :type: AdvanceConfigForGetVulnerabilityConfigOutput
        """

        self._advance_config = advance_config

    @property
    def rule_mode(self):
        """Gets the rule_mode of this GetVulnerabilityConfigResponse.  # noqa: E501


        :return: The rule_mode of this GetVulnerabilityConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._rule_mode

    @rule_mode.setter
    def rule_mode(self, rule_mode):
        """Sets the rule_mode of this GetVulnerabilityConfigResponse.


        :param rule_mode: The rule_mode of this GetVulnerabilityConfigResponse.  # noqa: E501
        :type: str
        """

        self._rule_mode = rule_mode

    @property
    def rule_set_info(self):
        """Gets the rule_set_info of this GetVulnerabilityConfigResponse.  # noqa: E501


        :return: The rule_set_info of this GetVulnerabilityConfigResponse.  # noqa: E501
        :rtype: RuleSetInfoForGetVulnerabilityConfigOutput
        """
        return self._rule_set_info

    @rule_set_info.setter
    def rule_set_info(self, rule_set_info):
        """Sets the rule_set_info of this GetVulnerabilityConfigResponse.


        :param rule_set_info: The rule_set_info of this GetVulnerabilityConfigResponse.  # noqa: E501
        :type: RuleSetInfoForGetVulnerabilityConfigOutput
        """

        self._rule_set_info = rule_set_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetVulnerabilityConfigResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetVulnerabilityConfigResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetVulnerabilityConfigResponse):
            return True

        return self.to_dict() != other.to_dict()
