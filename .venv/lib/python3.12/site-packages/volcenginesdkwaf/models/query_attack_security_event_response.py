# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryAttackSecurityEventResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'attack_count': 'int',
        'count': 'int',
        'current_page': 'int',
        'event_end_time': 'int',
        'event_id': 'str',
        'event_start_time': 'int',
        'event_type': 'str',
        'host': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'security_level': 'str',
        'total_count': 'int'
    }

    attribute_map = {
        'attack_count': 'AttackCount',
        'count': 'Count',
        'current_page': 'CurrentPage',
        'event_end_time': 'EventEndTime',
        'event_id': 'EventId',
        'event_start_time': 'EventStartTime',
        'event_type': 'EventType',
        'host': 'Host',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'security_level': 'SecurityLevel',
        'total_count': 'TotalCount'
    }

    def __init__(self, attack_count=None, count=None, current_page=None, event_end_time=None, event_id=None, event_start_time=None, event_type=None, host=None, page_number=None, page_size=None, security_level=None, total_count=None, _configuration=None):  # noqa: E501
        """QueryAttackSecurityEventResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._attack_count = None
        self._count = None
        self._current_page = None
        self._event_end_time = None
        self._event_id = None
        self._event_start_time = None
        self._event_type = None
        self._host = None
        self._page_number = None
        self._page_size = None
        self._security_level = None
        self._total_count = None
        self.discriminator = None

        if attack_count is not None:
            self.attack_count = attack_count
        if count is not None:
            self.count = count
        if current_page is not None:
            self.current_page = current_page
        if event_end_time is not None:
            self.event_end_time = event_end_time
        if event_id is not None:
            self.event_id = event_id
        if event_start_time is not None:
            self.event_start_time = event_start_time
        if event_type is not None:
            self.event_type = event_type
        if host is not None:
            self.host = host
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if security_level is not None:
            self.security_level = security_level
        if total_count is not None:
            self.total_count = total_count

    @property
    def attack_count(self):
        """Gets the attack_count of this QueryAttackSecurityEventResponse.  # noqa: E501


        :return: The attack_count of this QueryAttackSecurityEventResponse.  # noqa: E501
        :rtype: int
        """
        return self._attack_count

    @attack_count.setter
    def attack_count(self, attack_count):
        """Sets the attack_count of this QueryAttackSecurityEventResponse.


        :param attack_count: The attack_count of this QueryAttackSecurityEventResponse.  # noqa: E501
        :type: int
        """

        self._attack_count = attack_count

    @property
    def count(self):
        """Gets the count of this QueryAttackSecurityEventResponse.  # noqa: E501


        :return: The count of this QueryAttackSecurityEventResponse.  # noqa: E501
        :rtype: int
        """
        return self._count

    @count.setter
    def count(self, count):
        """Sets the count of this QueryAttackSecurityEventResponse.


        :param count: The count of this QueryAttackSecurityEventResponse.  # noqa: E501
        :type: int
        """

        self._count = count

    @property
    def current_page(self):
        """Gets the current_page of this QueryAttackSecurityEventResponse.  # noqa: E501


        :return: The current_page of this QueryAttackSecurityEventResponse.  # noqa: E501
        :rtype: int
        """
        return self._current_page

    @current_page.setter
    def current_page(self, current_page):
        """Sets the current_page of this QueryAttackSecurityEventResponse.


        :param current_page: The current_page of this QueryAttackSecurityEventResponse.  # noqa: E501
        :type: int
        """

        self._current_page = current_page

    @property
    def event_end_time(self):
        """Gets the event_end_time of this QueryAttackSecurityEventResponse.  # noqa: E501


        :return: The event_end_time of this QueryAttackSecurityEventResponse.  # noqa: E501
        :rtype: int
        """
        return self._event_end_time

    @event_end_time.setter
    def event_end_time(self, event_end_time):
        """Sets the event_end_time of this QueryAttackSecurityEventResponse.


        :param event_end_time: The event_end_time of this QueryAttackSecurityEventResponse.  # noqa: E501
        :type: int
        """

        self._event_end_time = event_end_time

    @property
    def event_id(self):
        """Gets the event_id of this QueryAttackSecurityEventResponse.  # noqa: E501


        :return: The event_id of this QueryAttackSecurityEventResponse.  # noqa: E501
        :rtype: str
        """
        return self._event_id

    @event_id.setter
    def event_id(self, event_id):
        """Sets the event_id of this QueryAttackSecurityEventResponse.


        :param event_id: The event_id of this QueryAttackSecurityEventResponse.  # noqa: E501
        :type: str
        """

        self._event_id = event_id

    @property
    def event_start_time(self):
        """Gets the event_start_time of this QueryAttackSecurityEventResponse.  # noqa: E501


        :return: The event_start_time of this QueryAttackSecurityEventResponse.  # noqa: E501
        :rtype: int
        """
        return self._event_start_time

    @event_start_time.setter
    def event_start_time(self, event_start_time):
        """Sets the event_start_time of this QueryAttackSecurityEventResponse.


        :param event_start_time: The event_start_time of this QueryAttackSecurityEventResponse.  # noqa: E501
        :type: int
        """

        self._event_start_time = event_start_time

    @property
    def event_type(self):
        """Gets the event_type of this QueryAttackSecurityEventResponse.  # noqa: E501


        :return: The event_type of this QueryAttackSecurityEventResponse.  # noqa: E501
        :rtype: str
        """
        return self._event_type

    @event_type.setter
    def event_type(self, event_type):
        """Sets the event_type of this QueryAttackSecurityEventResponse.


        :param event_type: The event_type of this QueryAttackSecurityEventResponse.  # noqa: E501
        :type: str
        """

        self._event_type = event_type

    @property
    def host(self):
        """Gets the host of this QueryAttackSecurityEventResponse.  # noqa: E501


        :return: The host of this QueryAttackSecurityEventResponse.  # noqa: E501
        :rtype: str
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this QueryAttackSecurityEventResponse.


        :param host: The host of this QueryAttackSecurityEventResponse.  # noqa: E501
        :type: str
        """

        self._host = host

    @property
    def page_number(self):
        """Gets the page_number of this QueryAttackSecurityEventResponse.  # noqa: E501


        :return: The page_number of this QueryAttackSecurityEventResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this QueryAttackSecurityEventResponse.


        :param page_number: The page_number of this QueryAttackSecurityEventResponse.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this QueryAttackSecurityEventResponse.  # noqa: E501


        :return: The page_size of this QueryAttackSecurityEventResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this QueryAttackSecurityEventResponse.


        :param page_size: The page_size of this QueryAttackSecurityEventResponse.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def security_level(self):
        """Gets the security_level of this QueryAttackSecurityEventResponse.  # noqa: E501


        :return: The security_level of this QueryAttackSecurityEventResponse.  # noqa: E501
        :rtype: str
        """
        return self._security_level

    @security_level.setter
    def security_level(self, security_level):
        """Sets the security_level of this QueryAttackSecurityEventResponse.


        :param security_level: The security_level of this QueryAttackSecurityEventResponse.  # noqa: E501
        :type: str
        """

        self._security_level = security_level

    @property
    def total_count(self):
        """Gets the total_count of this QueryAttackSecurityEventResponse.  # noqa: E501


        :return: The total_count of this QueryAttackSecurityEventResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this QueryAttackSecurityEventResponse.


        :param total_count: The total_count of this QueryAttackSecurityEventResponse.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryAttackSecurityEventResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryAttackSecurityEventResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryAttackSecurityEventResponse):
            return True

        return self.to_dict() != other.to_dict()
