# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListBotAnalyseProtectRuleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bot_space': 'str',
        'host': 'str',
        'name': 'str',
        'page': 'int',
        'page_size': 'int',
        'path': 'str',
        'project_name': 'str',
        'region': 'str',
        'rule_tag': 'str'
    }

    attribute_map = {
        'bot_space': 'BotSpace',
        'host': 'Host',
        'name': 'Name',
        'page': 'Page',
        'page_size': 'PageSize',
        'path': 'Path',
        'project_name': 'ProjectName',
        'region': 'Region',
        'rule_tag': 'RuleTag'
    }

    def __init__(self, bot_space=None, host=None, name=None, page=None, page_size=None, path=None, project_name=None, region=None, rule_tag=None, _configuration=None):  # noqa: E501
        """ListBotAnalyseProtectRuleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bot_space = None
        self._host = None
        self._name = None
        self._page = None
        self._page_size = None
        self._path = None
        self._project_name = None
        self._region = None
        self._rule_tag = None
        self.discriminator = None

        self.bot_space = bot_space
        self.host = host
        if name is not None:
            self.name = name
        if page is not None:
            self.page = page
        if page_size is not None:
            self.page_size = page_size
        if path is not None:
            self.path = path
        if project_name is not None:
            self.project_name = project_name
        self.region = region
        if rule_tag is not None:
            self.rule_tag = rule_tag

    @property
    def bot_space(self):
        """Gets the bot_space of this ListBotAnalyseProtectRuleRequest.  # noqa: E501


        :return: The bot_space of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._bot_space

    @bot_space.setter
    def bot_space(self, bot_space):
        """Sets the bot_space of this ListBotAnalyseProtectRuleRequest.


        :param bot_space: The bot_space of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and bot_space is None:
            raise ValueError("Invalid value for `bot_space`, must not be `None`")  # noqa: E501

        self._bot_space = bot_space

    @property
    def host(self):
        """Gets the host of this ListBotAnalyseProtectRuleRequest.  # noqa: E501


        :return: The host of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this ListBotAnalyseProtectRuleRequest.


        :param host: The host of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and host is None:
            raise ValueError("Invalid value for `host`, must not be `None`")  # noqa: E501

        self._host = host

    @property
    def name(self):
        """Gets the name of this ListBotAnalyseProtectRuleRequest.  # noqa: E501


        :return: The name of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListBotAnalyseProtectRuleRequest.


        :param name: The name of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def page(self):
        """Gets the page of this ListBotAnalyseProtectRuleRequest.  # noqa: E501


        :return: The page of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._page

    @page.setter
    def page(self, page):
        """Sets the page of this ListBotAnalyseProtectRuleRequest.


        :param page: The page of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :type: int
        """

        self._page = page

    @property
    def page_size(self):
        """Gets the page_size of this ListBotAnalyseProtectRuleRequest.  # noqa: E501


        :return: The page_size of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListBotAnalyseProtectRuleRequest.


        :param page_size: The page_size of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def path(self):
        """Gets the path of this ListBotAnalyseProtectRuleRequest.  # noqa: E501


        :return: The path of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this ListBotAnalyseProtectRuleRequest.


        :param path: The path of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :type: str
        """

        self._path = path

    @property
    def project_name(self):
        """Gets the project_name of this ListBotAnalyseProtectRuleRequest.  # noqa: E501


        :return: The project_name of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListBotAnalyseProtectRuleRequest.


        :param project_name: The project_name of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def region(self):
        """Gets the region of this ListBotAnalyseProtectRuleRequest.  # noqa: E501


        :return: The region of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this ListBotAnalyseProtectRuleRequest.


        :param region: The region of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and region is None:
            raise ValueError("Invalid value for `region`, must not be `None`")  # noqa: E501

        self._region = region

    @property
    def rule_tag(self):
        """Gets the rule_tag of this ListBotAnalyseProtectRuleRequest.  # noqa: E501


        :return: The rule_tag of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._rule_tag

    @rule_tag.setter
    def rule_tag(self, rule_tag):
        """Sets the rule_tag of this ListBotAnalyseProtectRuleRequest.


        :param rule_tag: The rule_tag of this ListBotAnalyseProtectRuleRequest.  # noqa: E501
        :type: str
        """

        self._rule_tag = rule_tag

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListBotAnalyseProtectRuleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListBotAnalyseProtectRuleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListBotAnalyseProtectRuleRequest):
            return True

        return self.to_dict() != other.to_dict()
