# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class IpGroupListForListAllIpGroupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ip_count': 'int',
        'ip_group_id': 'int',
        'name': 'str',
        'related_rules': 'list[RelatedRuleForListAllIpGroupsOutput]',
        'update_time': 'str'
    }

    attribute_map = {
        'ip_count': 'IpCount',
        'ip_group_id': 'IpGroupId',
        'name': 'Name',
        'related_rules': 'RelatedRules',
        'update_time': 'UpdateTime'
    }

    def __init__(self, ip_count=None, ip_group_id=None, name=None, related_rules=None, update_time=None, _configuration=None):  # noqa: E501
        """IpGroupListForListAllIpGroupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ip_count = None
        self._ip_group_id = None
        self._name = None
        self._related_rules = None
        self._update_time = None
        self.discriminator = None

        if ip_count is not None:
            self.ip_count = ip_count
        if ip_group_id is not None:
            self.ip_group_id = ip_group_id
        if name is not None:
            self.name = name
        if related_rules is not None:
            self.related_rules = related_rules
        if update_time is not None:
            self.update_time = update_time

    @property
    def ip_count(self):
        """Gets the ip_count of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501


        :return: The ip_count of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501
        :rtype: int
        """
        return self._ip_count

    @ip_count.setter
    def ip_count(self, ip_count):
        """Sets the ip_count of this IpGroupListForListAllIpGroupsOutput.


        :param ip_count: The ip_count of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501
        :type: int
        """

        self._ip_count = ip_count

    @property
    def ip_group_id(self):
        """Gets the ip_group_id of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501


        :return: The ip_group_id of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501
        :rtype: int
        """
        return self._ip_group_id

    @ip_group_id.setter
    def ip_group_id(self, ip_group_id):
        """Sets the ip_group_id of this IpGroupListForListAllIpGroupsOutput.


        :param ip_group_id: The ip_group_id of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501
        :type: int
        """

        self._ip_group_id = ip_group_id

    @property
    def name(self):
        """Gets the name of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501


        :return: The name of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this IpGroupListForListAllIpGroupsOutput.


        :param name: The name of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def related_rules(self):
        """Gets the related_rules of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501


        :return: The related_rules of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501
        :rtype: list[RelatedRuleForListAllIpGroupsOutput]
        """
        return self._related_rules

    @related_rules.setter
    def related_rules(self, related_rules):
        """Sets the related_rules of this IpGroupListForListAllIpGroupsOutput.


        :param related_rules: The related_rules of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501
        :type: list[RelatedRuleForListAllIpGroupsOutput]
        """

        self._related_rules = related_rules

    @property
    def update_time(self):
        """Gets the update_time of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501


        :return: The update_time of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this IpGroupListForListAllIpGroupsOutput.


        :param update_time: The update_time of this IpGroupListForListAllIpGroupsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(IpGroupListForListAllIpGroupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, IpGroupListForListAllIpGroupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, IpGroupListForListAllIpGroupsOutput):
            return True

        return self.to_dict() != other.to_dict()
