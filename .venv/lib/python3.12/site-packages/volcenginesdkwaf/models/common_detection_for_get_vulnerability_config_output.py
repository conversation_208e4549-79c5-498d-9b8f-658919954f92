# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CommonDetectionForGetVulnerabilityConfigOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'enable_rule_count': 'int',
        'rule_set_detail': 'list[RuleSetDetailForGetVulnerabilityConfigOutput]',
        'rule_set_name': 'str',
        'total_rule_count': 'int'
    }

    attribute_map = {
        'description': 'Description',
        'enable_rule_count': 'EnableRuleCount',
        'rule_set_detail': 'RuleSetDetail',
        'rule_set_name': 'RuleSetName',
        'total_rule_count': 'TotalRuleCount'
    }

    def __init__(self, description=None, enable_rule_count=None, rule_set_detail=None, rule_set_name=None, total_rule_count=None, _configuration=None):  # noqa: E501
        """CommonDetectionForGetVulnerabilityConfigOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._enable_rule_count = None
        self._rule_set_detail = None
        self._rule_set_name = None
        self._total_rule_count = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if enable_rule_count is not None:
            self.enable_rule_count = enable_rule_count
        if rule_set_detail is not None:
            self.rule_set_detail = rule_set_detail
        if rule_set_name is not None:
            self.rule_set_name = rule_set_name
        if total_rule_count is not None:
            self.total_rule_count = total_rule_count

    @property
    def description(self):
        """Gets the description of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501


        :return: The description of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CommonDetectionForGetVulnerabilityConfigOutput.


        :param description: The description of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def enable_rule_count(self):
        """Gets the enable_rule_count of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501


        :return: The enable_rule_count of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501
        :rtype: int
        """
        return self._enable_rule_count

    @enable_rule_count.setter
    def enable_rule_count(self, enable_rule_count):
        """Sets the enable_rule_count of this CommonDetectionForGetVulnerabilityConfigOutput.


        :param enable_rule_count: The enable_rule_count of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501
        :type: int
        """

        self._enable_rule_count = enable_rule_count

    @property
    def rule_set_detail(self):
        """Gets the rule_set_detail of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501


        :return: The rule_set_detail of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501
        :rtype: list[RuleSetDetailForGetVulnerabilityConfigOutput]
        """
        return self._rule_set_detail

    @rule_set_detail.setter
    def rule_set_detail(self, rule_set_detail):
        """Sets the rule_set_detail of this CommonDetectionForGetVulnerabilityConfigOutput.


        :param rule_set_detail: The rule_set_detail of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501
        :type: list[RuleSetDetailForGetVulnerabilityConfigOutput]
        """

        self._rule_set_detail = rule_set_detail

    @property
    def rule_set_name(self):
        """Gets the rule_set_name of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501


        :return: The rule_set_name of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_set_name

    @rule_set_name.setter
    def rule_set_name(self, rule_set_name):
        """Sets the rule_set_name of this CommonDetectionForGetVulnerabilityConfigOutput.


        :param rule_set_name: The rule_set_name of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501
        :type: str
        """

        self._rule_set_name = rule_set_name

    @property
    def total_rule_count(self):
        """Gets the total_rule_count of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501


        :return: The total_rule_count of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_rule_count

    @total_rule_count.setter
    def total_rule_count(self, total_rule_count):
        """Sets the total_rule_count of this CommonDetectionForGetVulnerabilityConfigOutput.


        :param total_rule_count: The total_rule_count of this CommonDetectionForGetVulnerabilityConfigOutput.  # noqa: E501
        :type: int
        """

        self._total_rule_count = total_rule_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CommonDetectionForGetVulnerabilityConfigOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CommonDetectionForGetVulnerabilityConfigOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CommonDetectionForGetVulnerabilityConfigOutput):
            return True

        return self.to_dict() != other.to_dict()
