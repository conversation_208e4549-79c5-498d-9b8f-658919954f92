# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BackendGroupForListDomainOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_port': 'list[int]',
        'backends': 'list[BackendForListDomainOutput]',
        'name': 'str'
    }

    attribute_map = {
        'access_port': 'AccessPort',
        'backends': 'Backends',
        'name': 'Name'
    }

    def __init__(self, access_port=None, backends=None, name=None, _configuration=None):  # noqa: E501
        """BackendGroupForListDomainOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_port = None
        self._backends = None
        self._name = None
        self.discriminator = None

        if access_port is not None:
            self.access_port = access_port
        if backends is not None:
            self.backends = backends
        if name is not None:
            self.name = name

    @property
    def access_port(self):
        """Gets the access_port of this BackendGroupForListDomainOutput.  # noqa: E501


        :return: The access_port of this BackendGroupForListDomainOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._access_port

    @access_port.setter
    def access_port(self, access_port):
        """Sets the access_port of this BackendGroupForListDomainOutput.


        :param access_port: The access_port of this BackendGroupForListDomainOutput.  # noqa: E501
        :type: list[int]
        """

        self._access_port = access_port

    @property
    def backends(self):
        """Gets the backends of this BackendGroupForListDomainOutput.  # noqa: E501


        :return: The backends of this BackendGroupForListDomainOutput.  # noqa: E501
        :rtype: list[BackendForListDomainOutput]
        """
        return self._backends

    @backends.setter
    def backends(self, backends):
        """Sets the backends of this BackendGroupForListDomainOutput.


        :param backends: The backends of this BackendGroupForListDomainOutput.  # noqa: E501
        :type: list[BackendForListDomainOutput]
        """

        self._backends = backends

    @property
    def name(self):
        """Gets the name of this BackendGroupForListDomainOutput.  # noqa: E501


        :return: The name of this BackendGroupForListDomainOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this BackendGroupForListDomainOutput.


        :param name: The name of this BackendGroupForListDomainOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BackendGroupForListDomainOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackendGroupForListDomainOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackendGroupForListDomainOutput):
            return True

        return self.to_dict() != other.to_dict()
