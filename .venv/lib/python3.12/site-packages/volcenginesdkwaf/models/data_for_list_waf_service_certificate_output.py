# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListWafServiceCertificateOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'applicable_domains': 'str',
        'clb_certificate_id': 'str',
        'description': 'str',
        'expire_time': 'str',
        'id': 'int',
        'insert_time': 'str',
        'isolation_id': 'str',
        'name': 'str',
        'operator': 'str',
        'optsrc': 'str',
        'private_key': 'str',
        'public_key': 'str',
        'user': 'str',
        'volc_certificate_id': 'str'
    }

    attribute_map = {
        'applicable_domains': 'ApplicableDomains',
        'clb_certificate_id': 'ClbCertificateId',
        'description': 'Description',
        'expire_time': 'ExpireTime',
        'id': 'Id',
        'insert_time': 'InsertTime',
        'isolation_id': 'IsolationId',
        'name': 'Name',
        'operator': 'Operator',
        'optsrc': 'Optsrc',
        'private_key': 'PrivateKey',
        'public_key': 'PublicKey',
        'user': 'User',
        'volc_certificate_id': 'VolcCertificateId'
    }

    def __init__(self, applicable_domains=None, clb_certificate_id=None, description=None, expire_time=None, id=None, insert_time=None, isolation_id=None, name=None, operator=None, optsrc=None, private_key=None, public_key=None, user=None, volc_certificate_id=None, _configuration=None):  # noqa: E501
        """DataForListWafServiceCertificateOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._applicable_domains = None
        self._clb_certificate_id = None
        self._description = None
        self._expire_time = None
        self._id = None
        self._insert_time = None
        self._isolation_id = None
        self._name = None
        self._operator = None
        self._optsrc = None
        self._private_key = None
        self._public_key = None
        self._user = None
        self._volc_certificate_id = None
        self.discriminator = None

        if applicable_domains is not None:
            self.applicable_domains = applicable_domains
        if clb_certificate_id is not None:
            self.clb_certificate_id = clb_certificate_id
        if description is not None:
            self.description = description
        if expire_time is not None:
            self.expire_time = expire_time
        if id is not None:
            self.id = id
        if insert_time is not None:
            self.insert_time = insert_time
        if isolation_id is not None:
            self.isolation_id = isolation_id
        if name is not None:
            self.name = name
        if operator is not None:
            self.operator = operator
        if optsrc is not None:
            self.optsrc = optsrc
        if private_key is not None:
            self.private_key = private_key
        if public_key is not None:
            self.public_key = public_key
        if user is not None:
            self.user = user
        if volc_certificate_id is not None:
            self.volc_certificate_id = volc_certificate_id

    @property
    def applicable_domains(self):
        """Gets the applicable_domains of this DataForListWafServiceCertificateOutput.  # noqa: E501


        :return: The applicable_domains of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._applicable_domains

    @applicable_domains.setter
    def applicable_domains(self, applicable_domains):
        """Sets the applicable_domains of this DataForListWafServiceCertificateOutput.


        :param applicable_domains: The applicable_domains of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :type: str
        """

        self._applicable_domains = applicable_domains

    @property
    def clb_certificate_id(self):
        """Gets the clb_certificate_id of this DataForListWafServiceCertificateOutput.  # noqa: E501


        :return: The clb_certificate_id of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._clb_certificate_id

    @clb_certificate_id.setter
    def clb_certificate_id(self, clb_certificate_id):
        """Sets the clb_certificate_id of this DataForListWafServiceCertificateOutput.


        :param clb_certificate_id: The clb_certificate_id of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :type: str
        """

        self._clb_certificate_id = clb_certificate_id

    @property
    def description(self):
        """Gets the description of this DataForListWafServiceCertificateOutput.  # noqa: E501


        :return: The description of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DataForListWafServiceCertificateOutput.


        :param description: The description of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def expire_time(self):
        """Gets the expire_time of this DataForListWafServiceCertificateOutput.  # noqa: E501


        :return: The expire_time of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this DataForListWafServiceCertificateOutput.


        :param expire_time: The expire_time of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :type: str
        """

        self._expire_time = expire_time

    @property
    def id(self):
        """Gets the id of this DataForListWafServiceCertificateOutput.  # noqa: E501


        :return: The id of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListWafServiceCertificateOutput.


        :param id: The id of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def insert_time(self):
        """Gets the insert_time of this DataForListWafServiceCertificateOutput.  # noqa: E501


        :return: The insert_time of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._insert_time

    @insert_time.setter
    def insert_time(self, insert_time):
        """Sets the insert_time of this DataForListWafServiceCertificateOutput.


        :param insert_time: The insert_time of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :type: str
        """

        self._insert_time = insert_time

    @property
    def isolation_id(self):
        """Gets the isolation_id of this DataForListWafServiceCertificateOutput.  # noqa: E501


        :return: The isolation_id of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._isolation_id

    @isolation_id.setter
    def isolation_id(self, isolation_id):
        """Sets the isolation_id of this DataForListWafServiceCertificateOutput.


        :param isolation_id: The isolation_id of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :type: str
        """

        self._isolation_id = isolation_id

    @property
    def name(self):
        """Gets the name of this DataForListWafServiceCertificateOutput.  # noqa: E501


        :return: The name of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForListWafServiceCertificateOutput.


        :param name: The name of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def operator(self):
        """Gets the operator of this DataForListWafServiceCertificateOutput.  # noqa: E501


        :return: The operator of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._operator

    @operator.setter
    def operator(self, operator):
        """Sets the operator of this DataForListWafServiceCertificateOutput.


        :param operator: The operator of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :type: str
        """

        self._operator = operator

    @property
    def optsrc(self):
        """Gets the optsrc of this DataForListWafServiceCertificateOutput.  # noqa: E501


        :return: The optsrc of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._optsrc

    @optsrc.setter
    def optsrc(self, optsrc):
        """Sets the optsrc of this DataForListWafServiceCertificateOutput.


        :param optsrc: The optsrc of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :type: str
        """

        self._optsrc = optsrc

    @property
    def private_key(self):
        """Gets the private_key of this DataForListWafServiceCertificateOutput.  # noqa: E501


        :return: The private_key of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_key

    @private_key.setter
    def private_key(self, private_key):
        """Sets the private_key of this DataForListWafServiceCertificateOutput.


        :param private_key: The private_key of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :type: str
        """

        self._private_key = private_key

    @property
    def public_key(self):
        """Gets the public_key of this DataForListWafServiceCertificateOutput.  # noqa: E501


        :return: The public_key of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_key

    @public_key.setter
    def public_key(self, public_key):
        """Sets the public_key of this DataForListWafServiceCertificateOutput.


        :param public_key: The public_key of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :type: str
        """

        self._public_key = public_key

    @property
    def user(self):
        """Gets the user of this DataForListWafServiceCertificateOutput.  # noqa: E501


        :return: The user of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._user

    @user.setter
    def user(self, user):
        """Sets the user of this DataForListWafServiceCertificateOutput.


        :param user: The user of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :type: str
        """

        self._user = user

    @property
    def volc_certificate_id(self):
        """Gets the volc_certificate_id of this DataForListWafServiceCertificateOutput.  # noqa: E501


        :return: The volc_certificate_id of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._volc_certificate_id

    @volc_certificate_id.setter
    def volc_certificate_id(self, volc_certificate_id):
        """Sets the volc_certificate_id of this DataForListWafServiceCertificateOutput.


        :param volc_certificate_id: The volc_certificate_id of this DataForListWafServiceCertificateOutput.  # noqa: E501
        :type: str
        """

        self._volc_certificate_id = volc_certificate_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListWafServiceCertificateOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListWafServiceCertificateOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListWafServiceCertificateOutput):
            return True

        return self.to_dict() != other.to_dict()
