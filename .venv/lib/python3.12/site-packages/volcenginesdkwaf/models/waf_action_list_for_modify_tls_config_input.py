# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class WafActionListForModifyTLSConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'answer_opt': 'bool',
        'block': 'bool',
        'drop': 'bool',
        'human_verify': 'bool',
        'human_verify_block': 'bool',
        'js': 'bool',
        'js_block': 'bool',
        'observe': 'bool',
        '_pass': 'bool',
        'permit': 'bool',
        'pow': 'bool',
        'pow_block': 'bool'
    }

    attribute_map = {
        'answer_opt': 'AnswerOpt',
        'block': 'Block',
        'drop': 'Drop',
        'human_verify': 'HumanVerify',
        'human_verify_block': 'HumanVerifyBlock',
        'js': 'JS',
        'js_block': 'JSBlock',
        'observe': 'Observe',
        '_pass': 'Pass',
        'permit': 'Permit',
        'pow': 'Pow',
        'pow_block': 'PowBlock'
    }

    def __init__(self, answer_opt=None, block=None, drop=None, human_verify=None, human_verify_block=None, js=None, js_block=None, observe=None, _pass=None, permit=None, pow=None, pow_block=None, _configuration=None):  # noqa: E501
        """WafActionListForModifyTLSConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._answer_opt = None
        self._block = None
        self._drop = None
        self._human_verify = None
        self._human_verify_block = None
        self._js = None
        self._js_block = None
        self._observe = None
        self.__pass = None
        self._permit = None
        self._pow = None
        self._pow_block = None
        self.discriminator = None

        if answer_opt is not None:
            self.answer_opt = answer_opt
        if block is not None:
            self.block = block
        if drop is not None:
            self.drop = drop
        if human_verify is not None:
            self.human_verify = human_verify
        if human_verify_block is not None:
            self.human_verify_block = human_verify_block
        if js is not None:
            self.js = js
        if js_block is not None:
            self.js_block = js_block
        if observe is not None:
            self.observe = observe
        if _pass is not None:
            self._pass = _pass
        if permit is not None:
            self.permit = permit
        if pow is not None:
            self.pow = pow
        if pow_block is not None:
            self.pow_block = pow_block

    @property
    def answer_opt(self):
        """Gets the answer_opt of this WafActionListForModifyTLSConfigInput.  # noqa: E501


        :return: The answer_opt of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._answer_opt

    @answer_opt.setter
    def answer_opt(self, answer_opt):
        """Sets the answer_opt of this WafActionListForModifyTLSConfigInput.


        :param answer_opt: The answer_opt of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :type: bool
        """

        self._answer_opt = answer_opt

    @property
    def block(self):
        """Gets the block of this WafActionListForModifyTLSConfigInput.  # noqa: E501


        :return: The block of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._block

    @block.setter
    def block(self, block):
        """Sets the block of this WafActionListForModifyTLSConfigInput.


        :param block: The block of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :type: bool
        """

        self._block = block

    @property
    def drop(self):
        """Gets the drop of this WafActionListForModifyTLSConfigInput.  # noqa: E501


        :return: The drop of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._drop

    @drop.setter
    def drop(self, drop):
        """Sets the drop of this WafActionListForModifyTLSConfigInput.


        :param drop: The drop of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :type: bool
        """

        self._drop = drop

    @property
    def human_verify(self):
        """Gets the human_verify of this WafActionListForModifyTLSConfigInput.  # noqa: E501


        :return: The human_verify of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._human_verify

    @human_verify.setter
    def human_verify(self, human_verify):
        """Sets the human_verify of this WafActionListForModifyTLSConfigInput.


        :param human_verify: The human_verify of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :type: bool
        """

        self._human_verify = human_verify

    @property
    def human_verify_block(self):
        """Gets the human_verify_block of this WafActionListForModifyTLSConfigInput.  # noqa: E501


        :return: The human_verify_block of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._human_verify_block

    @human_verify_block.setter
    def human_verify_block(self, human_verify_block):
        """Sets the human_verify_block of this WafActionListForModifyTLSConfigInput.


        :param human_verify_block: The human_verify_block of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :type: bool
        """

        self._human_verify_block = human_verify_block

    @property
    def js(self):
        """Gets the js of this WafActionListForModifyTLSConfigInput.  # noqa: E501


        :return: The js of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._js

    @js.setter
    def js(self, js):
        """Sets the js of this WafActionListForModifyTLSConfigInput.


        :param js: The js of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :type: bool
        """

        self._js = js

    @property
    def js_block(self):
        """Gets the js_block of this WafActionListForModifyTLSConfigInput.  # noqa: E501


        :return: The js_block of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._js_block

    @js_block.setter
    def js_block(self, js_block):
        """Sets the js_block of this WafActionListForModifyTLSConfigInput.


        :param js_block: The js_block of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :type: bool
        """

        self._js_block = js_block

    @property
    def observe(self):
        """Gets the observe of this WafActionListForModifyTLSConfigInput.  # noqa: E501


        :return: The observe of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._observe

    @observe.setter
    def observe(self, observe):
        """Sets the observe of this WafActionListForModifyTLSConfigInput.


        :param observe: The observe of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :type: bool
        """

        self._observe = observe

    @property
    def _pass(self):
        """Gets the _pass of this WafActionListForModifyTLSConfigInput.  # noqa: E501


        :return: The _pass of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self.__pass

    @_pass.setter
    def _pass(self, _pass):
        """Sets the _pass of this WafActionListForModifyTLSConfigInput.


        :param _pass: The _pass of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :type: bool
        """

        self.__pass = _pass

    @property
    def permit(self):
        """Gets the permit of this WafActionListForModifyTLSConfigInput.  # noqa: E501


        :return: The permit of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._permit

    @permit.setter
    def permit(self, permit):
        """Sets the permit of this WafActionListForModifyTLSConfigInput.


        :param permit: The permit of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :type: bool
        """

        self._permit = permit

    @property
    def pow(self):
        """Gets the pow of this WafActionListForModifyTLSConfigInput.  # noqa: E501


        :return: The pow of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._pow

    @pow.setter
    def pow(self, pow):
        """Sets the pow of this WafActionListForModifyTLSConfigInput.


        :param pow: The pow of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :type: bool
        """

        self._pow = pow

    @property
    def pow_block(self):
        """Gets the pow_block of this WafActionListForModifyTLSConfigInput.  # noqa: E501


        :return: The pow_block of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._pow_block

    @pow_block.setter
    def pow_block(self, pow_block):
        """Sets the pow_block of this WafActionListForModifyTLSConfigInput.


        :param pow_block: The pow_block of this WafActionListForModifyTLSConfigInput.  # noqa: E501
        :type: bool
        """

        self._pow_block = pow_block

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(WafActionListForModifyTLSConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, WafActionListForModifyTLSConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, WafActionListForModifyTLSConfigInput):
            return True

        return self.to_dict() != other.to_dict()
