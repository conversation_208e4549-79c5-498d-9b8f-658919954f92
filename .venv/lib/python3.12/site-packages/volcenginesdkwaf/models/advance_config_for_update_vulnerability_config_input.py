# coding: utf-8

"""
    waf

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AdvanceConfigForUpdateVulnerabilityConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_traversal': 'AutoTraversalForUpdateVulnerabilityConfigInput',
        'freq_scan': 'FreqScanForUpdateVulnerabilityConfigInput'
    }

    attribute_map = {
        'auto_traversal': 'AutoTraversal',
        'freq_scan': 'FreqScan'
    }

    def __init__(self, auto_traversal=None, freq_scan=None, _configuration=None):  # noqa: E501
        """AdvanceConfigForUpdateVulnerabilityConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_traversal = None
        self._freq_scan = None
        self.discriminator = None

        if auto_traversal is not None:
            self.auto_traversal = auto_traversal
        if freq_scan is not None:
            self.freq_scan = freq_scan

    @property
    def auto_traversal(self):
        """Gets the auto_traversal of this AdvanceConfigForUpdateVulnerabilityConfigInput.  # noqa: E501


        :return: The auto_traversal of this AdvanceConfigForUpdateVulnerabilityConfigInput.  # noqa: E501
        :rtype: AutoTraversalForUpdateVulnerabilityConfigInput
        """
        return self._auto_traversal

    @auto_traversal.setter
    def auto_traversal(self, auto_traversal):
        """Sets the auto_traversal of this AdvanceConfigForUpdateVulnerabilityConfigInput.


        :param auto_traversal: The auto_traversal of this AdvanceConfigForUpdateVulnerabilityConfigInput.  # noqa: E501
        :type: AutoTraversalForUpdateVulnerabilityConfigInput
        """

        self._auto_traversal = auto_traversal

    @property
    def freq_scan(self):
        """Gets the freq_scan of this AdvanceConfigForUpdateVulnerabilityConfigInput.  # noqa: E501


        :return: The freq_scan of this AdvanceConfigForUpdateVulnerabilityConfigInput.  # noqa: E501
        :rtype: FreqScanForUpdateVulnerabilityConfigInput
        """
        return self._freq_scan

    @freq_scan.setter
    def freq_scan(self, freq_scan):
        """Sets the freq_scan of this AdvanceConfigForUpdateVulnerabilityConfigInput.


        :param freq_scan: The freq_scan of this AdvanceConfigForUpdateVulnerabilityConfigInput.  # noqa: E501
        :type: FreqScanForUpdateVulnerabilityConfigInput
        """

        self._freq_scan = freq_scan

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AdvanceConfigForUpdateVulnerabilityConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AdvanceConfigForUpdateVulnerabilityConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AdvanceConfigForUpdateVulnerabilityConfigInput):
            return True

        return self.to_dict() != other.to_dict()
