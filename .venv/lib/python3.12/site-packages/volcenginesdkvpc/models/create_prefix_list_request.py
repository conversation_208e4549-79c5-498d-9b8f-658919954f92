# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreatePrefixListRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'description': 'str',
        'dry_run': 'bool',
        'ip_version': 'str',
        'max_entries': 'int',
        'prefix_list_entries': 'list[PrefixListEntryForCreatePrefixListInput]',
        'prefix_list_name': 'str',
        'project_name': 'str',
        'tags': 'list[TagForCreatePrefixListInput]'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'description': 'Description',
        'dry_run': 'DryRun',
        'ip_version': 'IpVersion',
        'max_entries': 'MaxEntries',
        'prefix_list_entries': 'PrefixListEntries',
        'prefix_list_name': 'PrefixListName',
        'project_name': 'ProjectName',
        'tags': 'Tags'
    }

    def __init__(self, client_token=None, description=None, dry_run=None, ip_version=None, max_entries=None, prefix_list_entries=None, prefix_list_name=None, project_name=None, tags=None, _configuration=None):  # noqa: E501
        """CreatePrefixListRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._description = None
        self._dry_run = None
        self._ip_version = None
        self._max_entries = None
        self._prefix_list_entries = None
        self._prefix_list_name = None
        self._project_name = None
        self._tags = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        if dry_run is not None:
            self.dry_run = dry_run
        if ip_version is not None:
            self.ip_version = ip_version
        self.max_entries = max_entries
        if prefix_list_entries is not None:
            self.prefix_list_entries = prefix_list_entries
        if prefix_list_name is not None:
            self.prefix_list_name = prefix_list_name
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags

    @property
    def client_token(self):
        """Gets the client_token of this CreatePrefixListRequest.  # noqa: E501


        :return: The client_token of this CreatePrefixListRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreatePrefixListRequest.


        :param client_token: The client_token of this CreatePrefixListRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreatePrefixListRequest.  # noqa: E501


        :return: The description of this CreatePrefixListRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreatePrefixListRequest.


        :param description: The description of this CreatePrefixListRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 255):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `255`")  # noqa: E501
        if (self._configuration.client_side_validation and
                description is not None and len(description) < 1):
            raise ValueError("Invalid value for `description`, length must be greater than or equal to `1`")  # noqa: E501

        self._description = description

    @property
    def dry_run(self):
        """Gets the dry_run of this CreatePrefixListRequest.  # noqa: E501


        :return: The dry_run of this CreatePrefixListRequest.  # noqa: E501
        :rtype: bool
        """
        return self._dry_run

    @dry_run.setter
    def dry_run(self, dry_run):
        """Sets the dry_run of this CreatePrefixListRequest.


        :param dry_run: The dry_run of this CreatePrefixListRequest.  # noqa: E501
        :type: bool
        """

        self._dry_run = dry_run

    @property
    def ip_version(self):
        """Gets the ip_version of this CreatePrefixListRequest.  # noqa: E501


        :return: The ip_version of this CreatePrefixListRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_version

    @ip_version.setter
    def ip_version(self, ip_version):
        """Sets the ip_version of this CreatePrefixListRequest.


        :param ip_version: The ip_version of this CreatePrefixListRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["IPv4", "IPv6"]  # noqa: E501
        if (self._configuration.client_side_validation and
                ip_version not in allowed_values):
            raise ValueError(
                "Invalid value for `ip_version` ({0}), must be one of {1}"  # noqa: E501
                .format(ip_version, allowed_values)
            )

        self._ip_version = ip_version

    @property
    def max_entries(self):
        """Gets the max_entries of this CreatePrefixListRequest.  # noqa: E501


        :return: The max_entries of this CreatePrefixListRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_entries

    @max_entries.setter
    def max_entries(self, max_entries):
        """Sets the max_entries of this CreatePrefixListRequest.


        :param max_entries: The max_entries of this CreatePrefixListRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and max_entries is None:
            raise ValueError("Invalid value for `max_entries`, must not be `None`")  # noqa: E501

        self._max_entries = max_entries

    @property
    def prefix_list_entries(self):
        """Gets the prefix_list_entries of this CreatePrefixListRequest.  # noqa: E501


        :return: The prefix_list_entries of this CreatePrefixListRequest.  # noqa: E501
        :rtype: list[PrefixListEntryForCreatePrefixListInput]
        """
        return self._prefix_list_entries

    @prefix_list_entries.setter
    def prefix_list_entries(self, prefix_list_entries):
        """Sets the prefix_list_entries of this CreatePrefixListRequest.


        :param prefix_list_entries: The prefix_list_entries of this CreatePrefixListRequest.  # noqa: E501
        :type: list[PrefixListEntryForCreatePrefixListInput]
        """

        self._prefix_list_entries = prefix_list_entries

    @property
    def prefix_list_name(self):
        """Gets the prefix_list_name of this CreatePrefixListRequest.  # noqa: E501


        :return: The prefix_list_name of this CreatePrefixListRequest.  # noqa: E501
        :rtype: str
        """
        return self._prefix_list_name

    @prefix_list_name.setter
    def prefix_list_name(self, prefix_list_name):
        """Sets the prefix_list_name of this CreatePrefixListRequest.


        :param prefix_list_name: The prefix_list_name of this CreatePrefixListRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                prefix_list_name is not None and len(prefix_list_name) > 128):
            raise ValueError("Invalid value for `prefix_list_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                prefix_list_name is not None and len(prefix_list_name) < 1):
            raise ValueError("Invalid value for `prefix_list_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._prefix_list_name = prefix_list_name

    @property
    def project_name(self):
        """Gets the project_name of this CreatePrefixListRequest.  # noqa: E501


        :return: The project_name of this CreatePrefixListRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreatePrefixListRequest.


        :param project_name: The project_name of this CreatePrefixListRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this CreatePrefixListRequest.  # noqa: E501


        :return: The tags of this CreatePrefixListRequest.  # noqa: E501
        :rtype: list[TagForCreatePrefixListInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreatePrefixListRequest.


        :param tags: The tags of this CreatePrefixListRequest.  # noqa: E501
        :type: list[TagForCreatePrefixListInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreatePrefixListRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreatePrefixListRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreatePrefixListRequest):
            return True

        return self.to_dict() != other.to_dict()
