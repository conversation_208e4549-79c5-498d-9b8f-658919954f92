# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyNetworkInterfaceAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'delete_on_termination': 'bool',
        'description': 'str',
        'network_interface_id': 'str',
        'network_interface_name': 'str',
        'port_security_enabled': 'bool',
        'security_group_ids': 'list[str]'
    }

    attribute_map = {
        'delete_on_termination': 'DeleteOnTermination',
        'description': 'Description',
        'network_interface_id': 'NetworkInterfaceId',
        'network_interface_name': 'NetworkInterfaceName',
        'port_security_enabled': 'PortSecurityEnabled',
        'security_group_ids': 'SecurityGroupIds'
    }

    def __init__(self, delete_on_termination=None, description=None, network_interface_id=None, network_interface_name=None, port_security_enabled=None, security_group_ids=None, _configuration=None):  # noqa: E501
        """ModifyNetworkInterfaceAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._delete_on_termination = None
        self._description = None
        self._network_interface_id = None
        self._network_interface_name = None
        self._port_security_enabled = None
        self._security_group_ids = None
        self.discriminator = None

        if delete_on_termination is not None:
            self.delete_on_termination = delete_on_termination
        if description is not None:
            self.description = description
        self.network_interface_id = network_interface_id
        if network_interface_name is not None:
            self.network_interface_name = network_interface_name
        if port_security_enabled is not None:
            self.port_security_enabled = port_security_enabled
        if security_group_ids is not None:
            self.security_group_ids = security_group_ids

    @property
    def delete_on_termination(self):
        """Gets the delete_on_termination of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501


        :return: The delete_on_termination of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._delete_on_termination

    @delete_on_termination.setter
    def delete_on_termination(self, delete_on_termination):
        """Sets the delete_on_termination of this ModifyNetworkInterfaceAttributesRequest.


        :param delete_on_termination: The delete_on_termination of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501
        :type: bool
        """

        self._delete_on_termination = delete_on_termination

    @property
    def description(self):
        """Gets the description of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501


        :return: The description of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyNetworkInterfaceAttributesRequest.


        :param description: The description of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 255):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `255`")  # noqa: E501
        if (self._configuration.client_side_validation and
                description is not None and len(description) < 1):
            raise ValueError("Invalid value for `description`, length must be greater than or equal to `1`")  # noqa: E501

        self._description = description

    @property
    def network_interface_id(self):
        """Gets the network_interface_id of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501


        :return: The network_interface_id of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._network_interface_id

    @network_interface_id.setter
    def network_interface_id(self, network_interface_id):
        """Sets the network_interface_id of this ModifyNetworkInterfaceAttributesRequest.


        :param network_interface_id: The network_interface_id of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and network_interface_id is None:
            raise ValueError("Invalid value for `network_interface_id`, must not be `None`")  # noqa: E501

        self._network_interface_id = network_interface_id

    @property
    def network_interface_name(self):
        """Gets the network_interface_name of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501


        :return: The network_interface_name of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._network_interface_name

    @network_interface_name.setter
    def network_interface_name(self, network_interface_name):
        """Sets the network_interface_name of this ModifyNetworkInterfaceAttributesRequest.


        :param network_interface_name: The network_interface_name of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                network_interface_name is not None and len(network_interface_name) > 128):
            raise ValueError("Invalid value for `network_interface_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                network_interface_name is not None and len(network_interface_name) < 1):
            raise ValueError("Invalid value for `network_interface_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._network_interface_name = network_interface_name

    @property
    def port_security_enabled(self):
        """Gets the port_security_enabled of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501


        :return: The port_security_enabled of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._port_security_enabled

    @port_security_enabled.setter
    def port_security_enabled(self, port_security_enabled):
        """Sets the port_security_enabled of this ModifyNetworkInterfaceAttributesRequest.


        :param port_security_enabled: The port_security_enabled of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501
        :type: bool
        """

        self._port_security_enabled = port_security_enabled

    @property
    def security_group_ids(self):
        """Gets the security_group_ids of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501


        :return: The security_group_ids of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_ids

    @security_group_ids.setter
    def security_group_ids(self, security_group_ids):
        """Sets the security_group_ids of this ModifyNetworkInterfaceAttributesRequest.


        :param security_group_ids: The security_group_ids of this ModifyNetworkInterfaceAttributesRequest.  # noqa: E501
        :type: list[str]
        """

        self._security_group_ids = security_group_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyNetworkInterfaceAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyNetworkInterfaceAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyNetworkInterfaceAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
