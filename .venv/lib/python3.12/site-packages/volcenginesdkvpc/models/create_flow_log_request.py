# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateFlowLogRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'aggregation_interval': 'int',
        'client_token': 'str',
        'description': 'str',
        'flow_log_name': 'str',
        'log_project_name': 'str',
        'log_topic_name': 'str',
        'project_name': 'str',
        'resource_id': 'str',
        'resource_type': 'str',
        'tags': 'list[TagForCreateFlowLogInput]',
        'traffic_type': 'str'
    }

    attribute_map = {
        'aggregation_interval': 'AggregationInterval',
        'client_token': 'ClientToken',
        'description': 'Description',
        'flow_log_name': 'FlowLogName',
        'log_project_name': 'LogProjectName',
        'log_topic_name': 'LogTopicName',
        'project_name': 'ProjectName',
        'resource_id': 'ResourceId',
        'resource_type': 'ResourceType',
        'tags': 'Tags',
        'traffic_type': 'TrafficType'
    }

    def __init__(self, aggregation_interval=None, client_token=None, description=None, flow_log_name=None, log_project_name=None, log_topic_name=None, project_name=None, resource_id=None, resource_type=None, tags=None, traffic_type=None, _configuration=None):  # noqa: E501
        """CreateFlowLogRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._aggregation_interval = None
        self._client_token = None
        self._description = None
        self._flow_log_name = None
        self._log_project_name = None
        self._log_topic_name = None
        self._project_name = None
        self._resource_id = None
        self._resource_type = None
        self._tags = None
        self._traffic_type = None
        self.discriminator = None

        self.aggregation_interval = aggregation_interval
        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        self.flow_log_name = flow_log_name
        self.log_project_name = log_project_name
        self.log_topic_name = log_topic_name
        if project_name is not None:
            self.project_name = project_name
        self.resource_id = resource_id
        self.resource_type = resource_type
        if tags is not None:
            self.tags = tags
        self.traffic_type = traffic_type

    @property
    def aggregation_interval(self):
        """Gets the aggregation_interval of this CreateFlowLogRequest.  # noqa: E501


        :return: The aggregation_interval of this CreateFlowLogRequest.  # noqa: E501
        :rtype: int
        """
        return self._aggregation_interval

    @aggregation_interval.setter
    def aggregation_interval(self, aggregation_interval):
        """Sets the aggregation_interval of this CreateFlowLogRequest.


        :param aggregation_interval: The aggregation_interval of this CreateFlowLogRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and aggregation_interval is None:
            raise ValueError("Invalid value for `aggregation_interval`, must not be `None`")  # noqa: E501

        self._aggregation_interval = aggregation_interval

    @property
    def client_token(self):
        """Gets the client_token of this CreateFlowLogRequest.  # noqa: E501


        :return: The client_token of this CreateFlowLogRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateFlowLogRequest.


        :param client_token: The client_token of this CreateFlowLogRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateFlowLogRequest.  # noqa: E501


        :return: The description of this CreateFlowLogRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateFlowLogRequest.


        :param description: The description of this CreateFlowLogRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 255):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `255`")  # noqa: E501

        self._description = description

    @property
    def flow_log_name(self):
        """Gets the flow_log_name of this CreateFlowLogRequest.  # noqa: E501


        :return: The flow_log_name of this CreateFlowLogRequest.  # noqa: E501
        :rtype: str
        """
        return self._flow_log_name

    @flow_log_name.setter
    def flow_log_name(self, flow_log_name):
        """Sets the flow_log_name of this CreateFlowLogRequest.


        :param flow_log_name: The flow_log_name of this CreateFlowLogRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and flow_log_name is None:
            raise ValueError("Invalid value for `flow_log_name`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                flow_log_name is not None and len(flow_log_name) > 128):
            raise ValueError("Invalid value for `flow_log_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                flow_log_name is not None and len(flow_log_name) < 1):
            raise ValueError("Invalid value for `flow_log_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._flow_log_name = flow_log_name

    @property
    def log_project_name(self):
        """Gets the log_project_name of this CreateFlowLogRequest.  # noqa: E501


        :return: The log_project_name of this CreateFlowLogRequest.  # noqa: E501
        :rtype: str
        """
        return self._log_project_name

    @log_project_name.setter
    def log_project_name(self, log_project_name):
        """Sets the log_project_name of this CreateFlowLogRequest.


        :param log_project_name: The log_project_name of this CreateFlowLogRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and log_project_name is None:
            raise ValueError("Invalid value for `log_project_name`, must not be `None`")  # noqa: E501

        self._log_project_name = log_project_name

    @property
    def log_topic_name(self):
        """Gets the log_topic_name of this CreateFlowLogRequest.  # noqa: E501


        :return: The log_topic_name of this CreateFlowLogRequest.  # noqa: E501
        :rtype: str
        """
        return self._log_topic_name

    @log_topic_name.setter
    def log_topic_name(self, log_topic_name):
        """Sets the log_topic_name of this CreateFlowLogRequest.


        :param log_topic_name: The log_topic_name of this CreateFlowLogRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and log_topic_name is None:
            raise ValueError("Invalid value for `log_topic_name`, must not be `None`")  # noqa: E501

        self._log_topic_name = log_topic_name

    @property
    def project_name(self):
        """Gets the project_name of this CreateFlowLogRequest.  # noqa: E501


        :return: The project_name of this CreateFlowLogRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateFlowLogRequest.


        :param project_name: The project_name of this CreateFlowLogRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def resource_id(self):
        """Gets the resource_id of this CreateFlowLogRequest.  # noqa: E501


        :return: The resource_id of this CreateFlowLogRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_id

    @resource_id.setter
    def resource_id(self, resource_id):
        """Sets the resource_id of this CreateFlowLogRequest.


        :param resource_id: The resource_id of this CreateFlowLogRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and resource_id is None:
            raise ValueError("Invalid value for `resource_id`, must not be `None`")  # noqa: E501

        self._resource_id = resource_id

    @property
    def resource_type(self):
        """Gets the resource_type of this CreateFlowLogRequest.  # noqa: E501


        :return: The resource_type of this CreateFlowLogRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this CreateFlowLogRequest.


        :param resource_type: The resource_type of this CreateFlowLogRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and resource_type is None:
            raise ValueError("Invalid value for `resource_type`, must not be `None`")  # noqa: E501

        self._resource_type = resource_type

    @property
    def tags(self):
        """Gets the tags of this CreateFlowLogRequest.  # noqa: E501


        :return: The tags of this CreateFlowLogRequest.  # noqa: E501
        :rtype: list[TagForCreateFlowLogInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateFlowLogRequest.


        :param tags: The tags of this CreateFlowLogRequest.  # noqa: E501
        :type: list[TagForCreateFlowLogInput]
        """

        self._tags = tags

    @property
    def traffic_type(self):
        """Gets the traffic_type of this CreateFlowLogRequest.  # noqa: E501


        :return: The traffic_type of this CreateFlowLogRequest.  # noqa: E501
        :rtype: str
        """
        return self._traffic_type

    @traffic_type.setter
    def traffic_type(self, traffic_type):
        """Sets the traffic_type of this CreateFlowLogRequest.


        :param traffic_type: The traffic_type of this CreateFlowLogRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and traffic_type is None:
            raise ValueError("Invalid value for `traffic_type`, must not be `None`")  # noqa: E501

        self._traffic_type = traffic_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateFlowLogRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateFlowLogRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateFlowLogRequest):
            return True

        return self.to_dict() != other.to_dict()
