# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifySubnetAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'enable_ipv6': 'bool',
        'ipv6_cidr_block': 'int',
        'subnet_id': 'str',
        'subnet_name': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'enable_ipv6': 'EnableIpv6',
        'ipv6_cidr_block': 'Ipv6CidrBlock',
        'subnet_id': 'SubnetId',
        'subnet_name': 'SubnetName'
    }

    def __init__(self, description=None, enable_ipv6=None, ipv6_cidr_block=None, subnet_id=None, subnet_name=None, _configuration=None):  # noqa: E501
        """ModifySubnetAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._enable_ipv6 = None
        self._ipv6_cidr_block = None
        self._subnet_id = None
        self._subnet_name = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if enable_ipv6 is not None:
            self.enable_ipv6 = enable_ipv6
        if ipv6_cidr_block is not None:
            self.ipv6_cidr_block = ipv6_cidr_block
        self.subnet_id = subnet_id
        if subnet_name is not None:
            self.subnet_name = subnet_name

    @property
    def description(self):
        """Gets the description of this ModifySubnetAttributesRequest.  # noqa: E501


        :return: The description of this ModifySubnetAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifySubnetAttributesRequest.


        :param description: The description of this ModifySubnetAttributesRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 255):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `255`")  # noqa: E501
        if (self._configuration.client_side_validation and
                description is not None and len(description) < 1):
            raise ValueError("Invalid value for `description`, length must be greater than or equal to `1`")  # noqa: E501

        self._description = description

    @property
    def enable_ipv6(self):
        """Gets the enable_ipv6 of this ModifySubnetAttributesRequest.  # noqa: E501


        :return: The enable_ipv6 of this ModifySubnetAttributesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_ipv6

    @enable_ipv6.setter
    def enable_ipv6(self, enable_ipv6):
        """Sets the enable_ipv6 of this ModifySubnetAttributesRequest.


        :param enable_ipv6: The enable_ipv6 of this ModifySubnetAttributesRequest.  # noqa: E501
        :type: bool
        """

        self._enable_ipv6 = enable_ipv6

    @property
    def ipv6_cidr_block(self):
        """Gets the ipv6_cidr_block of this ModifySubnetAttributesRequest.  # noqa: E501


        :return: The ipv6_cidr_block of this ModifySubnetAttributesRequest.  # noqa: E501
        :rtype: int
        """
        return self._ipv6_cidr_block

    @ipv6_cidr_block.setter
    def ipv6_cidr_block(self, ipv6_cidr_block):
        """Sets the ipv6_cidr_block of this ModifySubnetAttributesRequest.


        :param ipv6_cidr_block: The ipv6_cidr_block of this ModifySubnetAttributesRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                ipv6_cidr_block is not None and ipv6_cidr_block > 255):  # noqa: E501
            raise ValueError("Invalid value for `ipv6_cidr_block`, must be a value less than or equal to `255`")  # noqa: E501
        if (self._configuration.client_side_validation and
                ipv6_cidr_block is not None and ipv6_cidr_block < 0):  # noqa: E501
            raise ValueError("Invalid value for `ipv6_cidr_block`, must be a value greater than or equal to `0`")  # noqa: E501

        self._ipv6_cidr_block = ipv6_cidr_block

    @property
    def subnet_id(self):
        """Gets the subnet_id of this ModifySubnetAttributesRequest.  # noqa: E501


        :return: The subnet_id of this ModifySubnetAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this ModifySubnetAttributesRequest.


        :param subnet_id: The subnet_id of this ModifySubnetAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and subnet_id is None:
            raise ValueError("Invalid value for `subnet_id`, must not be `None`")  # noqa: E501

        self._subnet_id = subnet_id

    @property
    def subnet_name(self):
        """Gets the subnet_name of this ModifySubnetAttributesRequest.  # noqa: E501


        :return: The subnet_name of this ModifySubnetAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_name

    @subnet_name.setter
    def subnet_name(self, subnet_name):
        """Sets the subnet_name of this ModifySubnetAttributesRequest.


        :param subnet_name: The subnet_name of this ModifySubnetAttributesRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                subnet_name is not None and len(subnet_name) > 128):
            raise ValueError("Invalid value for `subnet_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                subnet_name is not None and len(subnet_name) < 1):
            raise ValueError("Invalid value for `subnet_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._subnet_name = subnet_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifySubnetAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifySubnetAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifySubnetAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
