# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeleteIpAddressPoolCidrBlockRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cidr_block': 'str',
        'client_token': 'str',
        'ip_address_pool_id': 'str'
    }

    attribute_map = {
        'cidr_block': 'CidrBlock',
        'client_token': 'ClientToken',
        'ip_address_pool_id': 'IpAddressPoolId'
    }

    def __init__(self, cidr_block=None, client_token=None, ip_address_pool_id=None, _configuration=None):  # noqa: E501
        """DeleteIpAddressPoolCidrBlockRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cidr_block = None
        self._client_token = None
        self._ip_address_pool_id = None
        self.discriminator = None

        self.cidr_block = cidr_block
        if client_token is not None:
            self.client_token = client_token
        self.ip_address_pool_id = ip_address_pool_id

    @property
    def cidr_block(self):
        """Gets the cidr_block of this DeleteIpAddressPoolCidrBlockRequest.  # noqa: E501


        :return: The cidr_block of this DeleteIpAddressPoolCidrBlockRequest.  # noqa: E501
        :rtype: str
        """
        return self._cidr_block

    @cidr_block.setter
    def cidr_block(self, cidr_block):
        """Sets the cidr_block of this DeleteIpAddressPoolCidrBlockRequest.


        :param cidr_block: The cidr_block of this DeleteIpAddressPoolCidrBlockRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cidr_block is None:
            raise ValueError("Invalid value for `cidr_block`, must not be `None`")  # noqa: E501

        self._cidr_block = cidr_block

    @property
    def client_token(self):
        """Gets the client_token of this DeleteIpAddressPoolCidrBlockRequest.  # noqa: E501


        :return: The client_token of this DeleteIpAddressPoolCidrBlockRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this DeleteIpAddressPoolCidrBlockRequest.


        :param client_token: The client_token of this DeleteIpAddressPoolCidrBlockRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def ip_address_pool_id(self):
        """Gets the ip_address_pool_id of this DeleteIpAddressPoolCidrBlockRequest.  # noqa: E501


        :return: The ip_address_pool_id of this DeleteIpAddressPoolCidrBlockRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_address_pool_id

    @ip_address_pool_id.setter
    def ip_address_pool_id(self, ip_address_pool_id):
        """Sets the ip_address_pool_id of this DeleteIpAddressPoolCidrBlockRequest.


        :param ip_address_pool_id: The ip_address_pool_id of this DeleteIpAddressPoolCidrBlockRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ip_address_pool_id is None:
            raise ValueError("Invalid value for `ip_address_pool_id`, must not be `None`")  # noqa: E501

        self._ip_address_pool_id = ip_address_pool_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeleteIpAddressPoolCidrBlockRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeleteIpAddressPoolCidrBlockRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeleteIpAddressPoolCidrBlockRequest):
            return True

        return self.to_dict() != other.to_dict()
