# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateHaVipRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'description': 'str',
        'ha_vip_name': 'str',
        'ip_address': 'str',
        'subnet_id': 'str',
        'tags': 'list[TagForCreateHaVipInput]'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'description': 'Description',
        'ha_vip_name': 'HaVipName',
        'ip_address': 'IpAddress',
        'subnet_id': 'SubnetId',
        'tags': 'Tags'
    }

    def __init__(self, client_token=None, description=None, ha_vip_name=None, ip_address=None, subnet_id=None, tags=None, _configuration=None):  # noqa: E501
        """CreateHaVipRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._description = None
        self._ha_vip_name = None
        self._ip_address = None
        self._subnet_id = None
        self._tags = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        if ha_vip_name is not None:
            self.ha_vip_name = ha_vip_name
        if ip_address is not None:
            self.ip_address = ip_address
        self.subnet_id = subnet_id
        if tags is not None:
            self.tags = tags

    @property
    def client_token(self):
        """Gets the client_token of this CreateHaVipRequest.  # noqa: E501


        :return: The client_token of this CreateHaVipRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateHaVipRequest.


        :param client_token: The client_token of this CreateHaVipRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateHaVipRequest.  # noqa: E501


        :return: The description of this CreateHaVipRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateHaVipRequest.


        :param description: The description of this CreateHaVipRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 255):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `255`")  # noqa: E501
        if (self._configuration.client_side_validation and
                description is not None and len(description) < 1):
            raise ValueError("Invalid value for `description`, length must be greater than or equal to `1`")  # noqa: E501

        self._description = description

    @property
    def ha_vip_name(self):
        """Gets the ha_vip_name of this CreateHaVipRequest.  # noqa: E501


        :return: The ha_vip_name of this CreateHaVipRequest.  # noqa: E501
        :rtype: str
        """
        return self._ha_vip_name

    @ha_vip_name.setter
    def ha_vip_name(self, ha_vip_name):
        """Sets the ha_vip_name of this CreateHaVipRequest.


        :param ha_vip_name: The ha_vip_name of this CreateHaVipRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                ha_vip_name is not None and len(ha_vip_name) > 128):
            raise ValueError("Invalid value for `ha_vip_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                ha_vip_name is not None and len(ha_vip_name) < 1):
            raise ValueError("Invalid value for `ha_vip_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._ha_vip_name = ha_vip_name

    @property
    def ip_address(self):
        """Gets the ip_address of this CreateHaVipRequest.  # noqa: E501


        :return: The ip_address of this CreateHaVipRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_address

    @ip_address.setter
    def ip_address(self, ip_address):
        """Sets the ip_address of this CreateHaVipRequest.


        :param ip_address: The ip_address of this CreateHaVipRequest.  # noqa: E501
        :type: str
        """

        self._ip_address = ip_address

    @property
    def subnet_id(self):
        """Gets the subnet_id of this CreateHaVipRequest.  # noqa: E501


        :return: The subnet_id of this CreateHaVipRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this CreateHaVipRequest.


        :param subnet_id: The subnet_id of this CreateHaVipRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and subnet_id is None:
            raise ValueError("Invalid value for `subnet_id`, must not be `None`")  # noqa: E501

        self._subnet_id = subnet_id

    @property
    def tags(self):
        """Gets the tags of this CreateHaVipRequest.  # noqa: E501


        :return: The tags of this CreateHaVipRequest.  # noqa: E501
        :rtype: list[TagForCreateHaVipInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateHaVipRequest.


        :param tags: The tags of this CreateHaVipRequest.  # noqa: E501
        :type: list[TagForCreateHaVipInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateHaVipRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateHaVipRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateHaVipRequest):
            return True

        return self.to_dict() != other.to_dict()
