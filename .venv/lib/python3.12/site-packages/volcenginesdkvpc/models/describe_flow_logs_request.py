# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeFlowLogsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'aggregation_interval': 'int',
        'description': 'str',
        'flow_log_ids': 'list[str]',
        'flow_log_name': 'str',
        'log_project_id': 'str',
        'log_topic_id': 'str',
        'max_results': 'int',
        'next_token': 'str',
        'project_name': 'str',
        'resource_id': 'str',
        'resource_type': 'str',
        'status': 'str',
        'tag_filters': 'list[TagFilterForDescribeFlowLogsInput]',
        'traffic_type': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'aggregation_interval': 'AggregationInterval',
        'description': 'Description',
        'flow_log_ids': 'FlowLogIds',
        'flow_log_name': 'FlowLogName',
        'log_project_id': 'LogProjectId',
        'log_topic_id': 'LogTopicId',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'project_name': 'ProjectName',
        'resource_id': 'ResourceId',
        'resource_type': 'ResourceType',
        'status': 'Status',
        'tag_filters': 'TagFilters',
        'traffic_type': 'TrafficType',
        'vpc_id': 'VpcId'
    }

    def __init__(self, aggregation_interval=None, description=None, flow_log_ids=None, flow_log_name=None, log_project_id=None, log_topic_id=None, max_results=None, next_token=None, project_name=None, resource_id=None, resource_type=None, status=None, tag_filters=None, traffic_type=None, vpc_id=None, _configuration=None):  # noqa: E501
        """DescribeFlowLogsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._aggregation_interval = None
        self._description = None
        self._flow_log_ids = None
        self._flow_log_name = None
        self._log_project_id = None
        self._log_topic_id = None
        self._max_results = None
        self._next_token = None
        self._project_name = None
        self._resource_id = None
        self._resource_type = None
        self._status = None
        self._tag_filters = None
        self._traffic_type = None
        self._vpc_id = None
        self.discriminator = None

        if aggregation_interval is not None:
            self.aggregation_interval = aggregation_interval
        if description is not None:
            self.description = description
        if flow_log_ids is not None:
            self.flow_log_ids = flow_log_ids
        if flow_log_name is not None:
            self.flow_log_name = flow_log_name
        if log_project_id is not None:
            self.log_project_id = log_project_id
        if log_topic_id is not None:
            self.log_topic_id = log_topic_id
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if project_name is not None:
            self.project_name = project_name
        if resource_id is not None:
            self.resource_id = resource_id
        if resource_type is not None:
            self.resource_type = resource_type
        if status is not None:
            self.status = status
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if traffic_type is not None:
            self.traffic_type = traffic_type
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def aggregation_interval(self):
        """Gets the aggregation_interval of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The aggregation_interval of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: int
        """
        return self._aggregation_interval

    @aggregation_interval.setter
    def aggregation_interval(self, aggregation_interval):
        """Sets the aggregation_interval of this DescribeFlowLogsRequest.


        :param aggregation_interval: The aggregation_interval of this DescribeFlowLogsRequest.  # noqa: E501
        :type: int
        """

        self._aggregation_interval = aggregation_interval

    @property
    def description(self):
        """Gets the description of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The description of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DescribeFlowLogsRequest.


        :param description: The description of this DescribeFlowLogsRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def flow_log_ids(self):
        """Gets the flow_log_ids of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The flow_log_ids of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._flow_log_ids

    @flow_log_ids.setter
    def flow_log_ids(self, flow_log_ids):
        """Sets the flow_log_ids of this DescribeFlowLogsRequest.


        :param flow_log_ids: The flow_log_ids of this DescribeFlowLogsRequest.  # noqa: E501
        :type: list[str]
        """

        self._flow_log_ids = flow_log_ids

    @property
    def flow_log_name(self):
        """Gets the flow_log_name of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The flow_log_name of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._flow_log_name

    @flow_log_name.setter
    def flow_log_name(self, flow_log_name):
        """Sets the flow_log_name of this DescribeFlowLogsRequest.


        :param flow_log_name: The flow_log_name of this DescribeFlowLogsRequest.  # noqa: E501
        :type: str
        """

        self._flow_log_name = flow_log_name

    @property
    def log_project_id(self):
        """Gets the log_project_id of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The log_project_id of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._log_project_id

    @log_project_id.setter
    def log_project_id(self, log_project_id):
        """Sets the log_project_id of this DescribeFlowLogsRequest.


        :param log_project_id: The log_project_id of this DescribeFlowLogsRequest.  # noqa: E501
        :type: str
        """

        self._log_project_id = log_project_id

    @property
    def log_topic_id(self):
        """Gets the log_topic_id of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The log_topic_id of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._log_topic_id

    @log_topic_id.setter
    def log_topic_id(self, log_topic_id):
        """Sets the log_topic_id of this DescribeFlowLogsRequest.


        :param log_topic_id: The log_topic_id of this DescribeFlowLogsRequest.  # noqa: E501
        :type: str
        """

        self._log_topic_id = log_topic_id

    @property
    def max_results(self):
        """Gets the max_results of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The max_results of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeFlowLogsRequest.


        :param max_results: The max_results of this DescribeFlowLogsRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The next_token of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeFlowLogsRequest.


        :param next_token: The next_token of this DescribeFlowLogsRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def project_name(self):
        """Gets the project_name of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The project_name of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeFlowLogsRequest.


        :param project_name: The project_name of this DescribeFlowLogsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def resource_id(self):
        """Gets the resource_id of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The resource_id of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_id

    @resource_id.setter
    def resource_id(self, resource_id):
        """Sets the resource_id of this DescribeFlowLogsRequest.


        :param resource_id: The resource_id of this DescribeFlowLogsRequest.  # noqa: E501
        :type: str
        """

        self._resource_id = resource_id

    @property
    def resource_type(self):
        """Gets the resource_type of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The resource_type of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this DescribeFlowLogsRequest.


        :param resource_type: The resource_type of this DescribeFlowLogsRequest.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def status(self):
        """Gets the status of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The status of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeFlowLogsRequest.


        :param status: The status of this DescribeFlowLogsRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The tag_filters of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeFlowLogsInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeFlowLogsRequest.


        :param tag_filters: The tag_filters of this DescribeFlowLogsRequest.  # noqa: E501
        :type: list[TagFilterForDescribeFlowLogsInput]
        """

        self._tag_filters = tag_filters

    @property
    def traffic_type(self):
        """Gets the traffic_type of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The traffic_type of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._traffic_type

    @traffic_type.setter
    def traffic_type(self, traffic_type):
        """Sets the traffic_type of this DescribeFlowLogsRequest.


        :param traffic_type: The traffic_type of this DescribeFlowLogsRequest.  # noqa: E501
        :type: str
        """

        self._traffic_type = traffic_type

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribeFlowLogsRequest.  # noqa: E501


        :return: The vpc_id of this DescribeFlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribeFlowLogsRequest.


        :param vpc_id: The vpc_id of this DescribeFlowLogsRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeFlowLogsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeFlowLogsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeFlowLogsRequest):
            return True

        return self.to_dict() != other.to_dict()
