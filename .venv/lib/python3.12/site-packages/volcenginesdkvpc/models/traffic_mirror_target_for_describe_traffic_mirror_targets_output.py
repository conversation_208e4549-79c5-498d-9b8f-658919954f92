# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'str',
        'description': 'str',
        'instance_id': 'str',
        'instance_type': 'str',
        'project_name': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribeTrafficMirrorTargetsOutput]',
        'traffic_mirror_target_id': 'str',
        'traffic_mirror_target_name': 'str'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'description': 'Description',
        'instance_id': 'InstanceId',
        'instance_type': 'InstanceType',
        'project_name': 'ProjectName',
        'status': 'Status',
        'tags': 'Tags',
        'traffic_mirror_target_id': 'TrafficMirrorTargetId',
        'traffic_mirror_target_name': 'TrafficMirrorTargetName'
    }

    def __init__(self, created_at=None, description=None, instance_id=None, instance_type=None, project_name=None, status=None, tags=None, traffic_mirror_target_id=None, traffic_mirror_target_name=None, _configuration=None):  # noqa: E501
        """TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._description = None
        self._instance_id = None
        self._instance_type = None
        self._project_name = None
        self._status = None
        self._tags = None
        self._traffic_mirror_target_id = None
        self._traffic_mirror_target_name = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if description is not None:
            self.description = description
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_type is not None:
            self.instance_type = instance_type
        if project_name is not None:
            self.project_name = project_name
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if traffic_mirror_target_id is not None:
            self.traffic_mirror_target_id = traffic_mirror_target_id
        if traffic_mirror_target_name is not None:
            self.traffic_mirror_target_name = traffic_mirror_target_name

    @property
    def created_at(self):
        """Gets the created_at of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501


        :return: The created_at of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.


        :param created_at: The created_at of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def description(self):
        """Gets the description of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501


        :return: The description of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.


        :param description: The description of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def instance_id(self):
        """Gets the instance_id of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501


        :return: The instance_id of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.


        :param instance_id: The instance_id of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_type(self):
        """Gets the instance_type of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501


        :return: The instance_type of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.


        :param instance_type: The instance_type of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :type: str
        """

        self._instance_type = instance_type

    @property
    def project_name(self):
        """Gets the project_name of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501


        :return: The project_name of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.


        :param project_name: The project_name of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def status(self):
        """Gets the status of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501


        :return: The status of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.


        :param status: The status of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501


        :return: The tags of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :rtype: list[TagForDescribeTrafficMirrorTargetsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.


        :param tags: The tags of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :type: list[TagForDescribeTrafficMirrorTargetsOutput]
        """

        self._tags = tags

    @property
    def traffic_mirror_target_id(self):
        """Gets the traffic_mirror_target_id of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501


        :return: The traffic_mirror_target_id of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._traffic_mirror_target_id

    @traffic_mirror_target_id.setter
    def traffic_mirror_target_id(self, traffic_mirror_target_id):
        """Sets the traffic_mirror_target_id of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.


        :param traffic_mirror_target_id: The traffic_mirror_target_id of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :type: str
        """

        self._traffic_mirror_target_id = traffic_mirror_target_id

    @property
    def traffic_mirror_target_name(self):
        """Gets the traffic_mirror_target_name of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501


        :return: The traffic_mirror_target_name of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._traffic_mirror_target_name

    @traffic_mirror_target_name.setter
    def traffic_mirror_target_name(self, traffic_mirror_target_name):
        """Sets the traffic_mirror_target_name of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.


        :param traffic_mirror_target_name: The traffic_mirror_target_name of this TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput.  # noqa: E501
        :type: str
        """

        self._traffic_mirror_target_name = traffic_mirror_target_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TrafficMirrorTargetForDescribeTrafficMirrorTargetsOutput):
            return True

        return self.to_dict() != other.to_dict()
