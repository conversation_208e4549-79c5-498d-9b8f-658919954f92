# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DisassociateEipAddressRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allocation_id': 'str',
        'client_token': 'str',
        'instance_id': 'str',
        'instance_type': 'str'
    }

    attribute_map = {
        'allocation_id': 'AllocationId',
        'client_token': 'ClientToken',
        'instance_id': 'InstanceId',
        'instance_type': 'InstanceType'
    }

    def __init__(self, allocation_id=None, client_token=None, instance_id=None, instance_type=None, _configuration=None):  # noqa: E501
        """DisassociateEipAddressRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allocation_id = None
        self._client_token = None
        self._instance_id = None
        self._instance_type = None
        self.discriminator = None

        self.allocation_id = allocation_id
        if client_token is not None:
            self.client_token = client_token
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_type is not None:
            self.instance_type = instance_type

    @property
    def allocation_id(self):
        """Gets the allocation_id of this DisassociateEipAddressRequest.  # noqa: E501


        :return: The allocation_id of this DisassociateEipAddressRequest.  # noqa: E501
        :rtype: str
        """
        return self._allocation_id

    @allocation_id.setter
    def allocation_id(self, allocation_id):
        """Sets the allocation_id of this DisassociateEipAddressRequest.


        :param allocation_id: The allocation_id of this DisassociateEipAddressRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and allocation_id is None:
            raise ValueError("Invalid value for `allocation_id`, must not be `None`")  # noqa: E501

        self._allocation_id = allocation_id

    @property
    def client_token(self):
        """Gets the client_token of this DisassociateEipAddressRequest.  # noqa: E501


        :return: The client_token of this DisassociateEipAddressRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this DisassociateEipAddressRequest.


        :param client_token: The client_token of this DisassociateEipAddressRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def instance_id(self):
        """Gets the instance_id of this DisassociateEipAddressRequest.  # noqa: E501


        :return: The instance_id of this DisassociateEipAddressRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DisassociateEipAddressRequest.


        :param instance_id: The instance_id of this DisassociateEipAddressRequest.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_type(self):
        """Gets the instance_type of this DisassociateEipAddressRequest.  # noqa: E501


        :return: The instance_type of this DisassociateEipAddressRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this DisassociateEipAddressRequest.


        :param instance_type: The instance_type of this DisassociateEipAddressRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Nat", "NetworkInterface", "ClbInstance", "EcsInstance"]  # noqa: E501
        if (self._configuration.client_side_validation and
                instance_type not in allowed_values):
            raise ValueError(
                "Invalid value for `instance_type` ({0}), must be one of {1}"  # noqa: E501
                .format(instance_type, allowed_values)
            )

        self._instance_type = instance_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DisassociateEipAddressRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DisassociateEipAddressRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DisassociateEipAddressRequest):
            return True

        return self.to_dict() != other.to_dict()
