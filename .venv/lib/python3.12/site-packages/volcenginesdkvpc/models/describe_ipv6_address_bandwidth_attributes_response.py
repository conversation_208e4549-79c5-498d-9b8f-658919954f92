# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeIpv6AddressBandwidthAttributesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allocation_id': 'str',
        'bandwidth': 'int',
        'bandwidth_package_id': 'str',
        'billing_type': 'int',
        'business_status': 'str',
        'creation_time': 'str',
        'delete_time': 'str',
        'isp': 'str',
        'instance_id': 'str',
        'instance_type': 'str',
        'ipv6_address': 'str',
        'ipv6_gateway_id': 'str',
        'lock_reason': 'str',
        'network_type': 'str',
        'overdue_time': 'str',
        'project_name': 'str',
        'request_id': 'str',
        'service_managed': 'bool',
        'status': 'str',
        'tags': 'list[TagForDescribeIpv6AddressBandwidthAttributesOutput]',
        'update_time': 'str'
    }

    attribute_map = {
        'allocation_id': 'AllocationId',
        'bandwidth': 'Bandwidth',
        'bandwidth_package_id': 'BandwidthPackageId',
        'billing_type': 'BillingType',
        'business_status': 'BusinessStatus',
        'creation_time': 'CreationTime',
        'delete_time': 'DeleteTime',
        'isp': 'ISP',
        'instance_id': 'InstanceId',
        'instance_type': 'InstanceType',
        'ipv6_address': 'Ipv6Address',
        'ipv6_gateway_id': 'Ipv6GatewayId',
        'lock_reason': 'LockReason',
        'network_type': 'NetworkType',
        'overdue_time': 'OverdueTime',
        'project_name': 'ProjectName',
        'request_id': 'RequestId',
        'service_managed': 'ServiceManaged',
        'status': 'Status',
        'tags': 'Tags',
        'update_time': 'UpdateTime'
    }

    def __init__(self, allocation_id=None, bandwidth=None, bandwidth_package_id=None, billing_type=None, business_status=None, creation_time=None, delete_time=None, isp=None, instance_id=None, instance_type=None, ipv6_address=None, ipv6_gateway_id=None, lock_reason=None, network_type=None, overdue_time=None, project_name=None, request_id=None, service_managed=None, status=None, tags=None, update_time=None, _configuration=None):  # noqa: E501
        """DescribeIpv6AddressBandwidthAttributesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allocation_id = None
        self._bandwidth = None
        self._bandwidth_package_id = None
        self._billing_type = None
        self._business_status = None
        self._creation_time = None
        self._delete_time = None
        self._isp = None
        self._instance_id = None
        self._instance_type = None
        self._ipv6_address = None
        self._ipv6_gateway_id = None
        self._lock_reason = None
        self._network_type = None
        self._overdue_time = None
        self._project_name = None
        self._request_id = None
        self._service_managed = None
        self._status = None
        self._tags = None
        self._update_time = None
        self.discriminator = None

        if allocation_id is not None:
            self.allocation_id = allocation_id
        if bandwidth is not None:
            self.bandwidth = bandwidth
        if bandwidth_package_id is not None:
            self.bandwidth_package_id = bandwidth_package_id
        if billing_type is not None:
            self.billing_type = billing_type
        if business_status is not None:
            self.business_status = business_status
        if creation_time is not None:
            self.creation_time = creation_time
        if delete_time is not None:
            self.delete_time = delete_time
        if isp is not None:
            self.isp = isp
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_type is not None:
            self.instance_type = instance_type
        if ipv6_address is not None:
            self.ipv6_address = ipv6_address
        if ipv6_gateway_id is not None:
            self.ipv6_gateway_id = ipv6_gateway_id
        if lock_reason is not None:
            self.lock_reason = lock_reason
        if network_type is not None:
            self.network_type = network_type
        if overdue_time is not None:
            self.overdue_time = overdue_time
        if project_name is not None:
            self.project_name = project_name
        if request_id is not None:
            self.request_id = request_id
        if service_managed is not None:
            self.service_managed = service_managed
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time

    @property
    def allocation_id(self):
        """Gets the allocation_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The allocation_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._allocation_id

    @allocation_id.setter
    def allocation_id(self, allocation_id):
        """Sets the allocation_id of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param allocation_id: The allocation_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._allocation_id = allocation_id

    @property
    def bandwidth(self):
        """Gets the bandwidth of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The bandwidth of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param bandwidth: The bandwidth of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def bandwidth_package_id(self):
        """Gets the bandwidth_package_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The bandwidth_package_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._bandwidth_package_id

    @bandwidth_package_id.setter
    def bandwidth_package_id(self, bandwidth_package_id):
        """Sets the bandwidth_package_id of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param bandwidth_package_id: The bandwidth_package_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._bandwidth_package_id = bandwidth_package_id

    @property
    def billing_type(self):
        """Gets the billing_type of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The billing_type of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param billing_type: The billing_type of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: int
        """

        self._billing_type = billing_type

    @property
    def business_status(self):
        """Gets the business_status of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The business_status of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._business_status

    @business_status.setter
    def business_status(self, business_status):
        """Sets the business_status of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param business_status: The business_status of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._business_status = business_status

    @property
    def creation_time(self):
        """Gets the creation_time of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The creation_time of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param creation_time: The creation_time of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def delete_time(self):
        """Gets the delete_time of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The delete_time of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._delete_time

    @delete_time.setter
    def delete_time(self, delete_time):
        """Sets the delete_time of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param delete_time: The delete_time of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._delete_time = delete_time

    @property
    def isp(self):
        """Gets the isp of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The isp of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param isp: The isp of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._isp = isp

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The instance_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param instance_id: The instance_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_type(self):
        """Gets the instance_type of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The instance_type of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param instance_type: The instance_type of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._instance_type = instance_type

    @property
    def ipv6_address(self):
        """Gets the ipv6_address of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The ipv6_address of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._ipv6_address

    @ipv6_address.setter
    def ipv6_address(self, ipv6_address):
        """Sets the ipv6_address of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param ipv6_address: The ipv6_address of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._ipv6_address = ipv6_address

    @property
    def ipv6_gateway_id(self):
        """Gets the ipv6_gateway_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The ipv6_gateway_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._ipv6_gateway_id

    @ipv6_gateway_id.setter
    def ipv6_gateway_id(self, ipv6_gateway_id):
        """Sets the ipv6_gateway_id of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param ipv6_gateway_id: The ipv6_gateway_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._ipv6_gateway_id = ipv6_gateway_id

    @property
    def lock_reason(self):
        """Gets the lock_reason of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The lock_reason of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._lock_reason

    @lock_reason.setter
    def lock_reason(self, lock_reason):
        """Sets the lock_reason of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param lock_reason: The lock_reason of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._lock_reason = lock_reason

    @property
    def network_type(self):
        """Gets the network_type of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The network_type of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._network_type

    @network_type.setter
    def network_type(self, network_type):
        """Sets the network_type of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param network_type: The network_type of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._network_type = network_type

    @property
    def overdue_time(self):
        """Gets the overdue_time of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The overdue_time of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._overdue_time

    @overdue_time.setter
    def overdue_time(self, overdue_time):
        """Sets the overdue_time of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param overdue_time: The overdue_time of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._overdue_time = overdue_time

    @property
    def project_name(self):
        """Gets the project_name of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The project_name of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param project_name: The project_name of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def request_id(self):
        """Gets the request_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The request_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._request_id

    @request_id.setter
    def request_id(self, request_id):
        """Sets the request_id of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param request_id: The request_id of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._request_id = request_id

    @property
    def service_managed(self):
        """Gets the service_managed of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The service_managed of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: bool
        """
        return self._service_managed

    @service_managed.setter
    def service_managed(self, service_managed):
        """Sets the service_managed of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param service_managed: The service_managed of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: bool
        """

        self._service_managed = service_managed

    @property
    def status(self):
        """Gets the status of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The status of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param status: The status of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The tags of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: list[TagForDescribeIpv6AddressBandwidthAttributesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param tags: The tags of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: list[TagForDescribeIpv6AddressBandwidthAttributesOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501


        :return: The update_time of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DescribeIpv6AddressBandwidthAttributesResponse.


        :param update_time: The update_time of this DescribeIpv6AddressBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeIpv6AddressBandwidthAttributesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeIpv6AddressBandwidthAttributesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeIpv6AddressBandwidthAttributesResponse):
            return True

        return self.to_dict() != other.to_dict()
