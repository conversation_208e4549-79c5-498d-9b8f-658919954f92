# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RouteTableForDescribeSubnetAttributesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'route_table_id': 'str',
        'route_table_type': 'str'
    }

    attribute_map = {
        'route_table_id': 'RouteTableId',
        'route_table_type': 'RouteTableType'
    }

    def __init__(self, route_table_id=None, route_table_type=None, _configuration=None):  # noqa: E501
        """RouteTableForDescribeSubnetAttributesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._route_table_id = None
        self._route_table_type = None
        self.discriminator = None

        if route_table_id is not None:
            self.route_table_id = route_table_id
        if route_table_type is not None:
            self.route_table_type = route_table_type

    @property
    def route_table_id(self):
        """Gets the route_table_id of this RouteTableForDescribeSubnetAttributesOutput.  # noqa: E501


        :return: The route_table_id of this RouteTableForDescribeSubnetAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._route_table_id

    @route_table_id.setter
    def route_table_id(self, route_table_id):
        """Sets the route_table_id of this RouteTableForDescribeSubnetAttributesOutput.


        :param route_table_id: The route_table_id of this RouteTableForDescribeSubnetAttributesOutput.  # noqa: E501
        :type: str
        """

        self._route_table_id = route_table_id

    @property
    def route_table_type(self):
        """Gets the route_table_type of this RouteTableForDescribeSubnetAttributesOutput.  # noqa: E501


        :return: The route_table_type of this RouteTableForDescribeSubnetAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._route_table_type

    @route_table_type.setter
    def route_table_type(self, route_table_type):
        """Sets the route_table_type of this RouteTableForDescribeSubnetAttributesOutput.


        :param route_table_type: The route_table_type of this RouteTableForDescribeSubnetAttributesOutput.  # noqa: E501
        :type: str
        """

        self._route_table_type = route_table_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RouteTableForDescribeSubnetAttributesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RouteTableForDescribeSubnetAttributesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RouteTableForDescribeSubnetAttributesOutput):
            return True

        return self.to_dict() != other.to_dict()
