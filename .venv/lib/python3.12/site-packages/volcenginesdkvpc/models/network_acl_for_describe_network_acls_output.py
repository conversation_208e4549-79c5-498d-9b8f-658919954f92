# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NetworkAclForDescribeNetworkAclsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creation_time': 'str',
        'description': 'str',
        'egress_acl_entries': 'list[EgressAclEntryForDescribeNetworkAclsOutput]',
        'ingress_acl_entries': 'list[IngressAclEntryForDescribeNetworkAclsOutput]',
        'network_acl_id': 'str',
        'network_acl_name': 'str',
        'project_name': 'str',
        'resources': 'list[ResourceForDescribeNetworkAclsOutput]',
        'status': 'str',
        'tags': 'list[TagForDescribeNetworkAclsOutput]',
        'update_time': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'creation_time': 'CreationTime',
        'description': 'Description',
        'egress_acl_entries': 'EgressAclEntries',
        'ingress_acl_entries': 'IngressAclEntries',
        'network_acl_id': 'NetworkAclId',
        'network_acl_name': 'NetworkAclName',
        'project_name': 'ProjectName',
        'resources': 'Resources',
        'status': 'Status',
        'tags': 'Tags',
        'update_time': 'UpdateTime',
        'vpc_id': 'VpcId'
    }

    def __init__(self, creation_time=None, description=None, egress_acl_entries=None, ingress_acl_entries=None, network_acl_id=None, network_acl_name=None, project_name=None, resources=None, status=None, tags=None, update_time=None, vpc_id=None, _configuration=None):  # noqa: E501
        """NetworkAclForDescribeNetworkAclsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creation_time = None
        self._description = None
        self._egress_acl_entries = None
        self._ingress_acl_entries = None
        self._network_acl_id = None
        self._network_acl_name = None
        self._project_name = None
        self._resources = None
        self._status = None
        self._tags = None
        self._update_time = None
        self._vpc_id = None
        self.discriminator = None

        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if egress_acl_entries is not None:
            self.egress_acl_entries = egress_acl_entries
        if ingress_acl_entries is not None:
            self.ingress_acl_entries = ingress_acl_entries
        if network_acl_id is not None:
            self.network_acl_id = network_acl_id
        if network_acl_name is not None:
            self.network_acl_name = network_acl_name
        if project_name is not None:
            self.project_name = project_name
        if resources is not None:
            self.resources = resources
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def creation_time(self):
        """Gets the creation_time of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501


        :return: The creation_time of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this NetworkAclForDescribeNetworkAclsOutput.


        :param creation_time: The creation_time of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501


        :return: The description of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this NetworkAclForDescribeNetworkAclsOutput.


        :param description: The description of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def egress_acl_entries(self):
        """Gets the egress_acl_entries of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501


        :return: The egress_acl_entries of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :rtype: list[EgressAclEntryForDescribeNetworkAclsOutput]
        """
        return self._egress_acl_entries

    @egress_acl_entries.setter
    def egress_acl_entries(self, egress_acl_entries):
        """Sets the egress_acl_entries of this NetworkAclForDescribeNetworkAclsOutput.


        :param egress_acl_entries: The egress_acl_entries of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :type: list[EgressAclEntryForDescribeNetworkAclsOutput]
        """

        self._egress_acl_entries = egress_acl_entries

    @property
    def ingress_acl_entries(self):
        """Gets the ingress_acl_entries of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501


        :return: The ingress_acl_entries of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :rtype: list[IngressAclEntryForDescribeNetworkAclsOutput]
        """
        return self._ingress_acl_entries

    @ingress_acl_entries.setter
    def ingress_acl_entries(self, ingress_acl_entries):
        """Sets the ingress_acl_entries of this NetworkAclForDescribeNetworkAclsOutput.


        :param ingress_acl_entries: The ingress_acl_entries of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :type: list[IngressAclEntryForDescribeNetworkAclsOutput]
        """

        self._ingress_acl_entries = ingress_acl_entries

    @property
    def network_acl_id(self):
        """Gets the network_acl_id of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501


        :return: The network_acl_id of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :rtype: str
        """
        return self._network_acl_id

    @network_acl_id.setter
    def network_acl_id(self, network_acl_id):
        """Sets the network_acl_id of this NetworkAclForDescribeNetworkAclsOutput.


        :param network_acl_id: The network_acl_id of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :type: str
        """

        self._network_acl_id = network_acl_id

    @property
    def network_acl_name(self):
        """Gets the network_acl_name of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501


        :return: The network_acl_name of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :rtype: str
        """
        return self._network_acl_name

    @network_acl_name.setter
    def network_acl_name(self, network_acl_name):
        """Sets the network_acl_name of this NetworkAclForDescribeNetworkAclsOutput.


        :param network_acl_name: The network_acl_name of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :type: str
        """

        self._network_acl_name = network_acl_name

    @property
    def project_name(self):
        """Gets the project_name of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501


        :return: The project_name of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this NetworkAclForDescribeNetworkAclsOutput.


        :param project_name: The project_name of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def resources(self):
        """Gets the resources of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501


        :return: The resources of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :rtype: list[ResourceForDescribeNetworkAclsOutput]
        """
        return self._resources

    @resources.setter
    def resources(self, resources):
        """Sets the resources of this NetworkAclForDescribeNetworkAclsOutput.


        :param resources: The resources of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :type: list[ResourceForDescribeNetworkAclsOutput]
        """

        self._resources = resources

    @property
    def status(self):
        """Gets the status of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501


        :return: The status of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this NetworkAclForDescribeNetworkAclsOutput.


        :param status: The status of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501


        :return: The tags of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :rtype: list[TagForDescribeNetworkAclsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this NetworkAclForDescribeNetworkAclsOutput.


        :param tags: The tags of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :type: list[TagForDescribeNetworkAclsOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501


        :return: The update_time of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this NetworkAclForDescribeNetworkAclsOutput.


        :param update_time: The update_time of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vpc_id(self):
        """Gets the vpc_id of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501


        :return: The vpc_id of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this NetworkAclForDescribeNetworkAclsOutput.


        :param vpc_id: The vpc_id of this NetworkAclForDescribeNetworkAclsOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NetworkAclForDescribeNetworkAclsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NetworkAclForDescribeNetworkAclsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NetworkAclForDescribeNetworkAclsOutput):
            return True

        return self.to_dict() != other.to_dict()
