# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeleteIpv6EgressOnlyRuleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ipv6_egress_only_rule_id': 'str'
    }

    attribute_map = {
        'ipv6_egress_only_rule_id': 'Ipv6EgressOnlyRuleId'
    }

    def __init__(self, ipv6_egress_only_rule_id=None, _configuration=None):  # noqa: E501
        """DeleteIpv6EgressOnlyRuleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ipv6_egress_only_rule_id = None
        self.discriminator = None

        self.ipv6_egress_only_rule_id = ipv6_egress_only_rule_id

    @property
    def ipv6_egress_only_rule_id(self):
        """Gets the ipv6_egress_only_rule_id of this DeleteIpv6EgressOnlyRuleRequest.  # noqa: E501


        :return: The ipv6_egress_only_rule_id of this DeleteIpv6EgressOnlyRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._ipv6_egress_only_rule_id

    @ipv6_egress_only_rule_id.setter
    def ipv6_egress_only_rule_id(self, ipv6_egress_only_rule_id):
        """Sets the ipv6_egress_only_rule_id of this DeleteIpv6EgressOnlyRuleRequest.


        :param ipv6_egress_only_rule_id: The ipv6_egress_only_rule_id of this DeleteIpv6EgressOnlyRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ipv6_egress_only_rule_id is None:
            raise ValueError("Invalid value for `ipv6_egress_only_rule_id`, must not be `None`")  # noqa: E501

        self._ipv6_egress_only_rule_id = ipv6_egress_only_rule_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeleteIpv6EgressOnlyRuleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeleteIpv6EgressOnlyRuleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeleteIpv6EgressOnlyRuleRequest):
            return True

        return self.to_dict() != other.to_dict()
