# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateTrafficMirrorSessionRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'description': 'str',
        'network_interface_id': 'str',
        'packet_length': 'int',
        'priority': 'int',
        'project_name': 'str',
        'tags': 'list[TagForCreateTrafficMirrorSessionInput]',
        'traffic_mirror_filter_id': 'str',
        'traffic_mirror_session_name': 'str',
        'traffic_mirror_target_id': 'str',
        'virtual_network_id': 'int'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'description': 'Description',
        'network_interface_id': 'NetworkInterfaceId',
        'packet_length': 'PacketLength',
        'priority': 'Priority',
        'project_name': 'ProjectName',
        'tags': 'Tags',
        'traffic_mirror_filter_id': 'TrafficMirrorFilterId',
        'traffic_mirror_session_name': 'TrafficMirrorSessionName',
        'traffic_mirror_target_id': 'TrafficMirrorTargetId',
        'virtual_network_id': 'VirtualNetworkId'
    }

    def __init__(self, client_token=None, description=None, network_interface_id=None, packet_length=None, priority=None, project_name=None, tags=None, traffic_mirror_filter_id=None, traffic_mirror_session_name=None, traffic_mirror_target_id=None, virtual_network_id=None, _configuration=None):  # noqa: E501
        """CreateTrafficMirrorSessionRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._description = None
        self._network_interface_id = None
        self._packet_length = None
        self._priority = None
        self._project_name = None
        self._tags = None
        self._traffic_mirror_filter_id = None
        self._traffic_mirror_session_name = None
        self._traffic_mirror_target_id = None
        self._virtual_network_id = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        self.network_interface_id = network_interface_id
        if packet_length is not None:
            self.packet_length = packet_length
        self.priority = priority
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags
        self.traffic_mirror_filter_id = traffic_mirror_filter_id
        if traffic_mirror_session_name is not None:
            self.traffic_mirror_session_name = traffic_mirror_session_name
        self.traffic_mirror_target_id = traffic_mirror_target_id
        if virtual_network_id is not None:
            self.virtual_network_id = virtual_network_id

    @property
    def client_token(self):
        """Gets the client_token of this CreateTrafficMirrorSessionRequest.  # noqa: E501


        :return: The client_token of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateTrafficMirrorSessionRequest.


        :param client_token: The client_token of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateTrafficMirrorSessionRequest.  # noqa: E501


        :return: The description of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateTrafficMirrorSessionRequest.


        :param description: The description of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def network_interface_id(self):
        """Gets the network_interface_id of this CreateTrafficMirrorSessionRequest.  # noqa: E501


        :return: The network_interface_id of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :rtype: str
        """
        return self._network_interface_id

    @network_interface_id.setter
    def network_interface_id(self, network_interface_id):
        """Sets the network_interface_id of this CreateTrafficMirrorSessionRequest.


        :param network_interface_id: The network_interface_id of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and network_interface_id is None:
            raise ValueError("Invalid value for `network_interface_id`, must not be `None`")  # noqa: E501

        self._network_interface_id = network_interface_id

    @property
    def packet_length(self):
        """Gets the packet_length of this CreateTrafficMirrorSessionRequest.  # noqa: E501


        :return: The packet_length of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :rtype: int
        """
        return self._packet_length

    @packet_length.setter
    def packet_length(self, packet_length):
        """Sets the packet_length of this CreateTrafficMirrorSessionRequest.


        :param packet_length: The packet_length of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :type: int
        """

        self._packet_length = packet_length

    @property
    def priority(self):
        """Gets the priority of this CreateTrafficMirrorSessionRequest.  # noqa: E501


        :return: The priority of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this CreateTrafficMirrorSessionRequest.


        :param priority: The priority of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and priority is None:
            raise ValueError("Invalid value for `priority`, must not be `None`")  # noqa: E501

        self._priority = priority

    @property
    def project_name(self):
        """Gets the project_name of this CreateTrafficMirrorSessionRequest.  # noqa: E501


        :return: The project_name of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateTrafficMirrorSessionRequest.


        :param project_name: The project_name of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this CreateTrafficMirrorSessionRequest.  # noqa: E501


        :return: The tags of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :rtype: list[TagForCreateTrafficMirrorSessionInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateTrafficMirrorSessionRequest.


        :param tags: The tags of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :type: list[TagForCreateTrafficMirrorSessionInput]
        """

        self._tags = tags

    @property
    def traffic_mirror_filter_id(self):
        """Gets the traffic_mirror_filter_id of this CreateTrafficMirrorSessionRequest.  # noqa: E501


        :return: The traffic_mirror_filter_id of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :rtype: str
        """
        return self._traffic_mirror_filter_id

    @traffic_mirror_filter_id.setter
    def traffic_mirror_filter_id(self, traffic_mirror_filter_id):
        """Sets the traffic_mirror_filter_id of this CreateTrafficMirrorSessionRequest.


        :param traffic_mirror_filter_id: The traffic_mirror_filter_id of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and traffic_mirror_filter_id is None:
            raise ValueError("Invalid value for `traffic_mirror_filter_id`, must not be `None`")  # noqa: E501

        self._traffic_mirror_filter_id = traffic_mirror_filter_id

    @property
    def traffic_mirror_session_name(self):
        """Gets the traffic_mirror_session_name of this CreateTrafficMirrorSessionRequest.  # noqa: E501


        :return: The traffic_mirror_session_name of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :rtype: str
        """
        return self._traffic_mirror_session_name

    @traffic_mirror_session_name.setter
    def traffic_mirror_session_name(self, traffic_mirror_session_name):
        """Sets the traffic_mirror_session_name of this CreateTrafficMirrorSessionRequest.


        :param traffic_mirror_session_name: The traffic_mirror_session_name of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :type: str
        """

        self._traffic_mirror_session_name = traffic_mirror_session_name

    @property
    def traffic_mirror_target_id(self):
        """Gets the traffic_mirror_target_id of this CreateTrafficMirrorSessionRequest.  # noqa: E501


        :return: The traffic_mirror_target_id of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :rtype: str
        """
        return self._traffic_mirror_target_id

    @traffic_mirror_target_id.setter
    def traffic_mirror_target_id(self, traffic_mirror_target_id):
        """Sets the traffic_mirror_target_id of this CreateTrafficMirrorSessionRequest.


        :param traffic_mirror_target_id: The traffic_mirror_target_id of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and traffic_mirror_target_id is None:
            raise ValueError("Invalid value for `traffic_mirror_target_id`, must not be `None`")  # noqa: E501

        self._traffic_mirror_target_id = traffic_mirror_target_id

    @property
    def virtual_network_id(self):
        """Gets the virtual_network_id of this CreateTrafficMirrorSessionRequest.  # noqa: E501


        :return: The virtual_network_id of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :rtype: int
        """
        return self._virtual_network_id

    @virtual_network_id.setter
    def virtual_network_id(self, virtual_network_id):
        """Sets the virtual_network_id of this CreateTrafficMirrorSessionRequest.


        :param virtual_network_id: The virtual_network_id of this CreateTrafficMirrorSessionRequest.  # noqa: E501
        :type: int
        """

        self._virtual_network_id = virtual_network_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateTrafficMirrorSessionRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateTrafficMirrorSessionRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateTrafficMirrorSessionRequest):
            return True

        return self.to_dict() != other.to_dict()
