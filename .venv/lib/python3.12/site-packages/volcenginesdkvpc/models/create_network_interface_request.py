# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateNetworkInterfaceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'description': 'str',
        'ipv6_address': 'list[str]',
        'ipv6_address_count': 'int',
        'network_interface_name': 'str',
        'port_security_enabled': 'bool',
        'primary_ip_address': 'str',
        'private_ip_address': 'list[str]',
        'project_name': 'str',
        'secondary_private_ip_address_count': 'int',
        'security_group_ids': 'list[str]',
        'subnet_id': 'str',
        'tags': 'list[TagForCreateNetworkInterfaceInput]'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'description': 'Description',
        'ipv6_address': 'Ipv6Address',
        'ipv6_address_count': 'Ipv6AddressCount',
        'network_interface_name': 'NetworkInterfaceName',
        'port_security_enabled': 'PortSecurityEnabled',
        'primary_ip_address': 'PrimaryIpAddress',
        'private_ip_address': 'PrivateIpAddress',
        'project_name': 'ProjectName',
        'secondary_private_ip_address_count': 'SecondaryPrivateIpAddressCount',
        'security_group_ids': 'SecurityGroupIds',
        'subnet_id': 'SubnetId',
        'tags': 'Tags'
    }

    def __init__(self, client_token=None, description=None, ipv6_address=None, ipv6_address_count=None, network_interface_name=None, port_security_enabled=None, primary_ip_address=None, private_ip_address=None, project_name=None, secondary_private_ip_address_count=None, security_group_ids=None, subnet_id=None, tags=None, _configuration=None):  # noqa: E501
        """CreateNetworkInterfaceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._description = None
        self._ipv6_address = None
        self._ipv6_address_count = None
        self._network_interface_name = None
        self._port_security_enabled = None
        self._primary_ip_address = None
        self._private_ip_address = None
        self._project_name = None
        self._secondary_private_ip_address_count = None
        self._security_group_ids = None
        self._subnet_id = None
        self._tags = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        if ipv6_address is not None:
            self.ipv6_address = ipv6_address
        if ipv6_address_count is not None:
            self.ipv6_address_count = ipv6_address_count
        if network_interface_name is not None:
            self.network_interface_name = network_interface_name
        if port_security_enabled is not None:
            self.port_security_enabled = port_security_enabled
        if primary_ip_address is not None:
            self.primary_ip_address = primary_ip_address
        if private_ip_address is not None:
            self.private_ip_address = private_ip_address
        if project_name is not None:
            self.project_name = project_name
        if secondary_private_ip_address_count is not None:
            self.secondary_private_ip_address_count = secondary_private_ip_address_count
        if security_group_ids is not None:
            self.security_group_ids = security_group_ids
        self.subnet_id = subnet_id
        if tags is not None:
            self.tags = tags

    @property
    def client_token(self):
        """Gets the client_token of this CreateNetworkInterfaceRequest.  # noqa: E501


        :return: The client_token of this CreateNetworkInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateNetworkInterfaceRequest.


        :param client_token: The client_token of this CreateNetworkInterfaceRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateNetworkInterfaceRequest.  # noqa: E501


        :return: The description of this CreateNetworkInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateNetworkInterfaceRequest.


        :param description: The description of this CreateNetworkInterfaceRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 255):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `255`")  # noqa: E501
        if (self._configuration.client_side_validation and
                description is not None and len(description) < 1):
            raise ValueError("Invalid value for `description`, length must be greater than or equal to `1`")  # noqa: E501

        self._description = description

    @property
    def ipv6_address(self):
        """Gets the ipv6_address of this CreateNetworkInterfaceRequest.  # noqa: E501


        :return: The ipv6_address of this CreateNetworkInterfaceRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ipv6_address

    @ipv6_address.setter
    def ipv6_address(self, ipv6_address):
        """Sets the ipv6_address of this CreateNetworkInterfaceRequest.


        :param ipv6_address: The ipv6_address of this CreateNetworkInterfaceRequest.  # noqa: E501
        :type: list[str]
        """

        self._ipv6_address = ipv6_address

    @property
    def ipv6_address_count(self):
        """Gets the ipv6_address_count of this CreateNetworkInterfaceRequest.  # noqa: E501


        :return: The ipv6_address_count of this CreateNetworkInterfaceRequest.  # noqa: E501
        :rtype: int
        """
        return self._ipv6_address_count

    @ipv6_address_count.setter
    def ipv6_address_count(self, ipv6_address_count):
        """Sets the ipv6_address_count of this CreateNetworkInterfaceRequest.


        :param ipv6_address_count: The ipv6_address_count of this CreateNetworkInterfaceRequest.  # noqa: E501
        :type: int
        """

        self._ipv6_address_count = ipv6_address_count

    @property
    def network_interface_name(self):
        """Gets the network_interface_name of this CreateNetworkInterfaceRequest.  # noqa: E501


        :return: The network_interface_name of this CreateNetworkInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._network_interface_name

    @network_interface_name.setter
    def network_interface_name(self, network_interface_name):
        """Sets the network_interface_name of this CreateNetworkInterfaceRequest.


        :param network_interface_name: The network_interface_name of this CreateNetworkInterfaceRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                network_interface_name is not None and len(network_interface_name) > 128):
            raise ValueError("Invalid value for `network_interface_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                network_interface_name is not None and len(network_interface_name) < 1):
            raise ValueError("Invalid value for `network_interface_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._network_interface_name = network_interface_name

    @property
    def port_security_enabled(self):
        """Gets the port_security_enabled of this CreateNetworkInterfaceRequest.  # noqa: E501


        :return: The port_security_enabled of this CreateNetworkInterfaceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._port_security_enabled

    @port_security_enabled.setter
    def port_security_enabled(self, port_security_enabled):
        """Sets the port_security_enabled of this CreateNetworkInterfaceRequest.


        :param port_security_enabled: The port_security_enabled of this CreateNetworkInterfaceRequest.  # noqa: E501
        :type: bool
        """

        self._port_security_enabled = port_security_enabled

    @property
    def primary_ip_address(self):
        """Gets the primary_ip_address of this CreateNetworkInterfaceRequest.  # noqa: E501


        :return: The primary_ip_address of this CreateNetworkInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip_address

    @primary_ip_address.setter
    def primary_ip_address(self, primary_ip_address):
        """Sets the primary_ip_address of this CreateNetworkInterfaceRequest.


        :param primary_ip_address: The primary_ip_address of this CreateNetworkInterfaceRequest.  # noqa: E501
        :type: str
        """

        self._primary_ip_address = primary_ip_address

    @property
    def private_ip_address(self):
        """Gets the private_ip_address of this CreateNetworkInterfaceRequest.  # noqa: E501


        :return: The private_ip_address of this CreateNetworkInterfaceRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._private_ip_address

    @private_ip_address.setter
    def private_ip_address(self, private_ip_address):
        """Sets the private_ip_address of this CreateNetworkInterfaceRequest.


        :param private_ip_address: The private_ip_address of this CreateNetworkInterfaceRequest.  # noqa: E501
        :type: list[str]
        """

        self._private_ip_address = private_ip_address

    @property
    def project_name(self):
        """Gets the project_name of this CreateNetworkInterfaceRequest.  # noqa: E501


        :return: The project_name of this CreateNetworkInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateNetworkInterfaceRequest.


        :param project_name: The project_name of this CreateNetworkInterfaceRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def secondary_private_ip_address_count(self):
        """Gets the secondary_private_ip_address_count of this CreateNetworkInterfaceRequest.  # noqa: E501


        :return: The secondary_private_ip_address_count of this CreateNetworkInterfaceRequest.  # noqa: E501
        :rtype: int
        """
        return self._secondary_private_ip_address_count

    @secondary_private_ip_address_count.setter
    def secondary_private_ip_address_count(self, secondary_private_ip_address_count):
        """Sets the secondary_private_ip_address_count of this CreateNetworkInterfaceRequest.


        :param secondary_private_ip_address_count: The secondary_private_ip_address_count of this CreateNetworkInterfaceRequest.  # noqa: E501
        :type: int
        """

        self._secondary_private_ip_address_count = secondary_private_ip_address_count

    @property
    def security_group_ids(self):
        """Gets the security_group_ids of this CreateNetworkInterfaceRequest.  # noqa: E501


        :return: The security_group_ids of this CreateNetworkInterfaceRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_ids

    @security_group_ids.setter
    def security_group_ids(self, security_group_ids):
        """Sets the security_group_ids of this CreateNetworkInterfaceRequest.


        :param security_group_ids: The security_group_ids of this CreateNetworkInterfaceRequest.  # noqa: E501
        :type: list[str]
        """

        self._security_group_ids = security_group_ids

    @property
    def subnet_id(self):
        """Gets the subnet_id of this CreateNetworkInterfaceRequest.  # noqa: E501


        :return: The subnet_id of this CreateNetworkInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this CreateNetworkInterfaceRequest.


        :param subnet_id: The subnet_id of this CreateNetworkInterfaceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and subnet_id is None:
            raise ValueError("Invalid value for `subnet_id`, must not be `None`")  # noqa: E501

        self._subnet_id = subnet_id

    @property
    def tags(self):
        """Gets the tags of this CreateNetworkInterfaceRequest.  # noqa: E501


        :return: The tags of this CreateNetworkInterfaceRequest.  # noqa: E501
        :rtype: list[TagForCreateNetworkInterfaceInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateNetworkInterfaceRequest.


        :param tags: The tags of this CreateNetworkInterfaceRequest.  # noqa: E501
        :type: list[TagForCreateNetworkInterfaceInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateNetworkInterfaceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateNetworkInterfaceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateNetworkInterfaceRequest):
            return True

        return self.to_dict() != other.to_dict()
