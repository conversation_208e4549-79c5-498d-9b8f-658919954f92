# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyFlowLogAttributeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'aggregation_interval': 'int',
        'client_token': 'str',
        'description': 'str',
        'flow_log_id': 'str',
        'flow_log_name': 'str'
    }

    attribute_map = {
        'aggregation_interval': 'AggregationInterval',
        'client_token': 'ClientToken',
        'description': 'Description',
        'flow_log_id': 'FlowLogId',
        'flow_log_name': 'FlowLogName'
    }

    def __init__(self, aggregation_interval=None, client_token=None, description=None, flow_log_id=None, flow_log_name=None, _configuration=None):  # noqa: E501
        """ModifyFlowLogAttributeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._aggregation_interval = None
        self._client_token = None
        self._description = None
        self._flow_log_id = None
        self._flow_log_name = None
        self.discriminator = None

        if aggregation_interval is not None:
            self.aggregation_interval = aggregation_interval
        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        self.flow_log_id = flow_log_id
        if flow_log_name is not None:
            self.flow_log_name = flow_log_name

    @property
    def aggregation_interval(self):
        """Gets the aggregation_interval of this ModifyFlowLogAttributeRequest.  # noqa: E501


        :return: The aggregation_interval of this ModifyFlowLogAttributeRequest.  # noqa: E501
        :rtype: int
        """
        return self._aggregation_interval

    @aggregation_interval.setter
    def aggregation_interval(self, aggregation_interval):
        """Sets the aggregation_interval of this ModifyFlowLogAttributeRequest.


        :param aggregation_interval: The aggregation_interval of this ModifyFlowLogAttributeRequest.  # noqa: E501
        :type: int
        """

        self._aggregation_interval = aggregation_interval

    @property
    def client_token(self):
        """Gets the client_token of this ModifyFlowLogAttributeRequest.  # noqa: E501


        :return: The client_token of this ModifyFlowLogAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this ModifyFlowLogAttributeRequest.


        :param client_token: The client_token of this ModifyFlowLogAttributeRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this ModifyFlowLogAttributeRequest.  # noqa: E501


        :return: The description of this ModifyFlowLogAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyFlowLogAttributeRequest.


        :param description: The description of this ModifyFlowLogAttributeRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 255):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `255`")  # noqa: E501

        self._description = description

    @property
    def flow_log_id(self):
        """Gets the flow_log_id of this ModifyFlowLogAttributeRequest.  # noqa: E501


        :return: The flow_log_id of this ModifyFlowLogAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._flow_log_id

    @flow_log_id.setter
    def flow_log_id(self, flow_log_id):
        """Sets the flow_log_id of this ModifyFlowLogAttributeRequest.


        :param flow_log_id: The flow_log_id of this ModifyFlowLogAttributeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and flow_log_id is None:
            raise ValueError("Invalid value for `flow_log_id`, must not be `None`")  # noqa: E501

        self._flow_log_id = flow_log_id

    @property
    def flow_log_name(self):
        """Gets the flow_log_name of this ModifyFlowLogAttributeRequest.  # noqa: E501


        :return: The flow_log_name of this ModifyFlowLogAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._flow_log_name

    @flow_log_name.setter
    def flow_log_name(self, flow_log_name):
        """Sets the flow_log_name of this ModifyFlowLogAttributeRequest.


        :param flow_log_name: The flow_log_name of this ModifyFlowLogAttributeRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                flow_log_name is not None and len(flow_log_name) > 128):
            raise ValueError("Invalid value for `flow_log_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                flow_log_name is not None and len(flow_log_name) < 1):
            raise ValueError("Invalid value for `flow_log_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._flow_log_name = flow_log_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyFlowLogAttributeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyFlowLogAttributeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyFlowLogAttributeRequest):
            return True

        return self.to_dict() != other.to_dict()
