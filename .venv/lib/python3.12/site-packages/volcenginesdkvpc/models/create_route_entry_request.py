# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateRouteEntryRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'description': 'str',
        'destination_cidr_block': 'str',
        'destination_prefix_list_id': 'str',
        'next_hop_id': 'str',
        'next_hop_name': 'str',
        'next_hop_type': 'str',
        'route_entry_name': 'str',
        'route_table_id': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'description': 'Description',
        'destination_cidr_block': 'DestinationCidrBlock',
        'destination_prefix_list_id': 'DestinationPrefixListId',
        'next_hop_id': 'NextHopId',
        'next_hop_name': 'NextHopName',
        'next_hop_type': 'NextHopType',
        'route_entry_name': 'RouteEntryName',
        'route_table_id': 'RouteTableId'
    }

    def __init__(self, client_token=None, description=None, destination_cidr_block=None, destination_prefix_list_id=None, next_hop_id=None, next_hop_name=None, next_hop_type=None, route_entry_name=None, route_table_id=None, _configuration=None):  # noqa: E501
        """CreateRouteEntryRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._description = None
        self._destination_cidr_block = None
        self._destination_prefix_list_id = None
        self._next_hop_id = None
        self._next_hop_name = None
        self._next_hop_type = None
        self._route_entry_name = None
        self._route_table_id = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        if destination_cidr_block is not None:
            self.destination_cidr_block = destination_cidr_block
        if destination_prefix_list_id is not None:
            self.destination_prefix_list_id = destination_prefix_list_id
        self.next_hop_id = next_hop_id
        if next_hop_name is not None:
            self.next_hop_name = next_hop_name
        self.next_hop_type = next_hop_type
        if route_entry_name is not None:
            self.route_entry_name = route_entry_name
        self.route_table_id = route_table_id

    @property
    def client_token(self):
        """Gets the client_token of this CreateRouteEntryRequest.  # noqa: E501


        :return: The client_token of this CreateRouteEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateRouteEntryRequest.


        :param client_token: The client_token of this CreateRouteEntryRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateRouteEntryRequest.  # noqa: E501


        :return: The description of this CreateRouteEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateRouteEntryRequest.


        :param description: The description of this CreateRouteEntryRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 255):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `255`")  # noqa: E501
        if (self._configuration.client_side_validation and
                description is not None and len(description) < 1):
            raise ValueError("Invalid value for `description`, length must be greater than or equal to `1`")  # noqa: E501

        self._description = description

    @property
    def destination_cidr_block(self):
        """Gets the destination_cidr_block of this CreateRouteEntryRequest.  # noqa: E501


        :return: The destination_cidr_block of this CreateRouteEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._destination_cidr_block

    @destination_cidr_block.setter
    def destination_cidr_block(self, destination_cidr_block):
        """Sets the destination_cidr_block of this CreateRouteEntryRequest.


        :param destination_cidr_block: The destination_cidr_block of this CreateRouteEntryRequest.  # noqa: E501
        :type: str
        """

        self._destination_cidr_block = destination_cidr_block

    @property
    def destination_prefix_list_id(self):
        """Gets the destination_prefix_list_id of this CreateRouteEntryRequest.  # noqa: E501


        :return: The destination_prefix_list_id of this CreateRouteEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._destination_prefix_list_id

    @destination_prefix_list_id.setter
    def destination_prefix_list_id(self, destination_prefix_list_id):
        """Sets the destination_prefix_list_id of this CreateRouteEntryRequest.


        :param destination_prefix_list_id: The destination_prefix_list_id of this CreateRouteEntryRequest.  # noqa: E501
        :type: str
        """

        self._destination_prefix_list_id = destination_prefix_list_id

    @property
    def next_hop_id(self):
        """Gets the next_hop_id of this CreateRouteEntryRequest.  # noqa: E501


        :return: The next_hop_id of this CreateRouteEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_hop_id

    @next_hop_id.setter
    def next_hop_id(self, next_hop_id):
        """Sets the next_hop_id of this CreateRouteEntryRequest.


        :param next_hop_id: The next_hop_id of this CreateRouteEntryRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and next_hop_id is None:
            raise ValueError("Invalid value for `next_hop_id`, must not be `None`")  # noqa: E501

        self._next_hop_id = next_hop_id

    @property
    def next_hop_name(self):
        """Gets the next_hop_name of this CreateRouteEntryRequest.  # noqa: E501


        :return: The next_hop_name of this CreateRouteEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_hop_name

    @next_hop_name.setter
    def next_hop_name(self, next_hop_name):
        """Sets the next_hop_name of this CreateRouteEntryRequest.


        :param next_hop_name: The next_hop_name of this CreateRouteEntryRequest.  # noqa: E501
        :type: str
        """

        self._next_hop_name = next_hop_name

    @property
    def next_hop_type(self):
        """Gets the next_hop_type of this CreateRouteEntryRequest.  # noqa: E501


        :return: The next_hop_type of this CreateRouteEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_hop_type

    @next_hop_type.setter
    def next_hop_type(self, next_hop_type):
        """Sets the next_hop_type of this CreateRouteEntryRequest.


        :param next_hop_type: The next_hop_type of this CreateRouteEntryRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and next_hop_type is None:
            raise ValueError("Invalid value for `next_hop_type`, must not be `None`")  # noqa: E501

        self._next_hop_type = next_hop_type

    @property
    def route_entry_name(self):
        """Gets the route_entry_name of this CreateRouteEntryRequest.  # noqa: E501


        :return: The route_entry_name of this CreateRouteEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._route_entry_name

    @route_entry_name.setter
    def route_entry_name(self, route_entry_name):
        """Sets the route_entry_name of this CreateRouteEntryRequest.


        :param route_entry_name: The route_entry_name of this CreateRouteEntryRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                route_entry_name is not None and len(route_entry_name) > 128):
            raise ValueError("Invalid value for `route_entry_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                route_entry_name is not None and len(route_entry_name) < 1):
            raise ValueError("Invalid value for `route_entry_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._route_entry_name = route_entry_name

    @property
    def route_table_id(self):
        """Gets the route_table_id of this CreateRouteEntryRequest.  # noqa: E501


        :return: The route_table_id of this CreateRouteEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._route_table_id

    @route_table_id.setter
    def route_table_id(self, route_table_id):
        """Sets the route_table_id of this CreateRouteEntryRequest.


        :param route_table_id: The route_table_id of this CreateRouteEntryRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and route_table_id is None:
            raise ValueError("Invalid value for `route_table_id`, must not be `None`")  # noqa: E501

        self._route_table_id = route_table_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateRouteEntryRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateRouteEntryRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateRouteEntryRequest):
            return True

        return self.to_dict() != other.to_dict()
