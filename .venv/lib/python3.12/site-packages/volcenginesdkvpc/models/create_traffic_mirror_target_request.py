# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateTrafficMirrorTargetRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'description': 'str',
        'instance_id': 'str',
        'instance_type': 'str',
        'project_name': 'str',
        'tags': 'list[TagForCreateTrafficMirrorTargetInput]',
        'traffic_mirror_target_name': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'description': 'Description',
        'instance_id': 'InstanceId',
        'instance_type': 'InstanceType',
        'project_name': 'ProjectName',
        'tags': 'Tags',
        'traffic_mirror_target_name': 'TrafficMirrorTargetName'
    }

    def __init__(self, client_token=None, description=None, instance_id=None, instance_type=None, project_name=None, tags=None, traffic_mirror_target_name=None, _configuration=None):  # noqa: E501
        """CreateTrafficMirrorTargetRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._description = None
        self._instance_id = None
        self._instance_type = None
        self._project_name = None
        self._tags = None
        self._traffic_mirror_target_name = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        self.instance_id = instance_id
        self.instance_type = instance_type
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags
        if traffic_mirror_target_name is not None:
            self.traffic_mirror_target_name = traffic_mirror_target_name

    @property
    def client_token(self):
        """Gets the client_token of this CreateTrafficMirrorTargetRequest.  # noqa: E501


        :return: The client_token of this CreateTrafficMirrorTargetRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateTrafficMirrorTargetRequest.


        :param client_token: The client_token of this CreateTrafficMirrorTargetRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateTrafficMirrorTargetRequest.  # noqa: E501


        :return: The description of this CreateTrafficMirrorTargetRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateTrafficMirrorTargetRequest.


        :param description: The description of this CreateTrafficMirrorTargetRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def instance_id(self):
        """Gets the instance_id of this CreateTrafficMirrorTargetRequest.  # noqa: E501


        :return: The instance_id of this CreateTrafficMirrorTargetRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this CreateTrafficMirrorTargetRequest.


        :param instance_id: The instance_id of this CreateTrafficMirrorTargetRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def instance_type(self):
        """Gets the instance_type of this CreateTrafficMirrorTargetRequest.  # noqa: E501


        :return: The instance_type of this CreateTrafficMirrorTargetRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this CreateTrafficMirrorTargetRequest.


        :param instance_type: The instance_type of this CreateTrafficMirrorTargetRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_type is None:
            raise ValueError("Invalid value for `instance_type`, must not be `None`")  # noqa: E501
        allowed_values = ["NetworkInterface", "ClbInstance"]  # noqa: E501
        if (self._configuration.client_side_validation and
                instance_type not in allowed_values):
            raise ValueError(
                "Invalid value for `instance_type` ({0}), must be one of {1}"  # noqa: E501
                .format(instance_type, allowed_values)
            )

        self._instance_type = instance_type

    @property
    def project_name(self):
        """Gets the project_name of this CreateTrafficMirrorTargetRequest.  # noqa: E501


        :return: The project_name of this CreateTrafficMirrorTargetRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateTrafficMirrorTargetRequest.


        :param project_name: The project_name of this CreateTrafficMirrorTargetRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this CreateTrafficMirrorTargetRequest.  # noqa: E501


        :return: The tags of this CreateTrafficMirrorTargetRequest.  # noqa: E501
        :rtype: list[TagForCreateTrafficMirrorTargetInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateTrafficMirrorTargetRequest.


        :param tags: The tags of this CreateTrafficMirrorTargetRequest.  # noqa: E501
        :type: list[TagForCreateTrafficMirrorTargetInput]
        """

        self._tags = tags

    @property
    def traffic_mirror_target_name(self):
        """Gets the traffic_mirror_target_name of this CreateTrafficMirrorTargetRequest.  # noqa: E501


        :return: The traffic_mirror_target_name of this CreateTrafficMirrorTargetRequest.  # noqa: E501
        :rtype: str
        """
        return self._traffic_mirror_target_name

    @traffic_mirror_target_name.setter
    def traffic_mirror_target_name(self, traffic_mirror_target_name):
        """Sets the traffic_mirror_target_name of this CreateTrafficMirrorTargetRequest.


        :param traffic_mirror_target_name: The traffic_mirror_target_name of this CreateTrafficMirrorTargetRequest.  # noqa: E501
        :type: str
        """

        self._traffic_mirror_target_name = traffic_mirror_target_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateTrafficMirrorTargetRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateTrafficMirrorTargetRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateTrafficMirrorTargetRequest):
            return True

        return self.to_dict() != other.to_dict()
