# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeIpAddressPoolsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'isp': 'str',
        'ip_address_pool_ids': 'list[str]',
        'max_results': 'int',
        'next_token': 'str',
        'project_name': 'str',
        'status': 'str',
        'tag_filters': 'list[TagFilterForDescribeIpAddressPoolsInput]'
    }

    attribute_map = {
        'isp': 'ISP',
        'ip_address_pool_ids': 'IpAddressPoolIds',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'project_name': 'ProjectName',
        'status': 'Status',
        'tag_filters': 'TagFilters'
    }

    def __init__(self, isp=None, ip_address_pool_ids=None, max_results=None, next_token=None, project_name=None, status=None, tag_filters=None, _configuration=None):  # noqa: E501
        """DescribeIpAddressPoolsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._isp = None
        self._ip_address_pool_ids = None
        self._max_results = None
        self._next_token = None
        self._project_name = None
        self._status = None
        self._tag_filters = None
        self.discriminator = None

        if isp is not None:
            self.isp = isp
        if ip_address_pool_ids is not None:
            self.ip_address_pool_ids = ip_address_pool_ids
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if project_name is not None:
            self.project_name = project_name
        if status is not None:
            self.status = status
        if tag_filters is not None:
            self.tag_filters = tag_filters

    @property
    def isp(self):
        """Gets the isp of this DescribeIpAddressPoolsRequest.  # noqa: E501


        :return: The isp of this DescribeIpAddressPoolsRequest.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this DescribeIpAddressPoolsRequest.


        :param isp: The isp of this DescribeIpAddressPoolsRequest.  # noqa: E501
        :type: str
        """

        self._isp = isp

    @property
    def ip_address_pool_ids(self):
        """Gets the ip_address_pool_ids of this DescribeIpAddressPoolsRequest.  # noqa: E501


        :return: The ip_address_pool_ids of this DescribeIpAddressPoolsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ip_address_pool_ids

    @ip_address_pool_ids.setter
    def ip_address_pool_ids(self, ip_address_pool_ids):
        """Sets the ip_address_pool_ids of this DescribeIpAddressPoolsRequest.


        :param ip_address_pool_ids: The ip_address_pool_ids of this DescribeIpAddressPoolsRequest.  # noqa: E501
        :type: list[str]
        """

        self._ip_address_pool_ids = ip_address_pool_ids

    @property
    def max_results(self):
        """Gets the max_results of this DescribeIpAddressPoolsRequest.  # noqa: E501


        :return: The max_results of this DescribeIpAddressPoolsRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeIpAddressPoolsRequest.


        :param max_results: The max_results of this DescribeIpAddressPoolsRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeIpAddressPoolsRequest.  # noqa: E501


        :return: The next_token of this DescribeIpAddressPoolsRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeIpAddressPoolsRequest.


        :param next_token: The next_token of this DescribeIpAddressPoolsRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def project_name(self):
        """Gets the project_name of this DescribeIpAddressPoolsRequest.  # noqa: E501


        :return: The project_name of this DescribeIpAddressPoolsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeIpAddressPoolsRequest.


        :param project_name: The project_name of this DescribeIpAddressPoolsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def status(self):
        """Gets the status of this DescribeIpAddressPoolsRequest.  # noqa: E501


        :return: The status of this DescribeIpAddressPoolsRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeIpAddressPoolsRequest.


        :param status: The status of this DescribeIpAddressPoolsRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeIpAddressPoolsRequest.  # noqa: E501


        :return: The tag_filters of this DescribeIpAddressPoolsRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeIpAddressPoolsInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeIpAddressPoolsRequest.


        :param tag_filters: The tag_filters of this DescribeIpAddressPoolsRequest.  # noqa: E501
        :type: list[TagFilterForDescribeIpAddressPoolsInput]
        """

        self._tag_filters = tag_filters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeIpAddressPoolsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeIpAddressPoolsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeIpAddressPoolsRequest):
            return True

        return self.to_dict() != other.to_dict()
