# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'instance_id': 'str',
        'ipv6_egress_only_rule_id': 'str',
        'name': 'str',
        'status': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'instance_id': 'InstanceId',
        'ipv6_egress_only_rule_id': 'Ipv6EgressOnlyRuleId',
        'name': 'Name',
        'status': 'Status'
    }

    def __init__(self, description=None, instance_id=None, ipv6_egress_only_rule_id=None, name=None, status=None, _configuration=None):  # noqa: E501
        """Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._instance_id = None
        self._ipv6_egress_only_rule_id = None
        self._name = None
        self._status = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if instance_id is not None:
            self.instance_id = instance_id
        if ipv6_egress_only_rule_id is not None:
            self.ipv6_egress_only_rule_id = ipv6_egress_only_rule_id
        if name is not None:
            self.name = name
        if status is not None:
            self.status = status

    @property
    def description(self):
        """Gets the description of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501


        :return: The description of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.


        :param description: The description of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def instance_id(self):
        """Gets the instance_id of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501


        :return: The instance_id of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.


        :param instance_id: The instance_id of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def ipv6_egress_only_rule_id(self):
        """Gets the ipv6_egress_only_rule_id of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501


        :return: The ipv6_egress_only_rule_id of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._ipv6_egress_only_rule_id

    @ipv6_egress_only_rule_id.setter
    def ipv6_egress_only_rule_id(self, ipv6_egress_only_rule_id):
        """Sets the ipv6_egress_only_rule_id of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.


        :param ipv6_egress_only_rule_id: The ipv6_egress_only_rule_id of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501
        :type: str
        """

        self._ipv6_egress_only_rule_id = ipv6_egress_only_rule_id

    @property
    def name(self):
        """Gets the name of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501


        :return: The name of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.


        :param name: The name of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def status(self):
        """Gets the status of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501


        :return: The status of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.


        :param status: The status of this Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, Ipv6EgressRuleForDescribeIpv6EgressOnlyRulesOutput):
            return True

        return self.to_dict() != other.to_dict()
