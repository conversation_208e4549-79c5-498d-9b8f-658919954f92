# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyNetworkAclAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'network_acl_id': 'str',
        'network_acl_name': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'network_acl_id': 'NetworkAclId',
        'network_acl_name': 'NetworkAclName'
    }

    def __init__(self, description=None, network_acl_id=None, network_acl_name=None, _configuration=None):  # noqa: E501
        """ModifyNetworkAclAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._network_acl_id = None
        self._network_acl_name = None
        self.discriminator = None

        if description is not None:
            self.description = description
        self.network_acl_id = network_acl_id
        if network_acl_name is not None:
            self.network_acl_name = network_acl_name

    @property
    def description(self):
        """Gets the description of this ModifyNetworkAclAttributesRequest.  # noqa: E501


        :return: The description of this ModifyNetworkAclAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyNetworkAclAttributesRequest.


        :param description: The description of this ModifyNetworkAclAttributesRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 255):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `255`")  # noqa: E501
        if (self._configuration.client_side_validation and
                description is not None and len(description) < 1):
            raise ValueError("Invalid value for `description`, length must be greater than or equal to `1`")  # noqa: E501

        self._description = description

    @property
    def network_acl_id(self):
        """Gets the network_acl_id of this ModifyNetworkAclAttributesRequest.  # noqa: E501


        :return: The network_acl_id of this ModifyNetworkAclAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._network_acl_id

    @network_acl_id.setter
    def network_acl_id(self, network_acl_id):
        """Sets the network_acl_id of this ModifyNetworkAclAttributesRequest.


        :param network_acl_id: The network_acl_id of this ModifyNetworkAclAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and network_acl_id is None:
            raise ValueError("Invalid value for `network_acl_id`, must not be `None`")  # noqa: E501

        self._network_acl_id = network_acl_id

    @property
    def network_acl_name(self):
        """Gets the network_acl_name of this ModifyNetworkAclAttributesRequest.  # noqa: E501


        :return: The network_acl_name of this ModifyNetworkAclAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._network_acl_name

    @network_acl_name.setter
    def network_acl_name(self, network_acl_name):
        """Sets the network_acl_name of this ModifyNetworkAclAttributesRequest.


        :param network_acl_name: The network_acl_name of this ModifyNetworkAclAttributesRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                network_acl_name is not None and len(network_acl_name) > 128):
            raise ValueError("Invalid value for `network_acl_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                network_acl_name is not None and len(network_acl_name) < 1):
            raise ValueError("Invalid value for `network_acl_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._network_acl_name = network_acl_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyNetworkAclAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyNetworkAclAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyNetworkAclAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
