# coding: utf-8

"""
    vpc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeIpv6AddressBandwidthsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allocation_ids': 'list[str]',
        'associated_instance_id': 'str',
        'associated_instance_type': 'str',
        'bandwidth_package_id': 'str',
        'isp': 'str',
        'ipv6_addresses': 'list[str]',
        'max_results': 'int',
        'network_type': 'str',
        'next_token': 'str',
        'project_name': 'str',
        'tag_filters': 'list[TagFilterForDescribeIpv6AddressBandwidthsInput]',
        'vpc_id': 'str'
    }

    attribute_map = {
        'allocation_ids': 'AllocationIds',
        'associated_instance_id': 'AssociatedInstanceId',
        'associated_instance_type': 'AssociatedInstanceType',
        'bandwidth_package_id': 'BandwidthPackageId',
        'isp': 'ISP',
        'ipv6_addresses': 'Ipv6Addresses',
        'max_results': 'MaxResults',
        'network_type': 'NetworkType',
        'next_token': 'NextToken',
        'project_name': 'ProjectName',
        'tag_filters': 'TagFilters',
        'vpc_id': 'VpcId'
    }

    def __init__(self, allocation_ids=None, associated_instance_id=None, associated_instance_type=None, bandwidth_package_id=None, isp=None, ipv6_addresses=None, max_results=None, network_type=None, next_token=None, project_name=None, tag_filters=None, vpc_id=None, _configuration=None):  # noqa: E501
        """DescribeIpv6AddressBandwidthsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allocation_ids = None
        self._associated_instance_id = None
        self._associated_instance_type = None
        self._bandwidth_package_id = None
        self._isp = None
        self._ipv6_addresses = None
        self._max_results = None
        self._network_type = None
        self._next_token = None
        self._project_name = None
        self._tag_filters = None
        self._vpc_id = None
        self.discriminator = None

        if allocation_ids is not None:
            self.allocation_ids = allocation_ids
        if associated_instance_id is not None:
            self.associated_instance_id = associated_instance_id
        if associated_instance_type is not None:
            self.associated_instance_type = associated_instance_type
        if bandwidth_package_id is not None:
            self.bandwidth_package_id = bandwidth_package_id
        if isp is not None:
            self.isp = isp
        if ipv6_addresses is not None:
            self.ipv6_addresses = ipv6_addresses
        if max_results is not None:
            self.max_results = max_results
        if network_type is not None:
            self.network_type = network_type
        if next_token is not None:
            self.next_token = next_token
        if project_name is not None:
            self.project_name = project_name
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def allocation_ids(self):
        """Gets the allocation_ids of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501


        :return: The allocation_ids of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._allocation_ids

    @allocation_ids.setter
    def allocation_ids(self, allocation_ids):
        """Sets the allocation_ids of this DescribeIpv6AddressBandwidthsRequest.


        :param allocation_ids: The allocation_ids of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :type: list[str]
        """

        self._allocation_ids = allocation_ids

    @property
    def associated_instance_id(self):
        """Gets the associated_instance_id of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501


        :return: The associated_instance_id of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :rtype: str
        """
        return self._associated_instance_id

    @associated_instance_id.setter
    def associated_instance_id(self, associated_instance_id):
        """Sets the associated_instance_id of this DescribeIpv6AddressBandwidthsRequest.


        :param associated_instance_id: The associated_instance_id of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :type: str
        """

        self._associated_instance_id = associated_instance_id

    @property
    def associated_instance_type(self):
        """Gets the associated_instance_type of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501


        :return: The associated_instance_type of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :rtype: str
        """
        return self._associated_instance_type

    @associated_instance_type.setter
    def associated_instance_type(self, associated_instance_type):
        """Sets the associated_instance_type of this DescribeIpv6AddressBandwidthsRequest.


        :param associated_instance_type: The associated_instance_type of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :type: str
        """

        self._associated_instance_type = associated_instance_type

    @property
    def bandwidth_package_id(self):
        """Gets the bandwidth_package_id of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501


        :return: The bandwidth_package_id of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :rtype: str
        """
        return self._bandwidth_package_id

    @bandwidth_package_id.setter
    def bandwidth_package_id(self, bandwidth_package_id):
        """Sets the bandwidth_package_id of this DescribeIpv6AddressBandwidthsRequest.


        :param bandwidth_package_id: The bandwidth_package_id of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :type: str
        """

        self._bandwidth_package_id = bandwidth_package_id

    @property
    def isp(self):
        """Gets the isp of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501


        :return: The isp of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this DescribeIpv6AddressBandwidthsRequest.


        :param isp: The isp of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :type: str
        """

        self._isp = isp

    @property
    def ipv6_addresses(self):
        """Gets the ipv6_addresses of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501


        :return: The ipv6_addresses of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ipv6_addresses

    @ipv6_addresses.setter
    def ipv6_addresses(self, ipv6_addresses):
        """Sets the ipv6_addresses of this DescribeIpv6AddressBandwidthsRequest.


        :param ipv6_addresses: The ipv6_addresses of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :type: list[str]
        """

        self._ipv6_addresses = ipv6_addresses

    @property
    def max_results(self):
        """Gets the max_results of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501


        :return: The max_results of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeIpv6AddressBandwidthsRequest.


        :param max_results: The max_results of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def network_type(self):
        """Gets the network_type of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501


        :return: The network_type of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :rtype: str
        """
        return self._network_type

    @network_type.setter
    def network_type(self, network_type):
        """Sets the network_type of this DescribeIpv6AddressBandwidthsRequest.


        :param network_type: The network_type of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :type: str
        """

        self._network_type = network_type

    @property
    def next_token(self):
        """Gets the next_token of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501


        :return: The next_token of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeIpv6AddressBandwidthsRequest.


        :param next_token: The next_token of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def project_name(self):
        """Gets the project_name of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501


        :return: The project_name of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeIpv6AddressBandwidthsRequest.


        :param project_name: The project_name of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501


        :return: The tag_filters of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeIpv6AddressBandwidthsInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeIpv6AddressBandwidthsRequest.


        :param tag_filters: The tag_filters of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :type: list[TagFilterForDescribeIpv6AddressBandwidthsInput]
        """

        self._tag_filters = tag_filters

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501


        :return: The vpc_id of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribeIpv6AddressBandwidthsRequest.


        :param vpc_id: The vpc_id of this DescribeIpv6AddressBandwidthsRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeIpv6AddressBandwidthsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeIpv6AddressBandwidthsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeIpv6AddressBandwidthsRequest):
            return True

        return self.to_dict() != other.to_dict()
