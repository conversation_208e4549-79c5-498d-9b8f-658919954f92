# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetNotebookServerSettingsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'image_id': 'str',
        'mount_tos_enabled': 'bool',
        'resource_size': 'str',
        'storage_capacity': 'int',
        'temp_image_name': 'str'
    }

    attribute_map = {
        'image_id': 'ImageID',
        'mount_tos_enabled': 'MountTOSEnabled',
        'resource_size': 'ResourceSize',
        'storage_capacity': 'StorageCapacity',
        'temp_image_name': 'TempImageName'
    }

    def __init__(self, image_id=None, mount_tos_enabled=None, resource_size=None, storage_capacity=None, temp_image_name=None, _configuration=None):  # noqa: E501
        """GetNotebookServerSettingsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._image_id = None
        self._mount_tos_enabled = None
        self._resource_size = None
        self._storage_capacity = None
        self._temp_image_name = None
        self.discriminator = None

        if image_id is not None:
            self.image_id = image_id
        if mount_tos_enabled is not None:
            self.mount_tos_enabled = mount_tos_enabled
        if resource_size is not None:
            self.resource_size = resource_size
        if storage_capacity is not None:
            self.storage_capacity = storage_capacity
        if temp_image_name is not None:
            self.temp_image_name = temp_image_name

    @property
    def image_id(self):
        """Gets the image_id of this GetNotebookServerSettingsResponse.  # noqa: E501


        :return: The image_id of this GetNotebookServerSettingsResponse.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this GetNotebookServerSettingsResponse.


        :param image_id: The image_id of this GetNotebookServerSettingsResponse.  # noqa: E501
        :type: str
        """

        self._image_id = image_id

    @property
    def mount_tos_enabled(self):
        """Gets the mount_tos_enabled of this GetNotebookServerSettingsResponse.  # noqa: E501


        :return: The mount_tos_enabled of this GetNotebookServerSettingsResponse.  # noqa: E501
        :rtype: bool
        """
        return self._mount_tos_enabled

    @mount_tos_enabled.setter
    def mount_tos_enabled(self, mount_tos_enabled):
        """Sets the mount_tos_enabled of this GetNotebookServerSettingsResponse.


        :param mount_tos_enabled: The mount_tos_enabled of this GetNotebookServerSettingsResponse.  # noqa: E501
        :type: bool
        """

        self._mount_tos_enabled = mount_tos_enabled

    @property
    def resource_size(self):
        """Gets the resource_size of this GetNotebookServerSettingsResponse.  # noqa: E501


        :return: The resource_size of this GetNotebookServerSettingsResponse.  # noqa: E501
        :rtype: str
        """
        return self._resource_size

    @resource_size.setter
    def resource_size(self, resource_size):
        """Sets the resource_size of this GetNotebookServerSettingsResponse.


        :param resource_size: The resource_size of this GetNotebookServerSettingsResponse.  # noqa: E501
        :type: str
        """

        self._resource_size = resource_size

    @property
    def storage_capacity(self):
        """Gets the storage_capacity of this GetNotebookServerSettingsResponse.  # noqa: E501


        :return: The storage_capacity of this GetNotebookServerSettingsResponse.  # noqa: E501
        :rtype: int
        """
        return self._storage_capacity

    @storage_capacity.setter
    def storage_capacity(self, storage_capacity):
        """Sets the storage_capacity of this GetNotebookServerSettingsResponse.


        :param storage_capacity: The storage_capacity of this GetNotebookServerSettingsResponse.  # noqa: E501
        :type: int
        """

        self._storage_capacity = storage_capacity

    @property
    def temp_image_name(self):
        """Gets the temp_image_name of this GetNotebookServerSettingsResponse.  # noqa: E501


        :return: The temp_image_name of this GetNotebookServerSettingsResponse.  # noqa: E501
        :rtype: str
        """
        return self._temp_image_name

    @temp_image_name.setter
    def temp_image_name(self, temp_image_name):
        """Sets the temp_image_name of this GetNotebookServerSettingsResponse.


        :param temp_image_name: The temp_image_name of this GetNotebookServerSettingsResponse.  # noqa: E501
        :type: str
        """

        self._temp_image_name = temp_image_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetNotebookServerSettingsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetNotebookServerSettingsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetNotebookServerSettingsResponse):
            return True

        return self.to_dict() != other.to_dict()
