# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListSubmissionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'cluster_type': 'str',
        'data_entity': 'DataEntityForListSubmissionsOutput',
        'data_model_id': 'str',
        'description': 'str',
        'duration': 'int',
        'exposed_options': 'ExposedOptionsForListSubmissionsOutput',
        'finish_time': 'int',
        'id': 'str',
        'inputs': 'str',
        'name': 'str',
        'outputs': 'str',
        'owner_name': 'str',
        'run_status': 'RunStatusForListSubmissionsOutput',
        'start_time': 'int',
        'status': 'str',
        'workflow_id': 'str'
    }

    attribute_map = {
        'cluster_id': 'ClusterID',
        'cluster_type': 'ClusterType',
        'data_entity': 'DataEntity',
        'data_model_id': 'DataModelID',
        'description': 'Description',
        'duration': 'Duration',
        'exposed_options': 'ExposedOptions',
        'finish_time': 'FinishTime',
        'id': 'ID',
        'inputs': 'Inputs',
        'name': 'Name',
        'outputs': 'Outputs',
        'owner_name': 'OwnerName',
        'run_status': 'RunStatus',
        'start_time': 'StartTime',
        'status': 'Status',
        'workflow_id': 'WorkflowID'
    }

    def __init__(self, cluster_id=None, cluster_type=None, data_entity=None, data_model_id=None, description=None, duration=None, exposed_options=None, finish_time=None, id=None, inputs=None, name=None, outputs=None, owner_name=None, run_status=None, start_time=None, status=None, workflow_id=None, _configuration=None):  # noqa: E501
        """ItemForListSubmissionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._cluster_type = None
        self._data_entity = None
        self._data_model_id = None
        self._description = None
        self._duration = None
        self._exposed_options = None
        self._finish_time = None
        self._id = None
        self._inputs = None
        self._name = None
        self._outputs = None
        self._owner_name = None
        self._run_status = None
        self._start_time = None
        self._status = None
        self._workflow_id = None
        self.discriminator = None

        if cluster_id is not None:
            self.cluster_id = cluster_id
        if cluster_type is not None:
            self.cluster_type = cluster_type
        if data_entity is not None:
            self.data_entity = data_entity
        if data_model_id is not None:
            self.data_model_id = data_model_id
        if description is not None:
            self.description = description
        if duration is not None:
            self.duration = duration
        if exposed_options is not None:
            self.exposed_options = exposed_options
        if finish_time is not None:
            self.finish_time = finish_time
        if id is not None:
            self.id = id
        if inputs is not None:
            self.inputs = inputs
        if name is not None:
            self.name = name
        if outputs is not None:
            self.outputs = outputs
        if owner_name is not None:
            self.owner_name = owner_name
        if run_status is not None:
            self.run_status = run_status
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if workflow_id is not None:
            self.workflow_id = workflow_id

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The cluster_id of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ItemForListSubmissionsOutput.


        :param cluster_id: The cluster_id of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def cluster_type(self):
        """Gets the cluster_type of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The cluster_type of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_type

    @cluster_type.setter
    def cluster_type(self, cluster_type):
        """Sets the cluster_type of this ItemForListSubmissionsOutput.


        :param cluster_type: The cluster_type of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_type = cluster_type

    @property
    def data_entity(self):
        """Gets the data_entity of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The data_entity of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: DataEntityForListSubmissionsOutput
        """
        return self._data_entity

    @data_entity.setter
    def data_entity(self, data_entity):
        """Sets the data_entity of this ItemForListSubmissionsOutput.


        :param data_entity: The data_entity of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: DataEntityForListSubmissionsOutput
        """

        self._data_entity = data_entity

    @property
    def data_model_id(self):
        """Gets the data_model_id of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The data_model_id of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_model_id

    @data_model_id.setter
    def data_model_id(self, data_model_id):
        """Sets the data_model_id of this ItemForListSubmissionsOutput.


        :param data_model_id: The data_model_id of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: str
        """

        self._data_model_id = data_model_id

    @property
    def description(self):
        """Gets the description of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The description of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListSubmissionsOutput.


        :param description: The description of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def duration(self):
        """Gets the duration of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The duration of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this ItemForListSubmissionsOutput.


        :param duration: The duration of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: int
        """

        self._duration = duration

    @property
    def exposed_options(self):
        """Gets the exposed_options of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The exposed_options of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: ExposedOptionsForListSubmissionsOutput
        """
        return self._exposed_options

    @exposed_options.setter
    def exposed_options(self, exposed_options):
        """Sets the exposed_options of this ItemForListSubmissionsOutput.


        :param exposed_options: The exposed_options of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: ExposedOptionsForListSubmissionsOutput
        """

        self._exposed_options = exposed_options

    @property
    def finish_time(self):
        """Gets the finish_time of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The finish_time of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._finish_time

    @finish_time.setter
    def finish_time(self, finish_time):
        """Sets the finish_time of this ItemForListSubmissionsOutput.


        :param finish_time: The finish_time of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: int
        """

        self._finish_time = finish_time

    @property
    def id(self):
        """Gets the id of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The id of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListSubmissionsOutput.


        :param id: The id of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def inputs(self):
        """Gets the inputs of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The inputs of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._inputs

    @inputs.setter
    def inputs(self, inputs):
        """Sets the inputs of this ItemForListSubmissionsOutput.


        :param inputs: The inputs of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: str
        """

        self._inputs = inputs

    @property
    def name(self):
        """Gets the name of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The name of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListSubmissionsOutput.


        :param name: The name of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def outputs(self):
        """Gets the outputs of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The outputs of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._outputs

    @outputs.setter
    def outputs(self, outputs):
        """Sets the outputs of this ItemForListSubmissionsOutput.


        :param outputs: The outputs of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: str
        """

        self._outputs = outputs

    @property
    def owner_name(self):
        """Gets the owner_name of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The owner_name of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_name

    @owner_name.setter
    def owner_name(self, owner_name):
        """Sets the owner_name of this ItemForListSubmissionsOutput.


        :param owner_name: The owner_name of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: str
        """

        self._owner_name = owner_name

    @property
    def run_status(self):
        """Gets the run_status of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The run_status of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: RunStatusForListSubmissionsOutput
        """
        return self._run_status

    @run_status.setter
    def run_status(self, run_status):
        """Sets the run_status of this ItemForListSubmissionsOutput.


        :param run_status: The run_status of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: RunStatusForListSubmissionsOutput
        """

        self._run_status = run_status

    @property
    def start_time(self):
        """Gets the start_time of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The start_time of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this ItemForListSubmissionsOutput.


        :param start_time: The start_time of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The status of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListSubmissionsOutput.


        :param status: The status of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def workflow_id(self):
        """Gets the workflow_id of this ItemForListSubmissionsOutput.  # noqa: E501


        :return: The workflow_id of this ItemForListSubmissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._workflow_id

    @workflow_id.setter
    def workflow_id(self, workflow_id):
        """Sets the workflow_id of this ItemForListSubmissionsOutput.


        :param workflow_id: The workflow_id of this ItemForListSubmissionsOutput.  # noqa: E501
        :type: str
        """

        self._workflow_id = workflow_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListSubmissionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListSubmissionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListSubmissionsOutput):
            return True

        return self.to_dict() != other.to_dict()
