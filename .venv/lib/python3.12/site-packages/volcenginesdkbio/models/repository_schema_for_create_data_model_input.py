# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RepositorySchemaForCreateDataModelInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'aai_passport': 'str',
        'schema_id': 'str'
    }

    attribute_map = {
        'aai_passport': 'AAIPassport',
        'schema_id': 'SchemaID'
    }

    def __init__(self, aai_passport=None, schema_id=None, _configuration=None):  # noqa: E501
        """RepositorySchemaForCreateDataModelInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._aai_passport = None
        self._schema_id = None
        self.discriminator = None

        if aai_passport is not None:
            self.aai_passport = aai_passport
        if schema_id is not None:
            self.schema_id = schema_id

    @property
    def aai_passport(self):
        """Gets the aai_passport of this RepositorySchemaForCreateDataModelInput.  # noqa: E501


        :return: The aai_passport of this RepositorySchemaForCreateDataModelInput.  # noqa: E501
        :rtype: str
        """
        return self._aai_passport

    @aai_passport.setter
    def aai_passport(self, aai_passport):
        """Sets the aai_passport of this RepositorySchemaForCreateDataModelInput.


        :param aai_passport: The aai_passport of this RepositorySchemaForCreateDataModelInput.  # noqa: E501
        :type: str
        """

        self._aai_passport = aai_passport

    @property
    def schema_id(self):
        """Gets the schema_id of this RepositorySchemaForCreateDataModelInput.  # noqa: E501


        :return: The schema_id of this RepositorySchemaForCreateDataModelInput.  # noqa: E501
        :rtype: str
        """
        return self._schema_id

    @schema_id.setter
    def schema_id(self, schema_id):
        """Sets the schema_id of this RepositorySchemaForCreateDataModelInput.


        :param schema_id: The schema_id of this RepositorySchemaForCreateDataModelInput.  # noqa: E501
        :type: str
        """

        self._schema_id = schema_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RepositorySchemaForCreateDataModelInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RepositorySchemaForCreateDataModelInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RepositorySchemaForCreateDataModelInput):
            return True

        return self.to_dict() != other.to_dict()
