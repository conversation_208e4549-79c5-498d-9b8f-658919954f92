# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDataModelRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'headers': 'list[str]',
        'name': 'str',
        'repository_schema': 'RepositorySchemaForCreateDataModelInput',
        'rows': 'list[RowForCreateDataModelInput]',
        'workspace_id': 'str'
    }

    attribute_map = {
        'headers': 'Headers',
        'name': 'Name',
        'repository_schema': 'RepositorySchema',
        'rows': 'Rows',
        'workspace_id': 'WorkspaceID'
    }

    def __init__(self, headers=None, name=None, repository_schema=None, rows=None, workspace_id=None, _configuration=None):  # noqa: E501
        """CreateDataModelRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._headers = None
        self._name = None
        self._repository_schema = None
        self._rows = None
        self._workspace_id = None
        self.discriminator = None

        if headers is not None:
            self.headers = headers
        self.name = name
        if repository_schema is not None:
            self.repository_schema = repository_schema
        if rows is not None:
            self.rows = rows
        self.workspace_id = workspace_id

    @property
    def headers(self):
        """Gets the headers of this CreateDataModelRequest.  # noqa: E501


        :return: The headers of this CreateDataModelRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._headers

    @headers.setter
    def headers(self, headers):
        """Sets the headers of this CreateDataModelRequest.


        :param headers: The headers of this CreateDataModelRequest.  # noqa: E501
        :type: list[str]
        """

        self._headers = headers

    @property
    def name(self):
        """Gets the name of this CreateDataModelRequest.  # noqa: E501


        :return: The name of this CreateDataModelRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateDataModelRequest.


        :param name: The name of this CreateDataModelRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                name is not None and len(name) > 30):
            raise ValueError("Invalid value for `name`, length must be less than or equal to `30`")  # noqa: E501
        if (self._configuration.client_side_validation and
                name is not None and len(name) < 1):
            raise ValueError("Invalid value for `name`, length must be greater than or equal to `1`")  # noqa: E501

        self._name = name

    @property
    def repository_schema(self):
        """Gets the repository_schema of this CreateDataModelRequest.  # noqa: E501


        :return: The repository_schema of this CreateDataModelRequest.  # noqa: E501
        :rtype: RepositorySchemaForCreateDataModelInput
        """
        return self._repository_schema

    @repository_schema.setter
    def repository_schema(self, repository_schema):
        """Sets the repository_schema of this CreateDataModelRequest.


        :param repository_schema: The repository_schema of this CreateDataModelRequest.  # noqa: E501
        :type: RepositorySchemaForCreateDataModelInput
        """

        self._repository_schema = repository_schema

    @property
    def rows(self):
        """Gets the rows of this CreateDataModelRequest.  # noqa: E501


        :return: The rows of this CreateDataModelRequest.  # noqa: E501
        :rtype: list[RowForCreateDataModelInput]
        """
        return self._rows

    @rows.setter
    def rows(self, rows):
        """Sets the rows of this CreateDataModelRequest.


        :param rows: The rows of this CreateDataModelRequest.  # noqa: E501
        :type: list[RowForCreateDataModelInput]
        """

        self._rows = rows

    @property
    def workspace_id(self):
        """Gets the workspace_id of this CreateDataModelRequest.  # noqa: E501


        :return: The workspace_id of this CreateDataModelRequest.  # noqa: E501
        :rtype: str
        """
        return self._workspace_id

    @workspace_id.setter
    def workspace_id(self, workspace_id):
        """Sets the workspace_id of this CreateDataModelRequest.


        :param workspace_id: The workspace_id of this CreateDataModelRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and workspace_id is None:
            raise ValueError("Invalid value for `workspace_id`, must not be `None`")  # noqa: E501

        self._workspace_id = workspace_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDataModelRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDataModelRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDataModelRequest):
            return True

        return self.to_dict() != other.to_dict()
