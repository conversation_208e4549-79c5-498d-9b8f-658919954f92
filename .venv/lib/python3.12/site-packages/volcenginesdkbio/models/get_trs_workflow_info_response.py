# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetTRSWorkflowInfoResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'name': 'str',
        'versions_info': 'list[VersionsInfoForGetTRSWorkflowInfoOutput]'
    }

    attribute_map = {
        'description': 'Description',
        'name': 'Name',
        'versions_info': 'VersionsInfo'
    }

    def __init__(self, description=None, name=None, versions_info=None, _configuration=None):  # noqa: E501
        """GetTRSWorkflowInfoResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._name = None
        self._versions_info = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if name is not None:
            self.name = name
        if versions_info is not None:
            self.versions_info = versions_info

    @property
    def description(self):
        """Gets the description of this GetTRSWorkflowInfoResponse.  # noqa: E501


        :return: The description of this GetTRSWorkflowInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetTRSWorkflowInfoResponse.


        :param description: The description of this GetTRSWorkflowInfoResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def name(self):
        """Gets the name of this GetTRSWorkflowInfoResponse.  # noqa: E501


        :return: The name of this GetTRSWorkflowInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this GetTRSWorkflowInfoResponse.


        :param name: The name of this GetTRSWorkflowInfoResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def versions_info(self):
        """Gets the versions_info of this GetTRSWorkflowInfoResponse.  # noqa: E501


        :return: The versions_info of this GetTRSWorkflowInfoResponse.  # noqa: E501
        :rtype: list[VersionsInfoForGetTRSWorkflowInfoOutput]
        """
        return self._versions_info

    @versions_info.setter
    def versions_info(self, versions_info):
        """Sets the versions_info of this GetTRSWorkflowInfoResponse.


        :param versions_info: The versions_info of this GetTRSWorkflowInfoResponse.  # noqa: E501
        :type: list[VersionsInfoForGetTRSWorkflowInfoOutput]
        """

        self._versions_info = versions_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetTRSWorkflowInfoResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetTRSWorkflowInfoResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetTRSWorkflowInfoResponse):
            return True

        return self.to_dict() != other.to_dict()
