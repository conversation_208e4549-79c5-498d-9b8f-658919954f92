# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class S3ProxyConfigForListClustersOfWorkspaceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_key': 'str',
        'endpoint': 'str',
        'force_path_style': 'bool',
        'region': 'str',
        'secret_key': 'str'
    }

    attribute_map = {
        'access_key': 'AccessKey',
        'endpoint': 'Endpoint',
        'force_path_style': 'ForcePathStyle',
        'region': 'Region',
        'secret_key': 'SecretKey'
    }

    def __init__(self, access_key=None, endpoint=None, force_path_style=None, region=None, secret_key=None, _configuration=None):  # noqa: E501
        """S3ProxyConfigForListClustersOfWorkspaceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_key = None
        self._endpoint = None
        self._force_path_style = None
        self._region = None
        self._secret_key = None
        self.discriminator = None

        if access_key is not None:
            self.access_key = access_key
        if endpoint is not None:
            self.endpoint = endpoint
        if force_path_style is not None:
            self.force_path_style = force_path_style
        if region is not None:
            self.region = region
        if secret_key is not None:
            self.secret_key = secret_key

    @property
    def access_key(self):
        """Gets the access_key of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The access_key of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._access_key

    @access_key.setter
    def access_key(self, access_key):
        """Sets the access_key of this S3ProxyConfigForListClustersOfWorkspaceOutput.


        :param access_key: The access_key of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: str
        """

        self._access_key = access_key

    @property
    def endpoint(self):
        """Gets the endpoint of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The endpoint of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint

    @endpoint.setter
    def endpoint(self, endpoint):
        """Sets the endpoint of this S3ProxyConfigForListClustersOfWorkspaceOutput.


        :param endpoint: The endpoint of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: str
        """

        self._endpoint = endpoint

    @property
    def force_path_style(self):
        """Gets the force_path_style of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The force_path_style of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: bool
        """
        return self._force_path_style

    @force_path_style.setter
    def force_path_style(self, force_path_style):
        """Sets the force_path_style of this S3ProxyConfigForListClustersOfWorkspaceOutput.


        :param force_path_style: The force_path_style of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: bool
        """

        self._force_path_style = force_path_style

    @property
    def region(self):
        """Gets the region of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The region of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this S3ProxyConfigForListClustersOfWorkspaceOutput.


        :param region: The region of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def secret_key(self):
        """Gets the secret_key of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The secret_key of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._secret_key

    @secret_key.setter
    def secret_key(self, secret_key):
        """Sets the secret_key of this S3ProxyConfigForListClustersOfWorkspaceOutput.


        :param secret_key: The secret_key of this S3ProxyConfigForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: str
        """

        self._secret_key = secret_key

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(S3ProxyConfigForListClustersOfWorkspaceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, S3ProxyConfigForListClustersOfWorkspaceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, S3ProxyConfigForListClustersOfWorkspaceOutput):
            return True

        return self.to_dict() != other.to_dict()
