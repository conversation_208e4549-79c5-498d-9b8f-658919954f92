# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetNotebookRunStatusResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'exit_code': 'int',
        'log': 'str',
        'status': 'str'
    }

    attribute_map = {
        'exit_code': 'ExitCode',
        'log': 'Log',
        'status': 'Status'
    }

    def __init__(self, exit_code=None, log=None, status=None, _configuration=None):  # noqa: E501
        """GetNotebookRunStatusResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._exit_code = None
        self._log = None
        self._status = None
        self.discriminator = None

        if exit_code is not None:
            self.exit_code = exit_code
        if log is not None:
            self.log = log
        if status is not None:
            self.status = status

    @property
    def exit_code(self):
        """Gets the exit_code of this GetNotebookRunStatusResponse.  # noqa: E501


        :return: The exit_code of this GetNotebookRunStatusResponse.  # noqa: E501
        :rtype: int
        """
        return self._exit_code

    @exit_code.setter
    def exit_code(self, exit_code):
        """Sets the exit_code of this GetNotebookRunStatusResponse.


        :param exit_code: The exit_code of this GetNotebookRunStatusResponse.  # noqa: E501
        :type: int
        """

        self._exit_code = exit_code

    @property
    def log(self):
        """Gets the log of this GetNotebookRunStatusResponse.  # noqa: E501


        :return: The log of this GetNotebookRunStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._log

    @log.setter
    def log(self, log):
        """Sets the log of this GetNotebookRunStatusResponse.


        :param log: The log of this GetNotebookRunStatusResponse.  # noqa: E501
        :type: str
        """

        self._log = log

    @property
    def status(self):
        """Gets the status of this GetNotebookRunStatusResponse.  # noqa: E501


        :return: The status of this GetNotebookRunStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetNotebookRunStatusResponse.


        :param status: The status of this GetNotebookRunStatusResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetNotebookRunStatusResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetNotebookRunStatusResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetNotebookRunStatusResponse):
            return True

        return self.to_dict() != other.to_dict()
