# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListWorkspaceLabelsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'exact': 'bool',
        'is_public': 'bool',
        'keywords': 'list[str]',
        'match_preset': 'bool'
    }

    attribute_map = {
        'exact': 'Exact',
        'is_public': 'IsPublic',
        'keywords': 'Keywords',
        'match_preset': 'MatchPreset'
    }

    def __init__(self, exact=None, is_public=None, keywords=None, match_preset=None, _configuration=None):  # noqa: E501
        """FilterForListWorkspaceLabelsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._exact = None
        self._is_public = None
        self._keywords = None
        self._match_preset = None
        self.discriminator = None

        if exact is not None:
            self.exact = exact
        if is_public is not None:
            self.is_public = is_public
        if keywords is not None:
            self.keywords = keywords
        if match_preset is not None:
            self.match_preset = match_preset

    @property
    def exact(self):
        """Gets the exact of this FilterForListWorkspaceLabelsInput.  # noqa: E501


        :return: The exact of this FilterForListWorkspaceLabelsInput.  # noqa: E501
        :rtype: bool
        """
        return self._exact

    @exact.setter
    def exact(self, exact):
        """Sets the exact of this FilterForListWorkspaceLabelsInput.


        :param exact: The exact of this FilterForListWorkspaceLabelsInput.  # noqa: E501
        :type: bool
        """

        self._exact = exact

    @property
    def is_public(self):
        """Gets the is_public of this FilterForListWorkspaceLabelsInput.  # noqa: E501


        :return: The is_public of this FilterForListWorkspaceLabelsInput.  # noqa: E501
        :rtype: bool
        """
        return self._is_public

    @is_public.setter
    def is_public(self, is_public):
        """Sets the is_public of this FilterForListWorkspaceLabelsInput.


        :param is_public: The is_public of this FilterForListWorkspaceLabelsInput.  # noqa: E501
        :type: bool
        """

        self._is_public = is_public

    @property
    def keywords(self):
        """Gets the keywords of this FilterForListWorkspaceLabelsInput.  # noqa: E501


        :return: The keywords of this FilterForListWorkspaceLabelsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._keywords

    @keywords.setter
    def keywords(self, keywords):
        """Sets the keywords of this FilterForListWorkspaceLabelsInput.


        :param keywords: The keywords of this FilterForListWorkspaceLabelsInput.  # noqa: E501
        :type: list[str]
        """

        self._keywords = keywords

    @property
    def match_preset(self):
        """Gets the match_preset of this FilterForListWorkspaceLabelsInput.  # noqa: E501


        :return: The match_preset of this FilterForListWorkspaceLabelsInput.  # noqa: E501
        :rtype: bool
        """
        return self._match_preset

    @match_preset.setter
    def match_preset(self, match_preset):
        """Sets the match_preset of this FilterForListWorkspaceLabelsInput.


        :param match_preset: The match_preset of this FilterForListWorkspaceLabelsInput.  # noqa: E501
        :type: bool
        """

        self._match_preset = match_preset

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListWorkspaceLabelsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListWorkspaceLabelsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListWorkspaceLabelsInput):
            return True

        return self.to_dict() != other.to_dict()
