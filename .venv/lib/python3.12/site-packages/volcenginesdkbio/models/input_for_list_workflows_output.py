# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InputForListWorkflowsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'default': 'str',
        'name': 'str',
        'optional': 'bool',
        'parameter_meta': 'str',
        'type': 'str'
    }

    attribute_map = {
        'default': 'Default',
        'name': 'Name',
        'optional': 'Optional',
        'parameter_meta': 'ParameterMeta',
        'type': 'Type'
    }

    def __init__(self, default=None, name=None, optional=None, parameter_meta=None, type=None, _configuration=None):  # noqa: E501
        """InputForListWorkflowsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._default = None
        self._name = None
        self._optional = None
        self._parameter_meta = None
        self._type = None
        self.discriminator = None

        if default is not None:
            self.default = default
        if name is not None:
            self.name = name
        if optional is not None:
            self.optional = optional
        if parameter_meta is not None:
            self.parameter_meta = parameter_meta
        if type is not None:
            self.type = type

    @property
    def default(self):
        """Gets the default of this InputForListWorkflowsOutput.  # noqa: E501


        :return: The default of this InputForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._default

    @default.setter
    def default(self, default):
        """Sets the default of this InputForListWorkflowsOutput.


        :param default: The default of this InputForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._default = default

    @property
    def name(self):
        """Gets the name of this InputForListWorkflowsOutput.  # noqa: E501


        :return: The name of this InputForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this InputForListWorkflowsOutput.


        :param name: The name of this InputForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def optional(self):
        """Gets the optional of this InputForListWorkflowsOutput.  # noqa: E501


        :return: The optional of this InputForListWorkflowsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._optional

    @optional.setter
    def optional(self, optional):
        """Sets the optional of this InputForListWorkflowsOutput.


        :param optional: The optional of this InputForListWorkflowsOutput.  # noqa: E501
        :type: bool
        """

        self._optional = optional

    @property
    def parameter_meta(self):
        """Gets the parameter_meta of this InputForListWorkflowsOutput.  # noqa: E501


        :return: The parameter_meta of this InputForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._parameter_meta

    @parameter_meta.setter
    def parameter_meta(self, parameter_meta):
        """Sets the parameter_meta of this InputForListWorkflowsOutput.


        :param parameter_meta: The parameter_meta of this InputForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._parameter_meta = parameter_meta

    @property
    def type(self):
        """Gets the type of this InputForListWorkflowsOutput.  # noqa: E501


        :return: The type of this InputForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this InputForListWorkflowsOutput.


        :param type: The type of this InputForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InputForListWorkflowsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InputForListWorkflowsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InputForListWorkflowsOutput):
            return True

        return self.to_dict() != other.to_dict()
