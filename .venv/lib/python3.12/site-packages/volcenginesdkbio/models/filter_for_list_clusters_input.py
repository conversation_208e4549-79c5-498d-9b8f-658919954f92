# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListClustersInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ids': 'list[str]',
        'keyword': 'str',
        'public': 'bool',
        'status': 'list[str]',
        'type': 'list[str]'
    }

    attribute_map = {
        'ids': 'IDs',
        'keyword': 'Keyword',
        'public': 'Public',
        'status': 'Status',
        'type': 'Type'
    }

    def __init__(self, ids=None, keyword=None, public=None, status=None, type=None, _configuration=None):  # noqa: E501
        """FilterForListClustersInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ids = None
        self._keyword = None
        self._public = None
        self._status = None
        self._type = None
        self.discriminator = None

        if ids is not None:
            self.ids = ids
        if keyword is not None:
            self.keyword = keyword
        if public is not None:
            self.public = public
        if status is not None:
            self.status = status
        if type is not None:
            self.type = type

    @property
    def ids(self):
        """Gets the ids of this FilterForListClustersInput.  # noqa: E501


        :return: The ids of this FilterForListClustersInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListClustersInput.


        :param ids: The ids of this FilterForListClustersInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def keyword(self):
        """Gets the keyword of this FilterForListClustersInput.  # noqa: E501


        :return: The keyword of this FilterForListClustersInput.  # noqa: E501
        :rtype: str
        """
        return self._keyword

    @keyword.setter
    def keyword(self, keyword):
        """Sets the keyword of this FilterForListClustersInput.


        :param keyword: The keyword of this FilterForListClustersInput.  # noqa: E501
        :type: str
        """

        self._keyword = keyword

    @property
    def public(self):
        """Gets the public of this FilterForListClustersInput.  # noqa: E501


        :return: The public of this FilterForListClustersInput.  # noqa: E501
        :rtype: bool
        """
        return self._public

    @public.setter
    def public(self, public):
        """Sets the public of this FilterForListClustersInput.


        :param public: The public of this FilterForListClustersInput.  # noqa: E501
        :type: bool
        """

        self._public = public

    @property
    def status(self):
        """Gets the status of this FilterForListClustersInput.  # noqa: E501


        :return: The status of this FilterForListClustersInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this FilterForListClustersInput.


        :param status: The status of this FilterForListClustersInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["Creating", "CreateFailed", "Running", "Error", "Deleting", "DeleteFailed", "Updating", "Stopped"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(status).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `status` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(status) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._status = status

    @property
    def type(self):
        """Gets the type of this FilterForListClustersInput.  # noqa: E501


        :return: The type of this FilterForListClustersInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this FilterForListClustersInput.


        :param type: The type of this FilterForListClustersInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["volc-vke", "external", "shared"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(type).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `type` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(type) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListClustersInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListClustersInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListClustersInput):
            return True

        return self.to_dict() != other.to_dict()
