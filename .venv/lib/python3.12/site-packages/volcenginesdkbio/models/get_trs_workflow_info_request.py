# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetTRSWorkflowInfoRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'trs_server': 'str'
    }

    attribute_map = {
        'id': 'ID',
        'trs_server': 'TRSServer'
    }

    def __init__(self, id=None, trs_server=None, _configuration=None):  # noqa: E501
        """GetTRSWorkflowInfoRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._trs_server = None
        self.discriminator = None

        self.id = id
        self.trs_server = trs_server

    @property
    def id(self):
        """Gets the id of this GetTRSWorkflowInfoRequest.  # noqa: E501


        :return: The id of this GetTRSWorkflowInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetTRSWorkflowInfoRequest.


        :param id: The id of this GetTRSWorkflowInfoRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def trs_server(self):
        """Gets the trs_server of this GetTRSWorkflowInfoRequest.  # noqa: E501


        :return: The trs_server of this GetTRSWorkflowInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._trs_server

    @trs_server.setter
    def trs_server(self, trs_server):
        """Sets the trs_server of this GetTRSWorkflowInfoRequest.


        :param trs_server: The trs_server of this GetTRSWorkflowInfoRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and trs_server is None:
            raise ValueError("Invalid value for `trs_server`, must not be `None`")  # noqa: E501

        self._trs_server = trs_server

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetTRSWorkflowInfoRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetTRSWorkflowInfoRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetTRSWorkflowInfoRequest):
            return True

        return self.to_dict() != other.to_dict()
