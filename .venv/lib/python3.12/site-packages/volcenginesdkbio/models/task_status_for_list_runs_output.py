# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TaskStatusForListRunsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cancelled': 'int',
        'count': 'int',
        'failed': 'int',
        'initializing': 'int',
        'queued': 'int',
        'running': 'int',
        'succeeded': 'int'
    }

    attribute_map = {
        'cancelled': 'Cancelled',
        'count': 'Count',
        'failed': 'Failed',
        'initializing': 'Initializing',
        'queued': 'Queued',
        'running': 'Running',
        'succeeded': 'Succeeded'
    }

    def __init__(self, cancelled=None, count=None, failed=None, initializing=None, queued=None, running=None, succeeded=None, _configuration=None):  # noqa: E501
        """TaskStatusForListRunsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cancelled = None
        self._count = None
        self._failed = None
        self._initializing = None
        self._queued = None
        self._running = None
        self._succeeded = None
        self.discriminator = None

        if cancelled is not None:
            self.cancelled = cancelled
        if count is not None:
            self.count = count
        if failed is not None:
            self.failed = failed
        if initializing is not None:
            self.initializing = initializing
        if queued is not None:
            self.queued = queued
        if running is not None:
            self.running = running
        if succeeded is not None:
            self.succeeded = succeeded

    @property
    def cancelled(self):
        """Gets the cancelled of this TaskStatusForListRunsOutput.  # noqa: E501


        :return: The cancelled of this TaskStatusForListRunsOutput.  # noqa: E501
        :rtype: int
        """
        return self._cancelled

    @cancelled.setter
    def cancelled(self, cancelled):
        """Sets the cancelled of this TaskStatusForListRunsOutput.


        :param cancelled: The cancelled of this TaskStatusForListRunsOutput.  # noqa: E501
        :type: int
        """

        self._cancelled = cancelled

    @property
    def count(self):
        """Gets the count of this TaskStatusForListRunsOutput.  # noqa: E501


        :return: The count of this TaskStatusForListRunsOutput.  # noqa: E501
        :rtype: int
        """
        return self._count

    @count.setter
    def count(self, count):
        """Sets the count of this TaskStatusForListRunsOutput.


        :param count: The count of this TaskStatusForListRunsOutput.  # noqa: E501
        :type: int
        """

        self._count = count

    @property
    def failed(self):
        """Gets the failed of this TaskStatusForListRunsOutput.  # noqa: E501


        :return: The failed of this TaskStatusForListRunsOutput.  # noqa: E501
        :rtype: int
        """
        return self._failed

    @failed.setter
    def failed(self, failed):
        """Sets the failed of this TaskStatusForListRunsOutput.


        :param failed: The failed of this TaskStatusForListRunsOutput.  # noqa: E501
        :type: int
        """

        self._failed = failed

    @property
    def initializing(self):
        """Gets the initializing of this TaskStatusForListRunsOutput.  # noqa: E501


        :return: The initializing of this TaskStatusForListRunsOutput.  # noqa: E501
        :rtype: int
        """
        return self._initializing

    @initializing.setter
    def initializing(self, initializing):
        """Sets the initializing of this TaskStatusForListRunsOutput.


        :param initializing: The initializing of this TaskStatusForListRunsOutput.  # noqa: E501
        :type: int
        """

        self._initializing = initializing

    @property
    def queued(self):
        """Gets the queued of this TaskStatusForListRunsOutput.  # noqa: E501


        :return: The queued of this TaskStatusForListRunsOutput.  # noqa: E501
        :rtype: int
        """
        return self._queued

    @queued.setter
    def queued(self, queued):
        """Sets the queued of this TaskStatusForListRunsOutput.


        :param queued: The queued of this TaskStatusForListRunsOutput.  # noqa: E501
        :type: int
        """

        self._queued = queued

    @property
    def running(self):
        """Gets the running of this TaskStatusForListRunsOutput.  # noqa: E501


        :return: The running of this TaskStatusForListRunsOutput.  # noqa: E501
        :rtype: int
        """
        return self._running

    @running.setter
    def running(self, running):
        """Sets the running of this TaskStatusForListRunsOutput.


        :param running: The running of this TaskStatusForListRunsOutput.  # noqa: E501
        :type: int
        """

        self._running = running

    @property
    def succeeded(self):
        """Gets the succeeded of this TaskStatusForListRunsOutput.  # noqa: E501


        :return: The succeeded of this TaskStatusForListRunsOutput.  # noqa: E501
        :rtype: int
        """
        return self._succeeded

    @succeeded.setter
    def succeeded(self, succeeded):
        """Sets the succeeded of this TaskStatusForListRunsOutput.


        :param succeeded: The succeeded of this TaskStatusForListRunsOutput.  # noqa: E501
        :type: int
        """

        self._succeeded = succeeded

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TaskStatusForListRunsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TaskStatusForListRunsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TaskStatusForListRunsOutput):
            return True

        return self.to_dict() != other.to_dict()
