# coding: utf-8

"""
    rds_mysql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateParameterTemplateRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'template_desc': 'str',
        'template_name': 'str',
        'template_params': 'list[TemplateParamForCreateParameterTemplateInput]',
        'template_type': 'str',
        'template_type_version': 'str'
    }

    attribute_map = {
        'template_desc': 'TemplateDesc',
        'template_name': 'TemplateName',
        'template_params': 'TemplateParams',
        'template_type': 'TemplateType',
        'template_type_version': 'TemplateTypeVersion'
    }

    def __init__(self, template_desc=None, template_name=None, template_params=None, template_type=None, template_type_version=None, _configuration=None):  # noqa: E501
        """CreateParameterTemplateRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._template_desc = None
        self._template_name = None
        self._template_params = None
        self._template_type = None
        self._template_type_version = None
        self.discriminator = None

        if template_desc is not None:
            self.template_desc = template_desc
        if template_name is not None:
            self.template_name = template_name
        if template_params is not None:
            self.template_params = template_params
        if template_type is not None:
            self.template_type = template_type
        if template_type_version is not None:
            self.template_type_version = template_type_version

    @property
    def template_desc(self):
        """Gets the template_desc of this CreateParameterTemplateRequest.  # noqa: E501


        :return: The template_desc of this CreateParameterTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._template_desc

    @template_desc.setter
    def template_desc(self, template_desc):
        """Sets the template_desc of this CreateParameterTemplateRequest.


        :param template_desc: The template_desc of this CreateParameterTemplateRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                template_desc is not None and len(template_desc) > 200):
            raise ValueError("Invalid value for `template_desc`, length must be less than or equal to `200`")  # noqa: E501

        self._template_desc = template_desc

    @property
    def template_name(self):
        """Gets the template_name of this CreateParameterTemplateRequest.  # noqa: E501


        :return: The template_name of this CreateParameterTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._template_name

    @template_name.setter
    def template_name(self, template_name):
        """Sets the template_name of this CreateParameterTemplateRequest.


        :param template_name: The template_name of this CreateParameterTemplateRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                template_name is not None and len(template_name) > 64):
            raise ValueError("Invalid value for `template_name`, length must be less than or equal to `64`")  # noqa: E501
        if (self._configuration.client_side_validation and
                template_name is not None and len(template_name) < 2):
            raise ValueError("Invalid value for `template_name`, length must be greater than or equal to `2`")  # noqa: E501

        self._template_name = template_name

    @property
    def template_params(self):
        """Gets the template_params of this CreateParameterTemplateRequest.  # noqa: E501


        :return: The template_params of this CreateParameterTemplateRequest.  # noqa: E501
        :rtype: list[TemplateParamForCreateParameterTemplateInput]
        """
        return self._template_params

    @template_params.setter
    def template_params(self, template_params):
        """Sets the template_params of this CreateParameterTemplateRequest.


        :param template_params: The template_params of this CreateParameterTemplateRequest.  # noqa: E501
        :type: list[TemplateParamForCreateParameterTemplateInput]
        """

        self._template_params = template_params

    @property
    def template_type(self):
        """Gets the template_type of this CreateParameterTemplateRequest.  # noqa: E501


        :return: The template_type of this CreateParameterTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._template_type

    @template_type.setter
    def template_type(self, template_type):
        """Sets the template_type of this CreateParameterTemplateRequest.


        :param template_type: The template_type of this CreateParameterTemplateRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["MySQL"]  # noqa: E501
        if (self._configuration.client_side_validation and
                template_type not in allowed_values):
            raise ValueError(
                "Invalid value for `template_type` ({0}), must be one of {1}"  # noqa: E501
                .format(template_type, allowed_values)
            )

        self._template_type = template_type

    @property
    def template_type_version(self):
        """Gets the template_type_version of this CreateParameterTemplateRequest.  # noqa: E501


        :return: The template_type_version of this CreateParameterTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._template_type_version

    @template_type_version.setter
    def template_type_version(self, template_type_version):
        """Sets the template_type_version of this CreateParameterTemplateRequest.


        :param template_type_version: The template_type_version of this CreateParameterTemplateRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["MySQL_8_0", "MySQL_Community_5_7"]  # noqa: E501
        if (self._configuration.client_side_validation and
                template_type_version not in allowed_values):
            raise ValueError(
                "Invalid value for `template_type_version` ({0}), must be one of {1}"  # noqa: E501
                .format(template_type_version, allowed_values)
            )

        self._template_type_version = template_type_version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateParameterTemplateRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateParameterTemplateRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateParameterTemplateRequest):
            return True

        return self.to_dict() != other.to_dict()
