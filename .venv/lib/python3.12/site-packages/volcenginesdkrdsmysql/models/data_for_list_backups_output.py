# coding: utf-8

"""
    rds_mysql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListBackupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_end_time': 'str',
        'backup_file_name': 'str',
        'backup_file_size': 'int',
        'backup_id': 'str',
        'backup_mode': 'str',
        'backup_start_time': 'str',
        'backup_status': 'str',
        'backup_strategy': 'str',
        'backup_type': 'str',
        'create_type': 'str'
    }

    attribute_map = {
        'backup_end_time': 'BackupEndTime',
        'backup_file_name': 'BackupFileName',
        'backup_file_size': 'BackupFileSize',
        'backup_id': 'BackupId',
        'backup_mode': 'BackupMode',
        'backup_start_time': 'BackupStartTime',
        'backup_status': 'BackupStatus',
        'backup_strategy': 'BackupStrategy',
        'backup_type': 'BackupType',
        'create_type': 'CreateType'
    }

    def __init__(self, backup_end_time=None, backup_file_name=None, backup_file_size=None, backup_id=None, backup_mode=None, backup_start_time=None, backup_status=None, backup_strategy=None, backup_type=None, create_type=None, _configuration=None):  # noqa: E501
        """DataForListBackupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_end_time = None
        self._backup_file_name = None
        self._backup_file_size = None
        self._backup_id = None
        self._backup_mode = None
        self._backup_start_time = None
        self._backup_status = None
        self._backup_strategy = None
        self._backup_type = None
        self._create_type = None
        self.discriminator = None

        if backup_end_time is not None:
            self.backup_end_time = backup_end_time
        if backup_file_name is not None:
            self.backup_file_name = backup_file_name
        if backup_file_size is not None:
            self.backup_file_size = backup_file_size
        if backup_id is not None:
            self.backup_id = backup_id
        if backup_mode is not None:
            self.backup_mode = backup_mode
        if backup_start_time is not None:
            self.backup_start_time = backup_start_time
        if backup_status is not None:
            self.backup_status = backup_status
        if backup_strategy is not None:
            self.backup_strategy = backup_strategy
        if backup_type is not None:
            self.backup_type = backup_type
        if create_type is not None:
            self.create_type = create_type

    @property
    def backup_end_time(self):
        """Gets the backup_end_time of this DataForListBackupsOutput.  # noqa: E501


        :return: The backup_end_time of this DataForListBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_end_time

    @backup_end_time.setter
    def backup_end_time(self, backup_end_time):
        """Sets the backup_end_time of this DataForListBackupsOutput.


        :param backup_end_time: The backup_end_time of this DataForListBackupsOutput.  # noqa: E501
        :type: str
        """

        self._backup_end_time = backup_end_time

    @property
    def backup_file_name(self):
        """Gets the backup_file_name of this DataForListBackupsOutput.  # noqa: E501


        :return: The backup_file_name of this DataForListBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_file_name

    @backup_file_name.setter
    def backup_file_name(self, backup_file_name):
        """Sets the backup_file_name of this DataForListBackupsOutput.


        :param backup_file_name: The backup_file_name of this DataForListBackupsOutput.  # noqa: E501
        :type: str
        """

        self._backup_file_name = backup_file_name

    @property
    def backup_file_size(self):
        """Gets the backup_file_size of this DataForListBackupsOutput.  # noqa: E501


        :return: The backup_file_size of this DataForListBackupsOutput.  # noqa: E501
        :rtype: int
        """
        return self._backup_file_size

    @backup_file_size.setter
    def backup_file_size(self, backup_file_size):
        """Sets the backup_file_size of this DataForListBackupsOutput.


        :param backup_file_size: The backup_file_size of this DataForListBackupsOutput.  # noqa: E501
        :type: int
        """

        self._backup_file_size = backup_file_size

    @property
    def backup_id(self):
        """Gets the backup_id of this DataForListBackupsOutput.  # noqa: E501


        :return: The backup_id of this DataForListBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_id

    @backup_id.setter
    def backup_id(self, backup_id):
        """Sets the backup_id of this DataForListBackupsOutput.


        :param backup_id: The backup_id of this DataForListBackupsOutput.  # noqa: E501
        :type: str
        """

        self._backup_id = backup_id

    @property
    def backup_mode(self):
        """Gets the backup_mode of this DataForListBackupsOutput.  # noqa: E501


        :return: The backup_mode of this DataForListBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_mode

    @backup_mode.setter
    def backup_mode(self, backup_mode):
        """Sets the backup_mode of this DataForListBackupsOutput.


        :param backup_mode: The backup_mode of this DataForListBackupsOutput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Full", "Increment"]  # noqa: E501
        if (self._configuration.client_side_validation and
                backup_mode not in allowed_values):
            raise ValueError(
                "Invalid value for `backup_mode` ({0}), must be one of {1}"  # noqa: E501
                .format(backup_mode, allowed_values)
            )

        self._backup_mode = backup_mode

    @property
    def backup_start_time(self):
        """Gets the backup_start_time of this DataForListBackupsOutput.  # noqa: E501


        :return: The backup_start_time of this DataForListBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_start_time

    @backup_start_time.setter
    def backup_start_time(self, backup_start_time):
        """Sets the backup_start_time of this DataForListBackupsOutput.


        :param backup_start_time: The backup_start_time of this DataForListBackupsOutput.  # noqa: E501
        :type: str
        """

        self._backup_start_time = backup_start_time

    @property
    def backup_status(self):
        """Gets the backup_status of this DataForListBackupsOutput.  # noqa: E501


        :return: The backup_status of this DataForListBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_status

    @backup_status.setter
    def backup_status(self, backup_status):
        """Sets the backup_status of this DataForListBackupsOutput.


        :param backup_status: The backup_status of this DataForListBackupsOutput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Failed", "Running", "Success"]  # noqa: E501
        if (self._configuration.client_side_validation and
                backup_status not in allowed_values):
            raise ValueError(
                "Invalid value for `backup_status` ({0}), must be one of {1}"  # noqa: E501
                .format(backup_status, allowed_values)
            )

        self._backup_status = backup_status

    @property
    def backup_strategy(self):
        """Gets the backup_strategy of this DataForListBackupsOutput.  # noqa: E501


        :return: The backup_strategy of this DataForListBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_strategy

    @backup_strategy.setter
    def backup_strategy(self, backup_strategy):
        """Sets the backup_strategy of this DataForListBackupsOutput.


        :param backup_strategy: The backup_strategy of this DataForListBackupsOutput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Database", "Instance"]  # noqa: E501
        if (self._configuration.client_side_validation and
                backup_strategy not in allowed_values):
            raise ValueError(
                "Invalid value for `backup_strategy` ({0}), must be one of {1}"  # noqa: E501
                .format(backup_strategy, allowed_values)
            )

        self._backup_strategy = backup_strategy

    @property
    def backup_type(self):
        """Gets the backup_type of this DataForListBackupsOutput.  # noqa: E501


        :return: The backup_type of this DataForListBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_type

    @backup_type.setter
    def backup_type(self, backup_type):
        """Sets the backup_type of this DataForListBackupsOutput.


        :param backup_type: The backup_type of this DataForListBackupsOutput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Logical", "Physical"]  # noqa: E501
        if (self._configuration.client_side_validation and
                backup_type not in allowed_values):
            raise ValueError(
                "Invalid value for `backup_type` ({0}), must be one of {1}"  # noqa: E501
                .format(backup_type, allowed_values)
            )

        self._backup_type = backup_type

    @property
    def create_type(self):
        """Gets the create_type of this DataForListBackupsOutput.  # noqa: E501


        :return: The create_type of this DataForListBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_type

    @create_type.setter
    def create_type(self, create_type):
        """Sets the create_type of this DataForListBackupsOutput.


        :param create_type: The create_type of this DataForListBackupsOutput.  # noqa: E501
        :type: str
        """
        allowed_values = ["System", "User"]  # noqa: E501
        if (self._configuration.client_side_validation and
                create_type not in allowed_values):
            raise ValueError(
                "Invalid value for `create_type` ({0}), must be one of {1}"  # noqa: E501
                .format(create_type, allowed_values)
            )

        self._create_type = create_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListBackupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListBackupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListBackupsOutput):
            return True

        return self.to_dict() != other.to_dict()
