# coding: utf-8

"""
    rds_mysql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ParameterForModifyInstanceParamsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'name': 'str',
        'new_value': 'str',
        'old_value': 'str',
        'restart': 'bool',
        'result': 'str'
    }

    attribute_map = {
        'name': 'Name',
        'new_value': 'NewValue',
        'old_value': 'OldValue',
        'restart': 'Restart',
        'result': 'Result'
    }

    def __init__(self, name=None, new_value=None, old_value=None, restart=None, result=None, _configuration=None):  # noqa: E501
        """ParameterForModifyInstanceParamsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._new_value = None
        self._old_value = None
        self._restart = None
        self._result = None
        self.discriminator = None

        if name is not None:
            self.name = name
        if new_value is not None:
            self.new_value = new_value
        if old_value is not None:
            self.old_value = old_value
        if restart is not None:
            self.restart = restart
        if result is not None:
            self.result = result

    @property
    def name(self):
        """Gets the name of this ParameterForModifyInstanceParamsInput.  # noqa: E501


        :return: The name of this ParameterForModifyInstanceParamsInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ParameterForModifyInstanceParamsInput.


        :param name: The name of this ParameterForModifyInstanceParamsInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def new_value(self):
        """Gets the new_value of this ParameterForModifyInstanceParamsInput.  # noqa: E501


        :return: The new_value of this ParameterForModifyInstanceParamsInput.  # noqa: E501
        :rtype: str
        """
        return self._new_value

    @new_value.setter
    def new_value(self, new_value):
        """Sets the new_value of this ParameterForModifyInstanceParamsInput.


        :param new_value: The new_value of this ParameterForModifyInstanceParamsInput.  # noqa: E501
        :type: str
        """

        self._new_value = new_value

    @property
    def old_value(self):
        """Gets the old_value of this ParameterForModifyInstanceParamsInput.  # noqa: E501


        :return: The old_value of this ParameterForModifyInstanceParamsInput.  # noqa: E501
        :rtype: str
        """
        return self._old_value

    @old_value.setter
    def old_value(self, old_value):
        """Sets the old_value of this ParameterForModifyInstanceParamsInput.


        :param old_value: The old_value of this ParameterForModifyInstanceParamsInput.  # noqa: E501
        :type: str
        """

        self._old_value = old_value

    @property
    def restart(self):
        """Gets the restart of this ParameterForModifyInstanceParamsInput.  # noqa: E501


        :return: The restart of this ParameterForModifyInstanceParamsInput.  # noqa: E501
        :rtype: bool
        """
        return self._restart

    @restart.setter
    def restart(self, restart):
        """Sets the restart of this ParameterForModifyInstanceParamsInput.


        :param restart: The restart of this ParameterForModifyInstanceParamsInput.  # noqa: E501
        :type: bool
        """

        self._restart = restart

    @property
    def result(self):
        """Gets the result of this ParameterForModifyInstanceParamsInput.  # noqa: E501


        :return: The result of this ParameterForModifyInstanceParamsInput.  # noqa: E501
        :rtype: str
        """
        return self._result

    @result.setter
    def result(self, result):
        """Sets the result of this ParameterForModifyInstanceParamsInput.


        :param result: The result of this ParameterForModifyInstanceParamsInput.  # noqa: E501
        :type: str
        """

        self._result = result

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ParameterForModifyInstanceParamsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ParameterForModifyInstanceParamsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ParameterForModifyInstanceParamsInput):
            return True

        return self.to_dict() != other.to_dict()
