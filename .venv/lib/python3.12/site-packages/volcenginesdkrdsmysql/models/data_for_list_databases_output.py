# coding: utf-8

"""
    rds_mysql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListDatabasesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_names': 'str',
        'character_set_name': 'str',
        'db_name': 'str',
        'db_status': 'str'
    }

    attribute_map = {
        'account_names': 'AccountNames',
        'character_set_name': 'CharacterSetName',
        'db_name': 'DBName',
        'db_status': 'DBStatus'
    }

    def __init__(self, account_names=None, character_set_name=None, db_name=None, db_status=None, _configuration=None):  # noqa: E501
        """DataForListDatabasesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_names = None
        self._character_set_name = None
        self._db_name = None
        self._db_status = None
        self.discriminator = None

        if account_names is not None:
            self.account_names = account_names
        if character_set_name is not None:
            self.character_set_name = character_set_name
        if db_name is not None:
            self.db_name = db_name
        if db_status is not None:
            self.db_status = db_status

    @property
    def account_names(self):
        """Gets the account_names of this DataForListDatabasesOutput.  # noqa: E501


        :return: The account_names of this DataForListDatabasesOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_names

    @account_names.setter
    def account_names(self, account_names):
        """Sets the account_names of this DataForListDatabasesOutput.


        :param account_names: The account_names of this DataForListDatabasesOutput.  # noqa: E501
        :type: str
        """

        self._account_names = account_names

    @property
    def character_set_name(self):
        """Gets the character_set_name of this DataForListDatabasesOutput.  # noqa: E501


        :return: The character_set_name of this DataForListDatabasesOutput.  # noqa: E501
        :rtype: str
        """
        return self._character_set_name

    @character_set_name.setter
    def character_set_name(self, character_set_name):
        """Sets the character_set_name of this DataForListDatabasesOutput.


        :param character_set_name: The character_set_name of this DataForListDatabasesOutput.  # noqa: E501
        :type: str
        """

        self._character_set_name = character_set_name

    @property
    def db_name(self):
        """Gets the db_name of this DataForListDatabasesOutput.  # noqa: E501


        :return: The db_name of this DataForListDatabasesOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_name

    @db_name.setter
    def db_name(self, db_name):
        """Sets the db_name of this DataForListDatabasesOutput.


        :param db_name: The db_name of this DataForListDatabasesOutput.  # noqa: E501
        :type: str
        """

        self._db_name = db_name

    @property
    def db_status(self):
        """Gets the db_status of this DataForListDatabasesOutput.  # noqa: E501


        :return: The db_status of this DataForListDatabasesOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_status

    @db_status.setter
    def db_status(self, db_status):
        """Sets the db_status of this DataForListDatabasesOutput.


        :param db_status: The db_status of this DataForListDatabasesOutput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Running"]  # noqa: E501
        if (self._configuration.client_side_validation and
                db_status not in allowed_values):
            raise ValueError(
                "Invalid value for `db_status` ({0}), must be one of {1}"  # noqa: E501
                .format(db_status, allowed_values)
            )

        self._db_status = db_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListDatabasesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListDatabasesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListDatabasesOutput):
            return True

        return self.to_dict() != other.to_dict()
