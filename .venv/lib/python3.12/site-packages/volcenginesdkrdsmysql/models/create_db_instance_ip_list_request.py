# coding: utf-8

"""
    rds_mysql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDBInstanceIPListRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'group_name': 'str',
        'ip_list': 'list[str]',
        'instance_id': 'str'
    }

    attribute_map = {
        'group_name': 'GroupName',
        'ip_list': 'IPList',
        'instance_id': 'InstanceId'
    }

    def __init__(self, group_name=None, ip_list=None, instance_id=None, _configuration=None):  # noqa: E501
        """CreateDBInstanceIPListRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._group_name = None
        self._ip_list = None
        self._instance_id = None
        self.discriminator = None

        if group_name is not None:
            self.group_name = group_name
        if ip_list is not None:
            self.ip_list = ip_list
        self.instance_id = instance_id

    @property
    def group_name(self):
        """Gets the group_name of this CreateDBInstanceIPListRequest.  # noqa: E501


        :return: The group_name of this CreateDBInstanceIPListRequest.  # noqa: E501
        :rtype: str
        """
        return self._group_name

    @group_name.setter
    def group_name(self, group_name):
        """Sets the group_name of this CreateDBInstanceIPListRequest.


        :param group_name: The group_name of this CreateDBInstanceIPListRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                group_name is not None and len(group_name) > 120):
            raise ValueError("Invalid value for `group_name`, length must be less than or equal to `120`")  # noqa: E501
        if (self._configuration.client_side_validation and
                group_name is not None and len(group_name) < 2):
            raise ValueError("Invalid value for `group_name`, length must be greater than or equal to `2`")  # noqa: E501

        self._group_name = group_name

    @property
    def ip_list(self):
        """Gets the ip_list of this CreateDBInstanceIPListRequest.  # noqa: E501


        :return: The ip_list of this CreateDBInstanceIPListRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ip_list

    @ip_list.setter
    def ip_list(self, ip_list):
        """Sets the ip_list of this CreateDBInstanceIPListRequest.


        :param ip_list: The ip_list of this CreateDBInstanceIPListRequest.  # noqa: E501
        :type: list[str]
        """

        self._ip_list = ip_list

    @property
    def instance_id(self):
        """Gets the instance_id of this CreateDBInstanceIPListRequest.  # noqa: E501


        :return: The instance_id of this CreateDBInstanceIPListRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this CreateDBInstanceIPListRequest.


        :param instance_id: The instance_id of this CreateDBInstanceIPListRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDBInstanceIPListRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDBInstanceIPListRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDBInstanceIPListRequest):
            return True

        return self.to_dict() != other.to_dict()
