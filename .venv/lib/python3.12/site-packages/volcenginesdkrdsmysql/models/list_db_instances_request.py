# coding: utf-8

"""
    rds_mysql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListDBInstancesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_end_time': 'str',
        'create_start_time': 'str',
        'instance_id': 'str',
        'instance_status': 'str',
        'instance_type': 'str',
        'limit': 'int',
        'offset': 'int',
        'region': 'str',
        'zone': 'str'
    }

    attribute_map = {
        'create_end_time': 'CreateEndTime',
        'create_start_time': 'CreateStartTime',
        'instance_id': 'InstanceId',
        'instance_status': 'InstanceStatus',
        'instance_type': 'InstanceType',
        'limit': 'Limit',
        'offset': 'Offset',
        'region': 'Region',
        'zone': 'Zone'
    }

    def __init__(self, create_end_time=None, create_start_time=None, instance_id=None, instance_status=None, instance_type=None, limit=None, offset=None, region=None, zone=None, _configuration=None):  # noqa: E501
        """ListDBInstancesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_end_time = None
        self._create_start_time = None
        self._instance_id = None
        self._instance_status = None
        self._instance_type = None
        self._limit = None
        self._offset = None
        self._region = None
        self._zone = None
        self.discriminator = None

        if create_end_time is not None:
            self.create_end_time = create_end_time
        if create_start_time is not None:
            self.create_start_time = create_start_time
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_status is not None:
            self.instance_status = instance_status
        if instance_type is not None:
            self.instance_type = instance_type
        if limit is not None:
            self.limit = limit
        if offset is not None:
            self.offset = offset
        if region is not None:
            self.region = region
        if zone is not None:
            self.zone = zone

    @property
    def create_end_time(self):
        """Gets the create_end_time of this ListDBInstancesRequest.  # noqa: E501


        :return: The create_end_time of this ListDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_end_time

    @create_end_time.setter
    def create_end_time(self, create_end_time):
        """Sets the create_end_time of this ListDBInstancesRequest.


        :param create_end_time: The create_end_time of this ListDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._create_end_time = create_end_time

    @property
    def create_start_time(self):
        """Gets the create_start_time of this ListDBInstancesRequest.  # noqa: E501


        :return: The create_start_time of this ListDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_start_time

    @create_start_time.setter
    def create_start_time(self, create_start_time):
        """Sets the create_start_time of this ListDBInstancesRequest.


        :param create_start_time: The create_start_time of this ListDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._create_start_time = create_start_time

    @property
    def instance_id(self):
        """Gets the instance_id of this ListDBInstancesRequest.  # noqa: E501


        :return: The instance_id of this ListDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ListDBInstancesRequest.


        :param instance_id: The instance_id of this ListDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_status(self):
        """Gets the instance_status of this ListDBInstancesRequest.  # noqa: E501


        :return: The instance_status of this ListDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_status

    @instance_status.setter
    def instance_status(self, instance_status):
        """Sets the instance_status of this ListDBInstancesRequest.


        :param instance_status: The instance_status of this ListDBInstancesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["AllowListMaintaining", "Closed", "Closing", "CreateFailed", "Creating", "Deleting", "Destroyed", "Destroying", "Error", "Importing", "Maintaining", "MasterChanging", "Migrating", "Reclaiming", "Recycled", "Released", "Restarting", "Restoring", "Resuming", "Running", "SSLUpdating", "TDEUpdating", "Unknown", "Updating", "Upgrading", "WaitingPaid"]  # noqa: E501
        if (self._configuration.client_side_validation and
                instance_status not in allowed_values):
            raise ValueError(
                "Invalid value for `instance_status` ({0}), must be one of {1}"  # noqa: E501
                .format(instance_status, allowed_values)
            )

        self._instance_status = instance_status

    @property
    def instance_type(self):
        """Gets the instance_type of this ListDBInstancesRequest.  # noqa: E501


        :return: The instance_type of this ListDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this ListDBInstancesRequest.


        :param instance_type: The instance_type of this ListDBInstancesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["HA"]  # noqa: E501
        if (self._configuration.client_side_validation and
                instance_type not in allowed_values):
            raise ValueError(
                "Invalid value for `instance_type` ({0}), must be one of {1}"  # noqa: E501
                .format(instance_type, allowed_values)
            )

        self._instance_type = instance_type

    @property
    def limit(self):
        """Gets the limit of this ListDBInstancesRequest.  # noqa: E501


        :return: The limit of this ListDBInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._limit

    @limit.setter
    def limit(self, limit):
        """Sets the limit of this ListDBInstancesRequest.


        :param limit: The limit of this ListDBInstancesRequest.  # noqa: E501
        :type: int
        """

        self._limit = limit

    @property
    def offset(self):
        """Gets the offset of this ListDBInstancesRequest.  # noqa: E501


        :return: The offset of this ListDBInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._offset

    @offset.setter
    def offset(self, offset):
        """Sets the offset of this ListDBInstancesRequest.


        :param offset: The offset of this ListDBInstancesRequest.  # noqa: E501
        :type: int
        """

        self._offset = offset

    @property
    def region(self):
        """Gets the region of this ListDBInstancesRequest.  # noqa: E501


        :return: The region of this ListDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this ListDBInstancesRequest.


        :param region: The region of this ListDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def zone(self):
        """Gets the zone of this ListDBInstancesRequest.  # noqa: E501


        :return: The zone of this ListDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone

    @zone.setter
    def zone(self, zone):
        """Sets the zone of this ListDBInstancesRequest.


        :param zone: The zone of this ListDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._zone = zone

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListDBInstancesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListDBInstancesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListDBInstancesRequest):
            return True

        return self.to_dict() != other.to_dict()
