# coding: utf-8

"""
    rds_mysql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyDBInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'instance_spec': 'InstanceSpecForModifyDBInstanceInput',
        'instance_type': 'str',
        'storage_space_gb': 'int'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'instance_spec': 'InstanceSpec',
        'instance_type': 'InstanceType',
        'storage_space_gb': 'StorageSpaceGB'
    }

    def __init__(self, instance_id=None, instance_spec=None, instance_type=None, storage_space_gb=None, _configuration=None):  # noqa: E501
        """ModifyDBInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._instance_spec = None
        self._instance_type = None
        self._storage_space_gb = None
        self.discriminator = None

        self.instance_id = instance_id
        if instance_spec is not None:
            self.instance_spec = instance_spec
        if instance_type is not None:
            self.instance_type = instance_type
        self.storage_space_gb = storage_space_gb

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyDBInstanceRequest.  # noqa: E501


        :return: The instance_id of this ModifyDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyDBInstanceRequest.


        :param instance_id: The instance_id of this ModifyDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def instance_spec(self):
        """Gets the instance_spec of this ModifyDBInstanceRequest.  # noqa: E501


        :return: The instance_spec of this ModifyDBInstanceRequest.  # noqa: E501
        :rtype: InstanceSpecForModifyDBInstanceInput
        """
        return self._instance_spec

    @instance_spec.setter
    def instance_spec(self, instance_spec):
        """Sets the instance_spec of this ModifyDBInstanceRequest.


        :param instance_spec: The instance_spec of this ModifyDBInstanceRequest.  # noqa: E501
        :type: InstanceSpecForModifyDBInstanceInput
        """

        self._instance_spec = instance_spec

    @property
    def instance_type(self):
        """Gets the instance_type of this ModifyDBInstanceRequest.  # noqa: E501


        :return: The instance_type of this ModifyDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this ModifyDBInstanceRequest.


        :param instance_type: The instance_type of this ModifyDBInstanceRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["HA"]  # noqa: E501
        if (self._configuration.client_side_validation and
                instance_type not in allowed_values):
            raise ValueError(
                "Invalid value for `instance_type` ({0}), must be one of {1}"  # noqa: E501
                .format(instance_type, allowed_values)
            )

        self._instance_type = instance_type

    @property
    def storage_space_gb(self):
        """Gets the storage_space_gb of this ModifyDBInstanceRequest.  # noqa: E501


        :return: The storage_space_gb of this ModifyDBInstanceRequest.  # noqa: E501
        :rtype: int
        """
        return self._storage_space_gb

    @storage_space_gb.setter
    def storage_space_gb(self, storage_space_gb):
        """Sets the storage_space_gb of this ModifyDBInstanceRequest.


        :param storage_space_gb: The storage_space_gb of this ModifyDBInstanceRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and storage_space_gb is None:
            raise ValueError("Invalid value for `storage_space_gb`, must not be `None`")  # noqa: E501

        self._storage_space_gb = storage_space_gb

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyDBInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyDBInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyDBInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
