# coding: utf-8

"""
    rds_mysql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDBInstanceResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'basic_info': 'BasicInfoForDescribeDBInstanceOutput',
        'connection_info': 'ConnectionInfoForDescribeDBInstanceOutput',
        'data_sync_mode': 'str',
        'storage_type': 'str'
    }

    attribute_map = {
        'basic_info': 'BasicInfo',
        'connection_info': 'ConnectionInfo',
        'data_sync_mode': 'DataSyncMode',
        'storage_type': 'StorageType'
    }

    def __init__(self, basic_info=None, connection_info=None, data_sync_mode=None, storage_type=None, _configuration=None):  # noqa: E501
        """DescribeDBInstanceResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._basic_info = None
        self._connection_info = None
        self._data_sync_mode = None
        self._storage_type = None
        self.discriminator = None

        if basic_info is not None:
            self.basic_info = basic_info
        if connection_info is not None:
            self.connection_info = connection_info
        if data_sync_mode is not None:
            self.data_sync_mode = data_sync_mode
        if storage_type is not None:
            self.storage_type = storage_type

    @property
    def basic_info(self):
        """Gets the basic_info of this DescribeDBInstanceResponse.  # noqa: E501


        :return: The basic_info of this DescribeDBInstanceResponse.  # noqa: E501
        :rtype: BasicInfoForDescribeDBInstanceOutput
        """
        return self._basic_info

    @basic_info.setter
    def basic_info(self, basic_info):
        """Sets the basic_info of this DescribeDBInstanceResponse.


        :param basic_info: The basic_info of this DescribeDBInstanceResponse.  # noqa: E501
        :type: BasicInfoForDescribeDBInstanceOutput
        """

        self._basic_info = basic_info

    @property
    def connection_info(self):
        """Gets the connection_info of this DescribeDBInstanceResponse.  # noqa: E501


        :return: The connection_info of this DescribeDBInstanceResponse.  # noqa: E501
        :rtype: ConnectionInfoForDescribeDBInstanceOutput
        """
        return self._connection_info

    @connection_info.setter
    def connection_info(self, connection_info):
        """Sets the connection_info of this DescribeDBInstanceResponse.


        :param connection_info: The connection_info of this DescribeDBInstanceResponse.  # noqa: E501
        :type: ConnectionInfoForDescribeDBInstanceOutput
        """

        self._connection_info = connection_info

    @property
    def data_sync_mode(self):
        """Gets the data_sync_mode of this DescribeDBInstanceResponse.  # noqa: E501


        :return: The data_sync_mode of this DescribeDBInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._data_sync_mode

    @data_sync_mode.setter
    def data_sync_mode(self, data_sync_mode):
        """Sets the data_sync_mode of this DescribeDBInstanceResponse.


        :param data_sync_mode: The data_sync_mode of this DescribeDBInstanceResponse.  # noqa: E501
        :type: str
        """
        allowed_values = ["Async", "SemiSync", "Sync"]  # noqa: E501
        if (self._configuration.client_side_validation and
                data_sync_mode not in allowed_values):
            raise ValueError(
                "Invalid value for `data_sync_mode` ({0}), must be one of {1}"  # noqa: E501
                .format(data_sync_mode, allowed_values)
            )

        self._data_sync_mode = data_sync_mode

    @property
    def storage_type(self):
        """Gets the storage_type of this DescribeDBInstanceResponse.  # noqa: E501


        :return: The storage_type of this DescribeDBInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._storage_type

    @storage_type.setter
    def storage_type(self, storage_type):
        """Sets the storage_type of this DescribeDBInstanceResponse.


        :param storage_type: The storage_type of this DescribeDBInstanceResponse.  # noqa: E501
        :type: str
        """
        allowed_values = ["CloudStorage", "ESSDPL1", "ESSDPL2", "LocalSSD"]  # noqa: E501
        if (self._configuration.client_side_validation and
                storage_type not in allowed_values):
            raise ValueError(
                "Invalid value for `storage_type` ({0}), must be one of {1}"  # noqa: E501
                .format(storage_type, allowed_values)
            )

        self._storage_type = storage_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDBInstanceResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDBInstanceResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDBInstanceResponse):
            return True

        return self.to_dict() != other.to_dict()
