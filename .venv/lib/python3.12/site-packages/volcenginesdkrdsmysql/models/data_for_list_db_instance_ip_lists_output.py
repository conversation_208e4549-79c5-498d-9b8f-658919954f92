# coding: utf-8

"""
    rds_mysql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListDBInstanceIPListsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'group_name': 'str',
        'ip_list': 'list[str]'
    }

    attribute_map = {
        'group_name': 'GroupName',
        'ip_list': 'IPList'
    }

    def __init__(self, group_name=None, ip_list=None, _configuration=None):  # noqa: E501
        """DataForListDBInstanceIPListsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._group_name = None
        self._ip_list = None
        self.discriminator = None

        if group_name is not None:
            self.group_name = group_name
        if ip_list is not None:
            self.ip_list = ip_list

    @property
    def group_name(self):
        """Gets the group_name of this DataForListDBInstanceIPListsOutput.  # noqa: E501


        :return: The group_name of this DataForListDBInstanceIPListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_name

    @group_name.setter
    def group_name(self, group_name):
        """Sets the group_name of this DataForListDBInstanceIPListsOutput.


        :param group_name: The group_name of this DataForListDBInstanceIPListsOutput.  # noqa: E501
        :type: str
        """

        self._group_name = group_name

    @property
    def ip_list(self):
        """Gets the ip_list of this DataForListDBInstanceIPListsOutput.  # noqa: E501


        :return: The ip_list of this DataForListDBInstanceIPListsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ip_list

    @ip_list.setter
    def ip_list(self, ip_list):
        """Sets the ip_list of this DataForListDBInstanceIPListsOutput.


        :param ip_list: The ip_list of this DataForListDBInstanceIPListsOutput.  # noqa: E501
        :type: list[str]
        """

        self._ip_list = ip_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListDBInstanceIPListsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListDBInstanceIPListsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListDBInstanceIPListsOutput):
            return True

        return self.to_dict() != other.to_dict()
