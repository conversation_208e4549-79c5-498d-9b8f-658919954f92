import os
from volcenginesdkarkruntime import Ark

# 初始化客户端
client = Ark(
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    api_key=os.environ.get("ARK_API_KEY"),
)

# 可能的模型名称列表
model_names = [
    "doubao-seedream-3-0-t2i-250415",
    "doubao-seedream-3.0-t2i",
    "doubao-seedream-3-0-t2i",
    "doubao-seedream",
    "doubao-seedream-3.0-t2i-250415",
    "ep-20250709213054-xxxxx",  # 可能需要创建推理接入点
]

test_prompt = "一只可爱的小猫"

print("🔍 测试不同的模型名称...")
print("=" * 50)

for model_name in model_names:
    print(f"\n📝 测试模型: {model_name}")
    try:
        response = client.images.generate(
            model=model_name,
            prompt=test_prompt
        )
        print(f"✅ 成功! 模型 {model_name} 可用")
        print(f"🖼️  图像URL: {response.data[0].url}")
        break  # 找到可用模型就停止
    except Exception as e:
        error_msg = str(e)
        if "invalid model specified" in error_msg:
            print(f"❌ 模型名称无效")
        elif "not found" in error_msg:
            print(f"❌ 模型未找到")
        elif "permission" in error_msg or "unauthorized" in error_msg:
            print(f"❌ 权限不足，可能需要开通服务")
        else:
            print(f"❌ 其他错误: {error_msg[:100]}...")

print("\n" + "=" * 50)
print("💡 提示:")
print("1. 如果所有模型都失败，请检查是否在控制台开通了图像生成服务")
print("2. 可能需要创建自定义推理接入点")
print("3. 检查API密钥是否有相应权限")
