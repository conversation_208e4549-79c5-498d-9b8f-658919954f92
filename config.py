"""
豆包图像生成 - 配置文件
"""

# 图像生成参数
IMAGE_PARAMS = {
    "size": "1024x1024",        # 图像尺寸: 512x512, 768x768, 1024x1024, 1152x864, 864x1152
    "watermark": False,         # 是否添加水印
    "guidance_scale": 7.5,      # 引导强度: 1.0-20.0
    "seed": None,               # 随机种子: None为随机，数字为固定
}

# 提示词配置
PROMPTS = {
    "default": "鱼眼镜头，一只猫咪的头部，画面呈现出猫咪的五官因为拍摄方式扭曲的效果。",
    "landscape": "山水画风格的自然风景，水墨画质感",
    "portrait": "专业人像摄影，柔和光线，高质量",
    "abstract": "抽象艺术，色彩丰富，现代风格",
}

# 使用示例
def get_params():
    """获取图像生成参数"""
    return IMAGE_PARAMS.copy()

def get_prompt(name="default"):
    """获取提示词"""
    return PROMPTS.get(name, PROMPTS["default"])
