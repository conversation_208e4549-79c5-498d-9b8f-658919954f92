#!/usr/bin/env python3
"""
测试与火山方舟API的连接
"""

import os
import requests
from volcenginesdkarkruntime import Ark


def test_api_connection():
    """测试API连接"""
    print("🔗 测试API连接...")
    
    api_key = os.environ.get("ARK_API_KEY")
    if not api_key:
        print("❌ 未找到 ARK_API_KEY 环境变量")
        return False
    
    print(f"🔑 使用API密钥: {api_key[:8]}...{api_key[-4:]}")
    
    # 测试基本连接
    try:
        client = Ark(
            base_url="https://ark.cn-beijing.volces.com/api/v3",
            api_key=api_key,
        )
        print("✅ Ark客户端初始化成功")
        return True
    except Exception as e:
        print(f"❌ Ark客户端初始化失败: {e}")
        return False


def test_text_generation():
    """测试文本生成（通常比图像生成更容易成功）"""
    print("\n📝 测试文本生成...")
    
    api_key = os.environ.get("ARK_API_KEY")
    client = Ark(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=api_key,
    )
    
    # 常见的文本模型ID
    text_models = [
        "doubao-lite-4k",
        "doubao-lite-32k", 
        "doubao-pro-32k",
        "ep-20250101000000-xxxxx"  # 示例接入点ID
    ]
    
    for model in text_models:
        try:
            print(f"🔄 尝试文本模型: {model}")
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": "你好"}
                ],
                max_tokens=10
            )
            print(f"✅ 文本模型 {model} 可用")
            print(f"📄 响应: {response.choices[0].message.content}")
            return True
        except Exception as e:
            error_msg = str(e)
            if "invalid model specified" in error_msg:
                print(f"❌ 模型 {model} 无效")
            else:
                print(f"❌ 模型 {model} 错误: {error_msg[:100]}...")
            continue
    
    print("❌ 所有文本模型都无法使用")
    return False


def check_network():
    """检查网络连接"""
    print("\n🌐 检查网络连接...")
    
    try:
        response = requests.get("https://ark.cn-beijing.volces.com", timeout=10)
        print(f"✅ 网络连接正常 (状态码: {response.status_code})")
        return True
    except requests.exceptions.Timeout:
        print("❌ 网络连接超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 网络连接失败")
        return False
    except Exception as e:
        print(f"❌ 网络检查失败: {e}")
        return False


def main():
    print("🧪 豆包API连接测试")
    print("=" * 40)
    
    # 测试API连接
    if not test_api_connection():
        return
    
    # 测试网络连接
    if not check_network():
        return
    
    # 测试文本生成
    if test_text_generation():
        print("\n✅ 基本API功能正常")
        print("💡 如果图像生成仍然失败，请确保:")
        print("   1. 已在控制台开通图像生成服务")
        print("   2. 已创建图像生成的推理接入点")
        print("   3. API密钥有图像生成权限")
    else:
        print("\n❌ API功能异常")
        print("💡 请检查:")
        print("   1. API密钥是否正确")
        print("   2. 是否已在控制台开通服务")
        print("   3. 账户余额是否充足")
    
    print("\n🔗 有用链接:")
    print("   - 控制台: https://console.volcengine.com/ark")
    print("   - 文档: https://www.volcengine.com/docs/82379")


if __name__ == "__main__":
    main()
