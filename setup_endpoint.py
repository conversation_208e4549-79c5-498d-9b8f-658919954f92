#!/usr/bin/env python3
"""
豆包图像生成 - 推理接入点配置助手

这个脚本帮助您配置推理接入点以使用豆包图像生成服务。
"""

import os
import sys
from volcenginesdkarkruntime import Ark


def check_api_key():
    """检查API密钥配置"""
    api_key = os.environ.get("ARK_API_KEY")
    if not api_key:
        print("❌ 错误: 未找到 ARK_API_KEY 环境变量")
        print("💡 请先设置API密钥:")
        print("   export ARK_API_KEY='your_api_key_here'")
        return None
    
    print(f"✅ API密钥已配置: {api_key[:8]}...{api_key[-4:]}")
    return api_key


def test_endpoint(endpoint_id):
    """测试推理接入点是否可用"""
    api_key = check_api_key()
    if not api_key:
        return False
    
    client = Ark(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=api_key,
    )
    
    try:
        print(f"🔄 测试推理接入点: {endpoint_id}")
        response = client.images.generate(
            model=endpoint_id,
            prompt="一只可爱的小猫"
        )
        print(f"✅ 推理接入点 {endpoint_id} 可用！")
        print(f"🖼️  测试图像URL: {response.data[0].url}")
        return True
    except Exception as e:
        print(f"❌ 推理接入点 {endpoint_id} 测试失败: {e}")
        return False


def update_main_py(endpoint_id):
    """更新main.py中的模型ID"""
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换模型列表中的第一个模型
        old_line = '"doubao-seedream-3-0-t2i-250415",'
        new_line = f'"{endpoint_id}",  # 您的推理接入点ID'
        
        if old_line in content:
            content = content.replace(old_line, new_line)
            
            with open('main.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 已更新 main.py 中的模型ID为: {endpoint_id}")
            return True
        else:
            print("❌ 未找到需要替换的模型ID行")
            return False
    except Exception as e:
        print(f"❌ 更新main.py失败: {e}")
        return False


def main():
    print("🚀 豆包图像生成 - 推理接入点配置助手")
    print("=" * 50)
    
    # 检查API密钥
    if not check_api_key():
        return
    
    print("\n📋 配置步骤:")
    print("1. 访问火山方舟控制台: https://console.volcengine.com/ark")
    print("2. 进入 '模型推理' > '创建推理接入点'")
    print("3. 选择 'doubao-seedream-3.0-t2i' 模型")
    print("4. 创建接入点并获取接入点ID")
    print("5. 将接入点ID输入到下面")
    
    print("\n" + "=" * 50)
    
    while True:
        endpoint_id = input("🔗 请输入您的推理接入点ID (格式: ep-xxxxxxxxx-xxxxx): ").strip()
        
        if not endpoint_id:
            print("❌ 接入点ID不能为空")
            continue
        
        if not endpoint_id.startswith("ep-"):
            print("❌ 接入点ID格式不正确，应该以 'ep-' 开头")
            continue
        
        print(f"\n🧪 测试接入点: {endpoint_id}")
        
        if test_endpoint(endpoint_id):
            print("\n🎉 接入点测试成功！")
            
            # 询问是否更新main.py
            update = input("\n❓ 是否自动更新main.py中的模型ID? (y/n): ").strip().lower()
            
            if update in ['y', 'yes', '是']:
                if update_main_py(endpoint_id):
                    print("\n✅ 配置完成！现在可以运行 'uv run python main.py' 生成图像了")
                else:
                    print(f"\n💡 请手动将main.py中的模型ID替换为: {endpoint_id}")
            else:
                print(f"\n💡 请手动将main.py中的模型ID替换为: {endpoint_id}")
            
            break
        else:
            print("\n❌ 接入点测试失败")
            retry = input("🔄 是否重新输入接入点ID? (y/n): ").strip().lower()
            
            if retry not in ['y', 'yes', '是']:
                print("👋 配置已取消")
                break
    
    print("\n📚 更多帮助:")
    print("- 火山方舟文档: https://www.volcengine.com/docs/82379")
    print("- 图像生成API: https://www.volcengine.com/docs/82379/1541523")


if __name__ == "__main__":
    main()
