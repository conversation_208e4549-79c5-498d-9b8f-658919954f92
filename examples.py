#!/usr/bin/env python3
"""
豆包图像生成 - 使用示例
"""

import os
from volcenginesdkarkruntime import Ark

# 基础配置
API_KEY = os.environ.get("ARK_API_KEY")
MODEL = "doubao-seedream-3-0-t2i-250415"
BASE_URL = "https://ark.cn-beijing.volces.com/api/v3"


def generate_image(prompt, **params):
    """生成图像的通用函数"""
    client = Ark(base_url=BASE_URL, api_key=API_KEY)
    
    default_params = {
        "size": "1024x1024",
        "response_format": "url",
        "watermark": False,
        "guidance_scale": 7.5,
    }
    default_params.update(params)
    
    try:
        response = client.images.generate(
            model=MODEL,
            prompt=prompt,
            **default_params
        )
        return response.data[0].url
    except Exception as e:
        print(f"生成失败: {e}")
        return None


def example_basic():
    """基础示例"""
    print("🎨 基础图像生成")
    url = generate_image("一只可爱的橘猫")
    if url:
        print(f"✅ 生成成功: {url}")


def example_landscape():
    """风景图示例"""
    print("\n🏞️ 风景图生成")
    url = generate_image(
        "山水画风格的自然风景，水墨画质感",
        size="1152x864"  # 横向构图
    )
    if url:
        print(f"✅ 生成成功: {url}")


def example_portrait():
    """人像示例"""
    print("\n👤 人像生成")
    url = generate_image(
        "专业人像摄影，柔和光线，高质量",
        size="864x1152",  # 纵向构图
        guidance_scale=10.0  # 高引导强度
    )
    if url:
        print(f"✅ 生成成功: {url}")


def example_fixed_seed():
    """固定种子示例"""
    print("\n🌱 固定种子生成")
    seed = 42
    prompt = "未来城市夜景，霓虹灯闪烁"
    
    print(f"使用固定种子 {seed}，多次生成相同图像:")
    for i in range(2):
        url = generate_image(prompt, seed=seed)
        if url:
            print(f"✅ 第{i+1}次: {url}")


if __name__ == "__main__":
    if not API_KEY:
        print("❌ 请设置 ARK_API_KEY 环境变量")
        exit(1)
    
    print("🚀 豆包图像生成示例")
    print("=" * 40)
    
    # 运行示例
    example_basic()
    example_landscape()
    example_portrait()
    example_fixed_seed()
    
    print("\n💡 提示:")
    print("- 修改 config.py 调整默认参数")
    print("- 运行 'uv run python main.py' 使用主程序")
