import os
import time
from volcenginesdkarkruntime import Ark

# 配置参数
DEFAULT_PARAMS = {
    "size": "1024x1024",
    "response_format": "url",
    "watermark": False,
    "guidance_scale": 7.5,
}

DEFAULT_PROMPT = ```



```
MODELS = ["doubao-seedream-3-0-t2i-250415"]


def generate_image(client, prompt, **params):
    """生成图像"""
    # 合并参数
    final_params = DEFAULT_PARAMS.copy()
    final_params.update(params)
    final_params = {k: v for k, v in final_params.items() if v is not None}

    for model in MODELS:
        try:
            print(f"🔄 使用模型: {model}")
            print(f" 提示词: {prompt}")

            response = client.images.generate(
                model=model,
                prompt=prompt,
                **final_params
            )
            print(f"✅ 生成成功")
            return response, final_params
        except Exception as e:
            print(f"❌ 错误: {str(e)[:100]}...")
            continue

    return None, None


def main():
    # 检查API密钥
    api_key = os.environ.get("ARK_API_KEY")
    if not api_key:
        print("❌ 未找到 ARK_API_KEY 环境变量")
        print("💡 设置方法: export ARK_API_KEY='your_api_key'")
        return

    # 初始化客户端
    client = Ark(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=api_key,
    )

    # 生成图像
    print("🎨 正在生成图像...")
    start_time = time.time()

    # 可以在这里自定义参数，例如：
    # custom_params = {"seed": 42, "size": "1152x864"}
    custom_params = {}

    response, used_params = generate_image(client, DEFAULT_PROMPT, **custom_params)
    generation_time = time.time() - start_time

    if response:
        print(f"\n🎉 生成成功！耗时: {generation_time:.2f}秒")
        for i, image in enumerate(response.data):
            print(f"🖼️  图像 {i+1}: {image.url}")

        print(f"\n📋 使用参数: {used_params}")
    else:
        print(f"\n❌ 生成失败 (耗时: {generation_time:.2f}秒)")
        print("💡 请检查API密钥和网络连接")


if __name__ == "__main__":
    main()
