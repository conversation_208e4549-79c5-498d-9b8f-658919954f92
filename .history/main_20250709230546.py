import os
import time
# 通过 uv add 'volcengine-python-sdk[ark]' 安装方舟SDK
from volcenginesdkarkruntime import Ark

# 导入配置文件
try:
    from config import get_image_params, PROMPT_CONFIG
    USE_CONFIG_FILE = True
except ImportError:
    USE_CONFIG_FILE = False
    print("💡 未找到 config.py，使用内置默认参数")


def get_available_models():
    """获取可用的图像生成模型列表"""
    # 根据官方文档，这些是可能的模型名称
    return [
        "doubao-seedream-3-0-t2i-250415",  # 官方文档中的模型ID
        "ep-20250709000000-xxxxx",         # 需要替换为实际的推理接入点ID
    ]


def get_default_params():
    """获取默认的图像生成参数"""
    return {
        "size": "1024x1024",           # 图像尺寸
        "response_format": "url",       # 响应格式
        "seed": None,                   # 随机种子，None表示随机
        # "guidance_scale": 7.5,          # 引导强度 (1.0-20.0)
        "watermark": ,              # 是否添加水印
        # 注意: 'n' 参数在当前API版本中不支持
    }


def generate_image_with_fallback(client, prompt, **custom_params):
    """
    尝试使用不同的模型生成图像

    Args:
        client: Ark客户端
        prompt: 提示词
        **custom_params: 自定义参数，会覆盖默认参数
    """
    models = get_available_models()

    # 合并默认参数和自定义参数
    params = get_default_params()
    params.update(custom_params)

    # 移除None值的参数
    params = {k: v for k, v in params.items() if v is not None}

    for model in models:
        try:
            print(f"🔄 尝试使用模型: {model}")

            # 显示生成参数
            print(f"📋 生成参数:")
            print(f"   📝 提示词: {prompt}")
            for key, value in params.items():
                print(f"   {key}: {value}")

            response = client.images.generate(
                model=model,
                prompt=prompt,
                **params
            )
            print(f"✅ 成功使用模型: {model}")
            return response
        except Exception as e:
            error_msg = str(e)
            if "invalid model specified" in error_msg:
                print(f"❌ 模型 {model} 无效")
            elif "not found" in error_msg:
                print(f"❌ 模型 {model} 未找到")
            else:
                print(f"❌ 模型 {model} 错误: {error_msg[:100]}...")
            continue

    return None


def get_user_preferences():
    """获取用户自定义参数配置"""
    if USE_CONFIG_FILE:
        # 使用配置文件中的参数
        return get_image_params()
    else:
        # 使用内置默认参数
        return {
            # 基础参数
            "size": "1024x1024",           # 可选: "512x512", "768x768", "1024x1024", "1152x864", "864x1152"
            "response_format": "url",       # 固定为url

            # 高级参数
            "seed": 42,                    # 固定种子确保可重现，None为随机
            "guidance_scale": 7.5,         # 引导强度 (1.0-20.0)，越高越符合提示词
            "watermark": True,             # 是否添加水印
        }


def main():
    # 检查API密钥
    api_key = os.environ.get("ARK_API_KEY")
    if not api_key:
        print("❌ 错误: 未找到 ARK_API_KEY 环境变量")
        print("💡 请设置环境变量: export ARK_API_KEY='your_api_key_here'")
        return

    print(f"🔑 使用API密钥: {api_key[:8]}...{api_key[-4:]}")

    # 初始化Ark客户端
    client = Ark(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=api_key,
    )

    # 提示词配置
    if USE_CONFIG_FILE and "default_prompt" in PROMPT_CONFIG:
        prompt = PROMPT_CONFIG["default_prompt"]
    else:
        prompt = "鱼眼镜头，一只猫咪的头部，画面呈现出猫咪的五官因为拍摄方式扭曲的效果。"

    # 获取用户自定义参数
    custom_params = get_user_preferences()

    print("🎨 正在生成图像，请稍候...")
    print(f"📝 提示词: {prompt}")

    # 记录开始时间
    start_time = time.time()

    response = generate_image_with_fallback(client, prompt, **custom_params)

    # 计算生成时间
    generation_time = time.time() - start_time

    if response:
        print("\n🎉 图像生成成功！")
        for i, image in enumerate(response.data):
            print(f"🖼️  图像 {i+1}: {image.url}")
        print(f"📊 总共生成: {len(response.data)} 张图片")
        print(f"⏱️  生成耗时: {generation_time:.2f} 秒")

        # 显示参数信息
        print(f"\n📋 使用的参数:")
        merged_params = get_default_params()
        merged_params.update(custom_params)
        for key, value in merged_params.items():
            if value is not None:
                print(f"   {key}: {value}")

        # 显示参数说明
        print(f"\n💡 参数说明:")
        print(f"   - 修改 config.py 文件可以调整生成参数")
        print(f"   - 查看 advanced_example.py 了解更多用法")
        print(f"   - 运行 'uv run python advanced_example.py' 查看示例")
    else:
        print(f"\n❌ 所有模型都无法使用 (耗时: {generation_time:.2f} 秒)")
        print_troubleshooting_guide()


def print_troubleshooting_guide():
    """打印故障排除指南"""
    print("\n" + "="*60)
    print("🔧 故障排除指南")
    print("="*60)
    print("1. � 检查控制台设置:")
    print("   - 访问: https://console.volcengine.com/ark")
    print("   - 确保已开通图像生成服务")
    print("   - 在'模型推理'中创建推理接入点")
    print()
    print("2. 🔑 API密钥权限:")
    print("   - 确保API密钥有图像生成权限")
    print("   - 检查密钥是否过期")
    print()
    print("3. 🏗️  创建推理接入点:")
    print("   - 进入控制台 > 模型推理 > 创建推理接入点")
    print("   - 选择 doubao-seedream-3.0-t2i 模型")
    print("   - 获取接入点ID (格式: ep-xxxxxxxxx-xxxxx)")
    print("   - 将接入点ID替换到代码中的model参数")
    print()
    print("4. 🌐 网络连接:")
    print("   - 确保网络可以访问火山引擎API")
    print("   - 检查防火墙设置")
    print("="*60)


if __name__ == "__main__":
    main()
