"""
豆包图像生成 - 配置文件
"""

# 图像生成参数
IMAGE_PARAMS = {
    "size": "1024x1024",        # 图像尺寸: 512x512, 768x768, 1024x1024, 1152x864, 864x1152
    "watermark": False,         # 是否添加水印
    "guidance_scale": 7.5,      # 引导强度: 1.0-20.0
    "seed": None,               # 随机种子: None为随机，数字为固定
}

# 提示词配置
PROMPTS = {
    "default": "鱼眼镜头，一只猫咪的头部，画面呈现出猫咪的五官因为拍摄方式扭曲的效果。",
    "landscape": "山水画风格的自然风景，水墨画质感",
    "portrait": "专业人像摄影，柔和光线，高质量",
    "abstract": "抽象艺术，色彩丰富，现代风格",
}

# 提示词配置
PROMPT_CONFIG = {
    # 默认提示词
    "default_prompt": "鱼眼镜头，一只猫咪的头部，画面呈现出猫咪的五官因为拍摄方式扭曲的效果。",
    
    # 提示词模板 - 可以在这里定义常用的提示词
    "templates": {
        "cat_portrait": "一只{color}猫咪的特写肖像，{style}风格，{lighting}光线",
        "landscape": "{time}的{location}风景，{style}风格，{mood}氛围",
        "abstract": "抽象艺术，{colors}色调，{composition}构图",
        "character": "{description}的角色设计，{style}风格，全身像",
    },
    
    # 风格关键词
    "styles": [
        "水彩画", "油画", "素描", "数字艺术", "像素艺术",
        "赛博朋克", "蒸汽朋克", "极简主义", "超现实主义",
        "动漫风格", "写实风格", "卡通风格", "复古风格"
    ],
    
    # 光线关键词
    "lighting": [
        "柔和光线", "戏剧性光影", "黄金时光", "蓝调时光",
        "背光", "侧光", "顶光", "环境光", "霓虹灯光"
    ],
    
    # 情绪关键词
    "moods": [
        "宁静的", "神秘的", "温馨的", "激动人心的",
        "忧郁的", "欢快的", "史诗般的", "梦幻的"
    ]
}

# 模型配置
MODEL_CONFIG = {
    # 可用的模型列表
    "available_models": [
        "doubao-seedream-3-0-t2i-250415",  # 官方预置模型
        "ep-20250709000000-xxxxx",         # 替换为您的推理接入点ID
    ],
    
    # 默认使用的模型索引
    "default_model_index": 0,
}

# API配置
API_CONFIG = {
    # API基础URL
    "base_url": "https://ark.cn-beijing.volces.com/api/v3",
    
    # 超时设置（秒）
    "timeout": 60,
    
    # 重试次数
    "max_retries": 3,
}

# 输出配置
OUTPUT_CONFIG = {
    # 是否显示详细参数
    "show_parameters": True,
    
    # 是否显示生成时间
    "show_timing": True,
    
    # 是否保存生成记录
    "save_history": False,
    
    # 历史记录文件路径
    "history_file": "generation_history.json",
}


def get_config():
    """获取完整配置"""
    return {
        "image_generation": IMAGE_GENERATION_CONFIG,
        "prompt": PROMPT_CONFIG,
        "model": MODEL_CONFIG,
        "api": API_CONFIG,
        "output": OUTPUT_CONFIG,
    }


def get_image_params():
    """获取图像生成参数"""
    return IMAGE_GENERATION_CONFIG.copy()


def get_prompt_template(template_name, **kwargs):
    """
    获取格式化的提示词模板
    
    Args:
        template_name: 模板名称
        **kwargs: 模板参数
    
    Returns:
        格式化后的提示词
    """
    templates = PROMPT_CONFIG["templates"]
    if template_name not in templates:
        return PROMPT_CONFIG["default_prompt"]
    
    try:
        return templates[template_name].format(**kwargs)
    except KeyError as e:
        print(f"⚠️  模板参数缺失: {e}")
        return templates[template_name]


# 使用示例
if __name__ == "__main__":
    print("📋 当前配置:")
    print("=" * 40)
    
    config = get_config()
    
    print("🖼️  图像生成参数:")
    for key, value in config["image_generation"].items():
        print(f"   {key}: {value}")
    
    print("\n📝 提示词模板示例:")
    example_prompt = get_prompt_template(
        "cat_portrait",
        color="橘色",
        style="水彩画",
        lighting="柔和"
    )
    print(f"   {example_prompt}")
    
    print("\n💡 使用方法:")
    print("   1. 修改此文件中的参数")
    print("   2. 在 main.py 中导入: from config import get_image_params")
    print("   3. 使用: custom_params = get_image_params()")
