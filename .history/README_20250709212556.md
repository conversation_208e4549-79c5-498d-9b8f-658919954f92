# 豆包图像生成项目

这是一个使用豆包AI生成图像的Python项目，使用uv进行依赖管理。

## 项目结构

```
doubao-image/
├── .venv/              # 虚拟环境目录
├── main.py             # 主程序文件
├── doubao-image.py     # 原始示例文件
├── pyproject.toml      # 项目配置文件
├── uv.lock            # 依赖锁定文件
├── requirements.txt    # 传统依赖文件（保留用于参考）
├── .env               # 环境变量文件
└── README.md          # 项目说明文件
```

## 环境要求

- Python >= 3.12
- uv 包管理器

## 安装和设置

### 1. 安装uv

如果还没有安装uv，请先安装：

```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 2. 克隆项目并安装依赖

```bash
cd doubao-image
uv sync  # 安装所有依赖并创建虚拟环境
```

### 3. 配置API密钥

确保`.env`文件中包含您的豆包API密钥：

```
ARK_API_KEY=your_api_key_here
```

## 使用方法

### 运行主程序

```bash
uv run python main.py
```

### 使用uv的其他常用命令

```bash
# 添加新依赖
uv add package_name

# 移除依赖
uv remove package_name

# 更新依赖
uv lock --upgrade

# 运行Python脚本
uv run python script.py

# 激活虚拟环境
source .venv/bin/activate  # macOS/Linux
# 或
.venv\Scripts\activate     # Windows

# 查看已安装的包
uv pip list
```

## 项目特点

1. **现代化依赖管理**: 使用uv替代传统的pip+virtualenv，提供更快的依赖解析和安装
2. **锁定文件**: uv.lock确保在不同环境中的依赖一致性
3. **项目配置**: pyproject.toml包含项目元数据和依赖信息
4. **错误处理**: 包含适当的异常处理和用户友好的错误信息

## 故障排除

### 模型名称错误

如果遇到模型名称无效的错误，请：

1. 检查豆包API文档获取正确的模型名称
2. 确认您的API密钥有权限访问该模型
3. 联系API提供商获取可用模型列表

### 依赖问题

如果遇到依赖相关问题：

```bash
# 重新安装所有依赖
uv sync --reinstall

# 清理缓存
uv cache clean
```

## 开发

### 添加新功能

1. 使用`uv add`添加新依赖
2. 更新代码
3. 测试功能
4. 更新文档

### 项目维护

- 定期运行`uv lock --upgrade`更新依赖
- 保持pyproject.toml中的项目信息最新
- 遵循Python代码规范

## 许可证

请根据您的需要添加适当的许可证信息。
