import os
# 通过 uv add 'volcengine-python-sdk[ark]' 安装方舟SDK
from volcenginesdkarkruntime import Ark


def get_available_models():
    """获取可用的图像生成模型列表"""
    # 根据官方文档，这些是可能的模型名称
    return [
        "doubao-seedream-3-0-t2i-250415",  # 官方文档中的模型ID
        "ep-20250709000000-xxxxx",         # 需要替换为实际的推理接入点ID
    ]


def generate_image_with_fallback(client, prompt):
    """尝试使用不同的模型生成图像"""
    models = get_available_models()

    for model in models:
        try:
            print(f"🔄 尝试使用模型: {model}")
            response = client.images.generate(
                model=model,
                prompt=prompt,
                size="1024x1024",
                response_format="url"   
            )
            print(f"✅ 成功使用模型: {model}")
            return response
        except Exception as e:
            error_msg = str(e)
            if "invalid model specified" in error_msg:
                print(f"❌ 模型 {model} 无效")
            elif "not found" in error_msg:
                print(f"❌ 模型 {model} 未找到")
            else:
                print(f"❌ 模型 {model} 错误: {error_msg[:100]}...")
            continue

    return None


def main():
    # 检查API密钥
    api_key = os.environ.get("ARK_API_KEY")
    if not api_key:
        print("❌ 错误: 未找到 ARK_API_KEY 环境变量")
        print("💡 请设置环境变量: export ARK_API_KEY='your_api_key_here'")
        return

    print(f"🔑 使用API密钥: {api_key[:8]}...{api_key[-4:]}")

    # 初始化Ark客户端
    client = Ark(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=api_key,
    )

    prompt = "鱼眼镜头，一只猫咪的头部，画面呈现出猫咪的五官因为拍摄方式扭曲的效果。"

    print("🎨 正在生成图像，请稍候...")
    print(f"📝 提示词: {prompt}")

    response = generate_image_with_fallback(client, prompt)

    if response:
        print("\n🎉 图像生成成功！")
        print(f"🖼️  生成的图像URL: {response.data[0].url}")
        print(f"📊 响应数据: {len(response.data)} 张图片")
    else:
        print("\n❌ 所有模型都无法使用")
        print_troubleshooting_guide()


def print_troubleshooting_guide():
    """打印故障排除指南"""
    print("\n" + "="*60)
    print("🔧 故障排除指南")
    print("="*60)
    print("1. � 检查控制台设置:")
    print("   - 访问: https://console.volcengine.com/ark")
    print("   - 确保已开通图像生成服务")
    print("   - 在'模型推理'中创建推理接入点")
    print()
    print("2. 🔑 API密钥权限:")
    print("   - 确保API密钥有图像生成权限")
    print("   - 检查密钥是否过期")
    print()
    print("3. 🏗️  创建推理接入点:")
    print("   - 进入控制台 > 模型推理 > 创建推理接入点")
    print("   - 选择 doubao-seedream-3.0-t2i 模型")
    print("   - 获取接入点ID (格式: ep-xxxxxxxxx-xxxxx)")
    print("   - 将接入点ID替换到代码中的model参数")
    print()
    print("4. 🌐 网络连接:")
    print("   - 确保网络可以访问火山引擎API")
    print("   - 检查防火墙设置")
    print("="*60)


if __name__ == "__main__":
    main()
