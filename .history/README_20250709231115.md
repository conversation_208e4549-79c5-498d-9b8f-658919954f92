# 豆包图像生成项目

使用豆包AI生成图像的Python项目，基于uv包管理器。

## 快速开始

### 1. 安装依赖
```bash
uv sync
```

### 2. 配置API密钥
在`.env`文件中设置：
```
ARK_API_KEY=your_api_key_here
```

### 3. 运行程序
```bash
uv run python main.py
```

## 参数配置

编辑 `config.py` 文件自定义参数：

```python
IMAGE_PARAMS = {
    "size": "1024x1024",        # 图像尺寸
    "watermark": False,         # 是否添加水印
    "guidance_scale": 7.5,      # 引导强度 (1.0-20.0)
    "seed": None,               # 随机种子
}
```

### 可用参数

| 参数 | 说明 | 可选值 |
|------|------|--------|
| `size` | 图像尺寸 | `512x512`, `768x768`, `1024x1024`, `1152x864`, `864x1152` |
| `guidance_scale` | 引导强度 | 1.0-20.0 (推荐7.5) |
| `seed` | 随机种子 | 数字(固定) 或 None(随机) |
| `watermark` | 水印 | true/false |

### 使用uv的其他常用命令

```bash
# 添加新依赖
uv add package_name

# 移除依赖
uv remove package_name

# 更新依赖
uv lock --upgrade

# 运行Python脚本
uv run python script.py

# 激活虚拟环境
source .venv/bin/activate  # macOS/Linux
# 或
.venv\Scripts\activate     # Windows

# 查看已安装的包
uv pip list
```

## 项目特点

1. **现代化依赖管理**: 使用uv替代传统的pip+virtualenv，提供更快的依赖解析和安装
2. **锁定文件**: uv.lock确保在不同环境中的依赖一致性
3. **项目配置**: pyproject.toml包含项目元数据和依赖信息
4. **错误处理**: 包含适当的异常处理和用户友好的错误信息

## 故障排除

### 模型名称无效错误

如果遇到 `invalid model specified` 错误，通常是因为：

1. **未创建推理接入点**: 这是最常见的原因
   - 运行 `uv run python setup_endpoint.py` 配置接入点
   - 或手动在控制台创建推理接入点

2. **API密钥权限不足**:
   - 确认API密钥有图像生成权限
   - 检查密钥是否过期

3. **服务未开通**:
   - 在控制台确认已开通图像生成服务
   - 检查账户余额是否充足

### 依赖问题

如果遇到依赖相关问题：

```bash
# 重新安装所有依赖
uv sync --reinstall

# 清理缓存
uv cache clean
```

## 开发

### 添加新功能

1. 使用`uv add`添加新依赖
2. 更新代码
3. 测试功能
4. 更新文档

### 项目维护

- 定期运行`uv lock --upgrade`更新依赖
- 保持pyproject.toml中的项目信息最新
- 遵循Python代码规范

## 许可证

请根据您的需要添加适当的许可证信息。
